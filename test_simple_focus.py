"""
Test sederhana untuk memverifikasi implementasi focus metric
Tanpa dependency pytest

(c) 2025 Radhi<PERSON><PERSON>hi
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from analytics.focus_metric import FocusMetricCalculator, compute_focus
import time

def test_focus_metric_basic():
    """Test dasar untuk focus metric calculator"""
    print("🧪 Testing FocusMetricCalculator...")
    
    # Test inisialisasi
    calculator = FocusMetricCalculator(
        blink_window_sec=10,
        head_var_window=10,
        normal_blink_range=(12.0, 20.0)
    )
    
    assert calculator.blink_window_sec == 10
    assert calculator.head_var_window == 10
    print("✅ Inisialisasi berhasil")
    
    # Test penambahan data
    timestamp = time.time()
    head_pose = {'yaw': 5.0, 'pitch': -2.0, 'roll': 1.0}
    
    calculator.add_frame_data(0.3, head_pose, timestamp)
    assert len(calculator.head_pose_history) == 1
    print("✅ Penambahan data berhasil")
    
    # Test compute focus
    score = calculator.compute_focus(blink_rate=15.0, head_variance=50.0)
    assert 0.0 <= score <= 100.0
    print(f"✅ Compute focus berhasil: {score:.1f}")
    
    # Test fungsi standalone
    standalone_score = compute_focus(15.0, 50.0)
    assert 0.0 <= standalone_score <= 100.0
    print(f"✅ Fungsi standalone berhasil: {standalone_score:.1f}")
    
    print("🎉 Semua test focus metric berhasil!")

def test_scoring_logic():
    """Test logika scoring"""
    print("\n🧪 Testing scoring logic...")
    
    calculator = FocusMetricCalculator()
    
    # Test blink rate scoring
    normal_score = calculator._score_blink_rate(15.0)  # Normal
    low_score = calculator._score_blink_rate(5.0)      # Rendah
    high_score = calculator._score_blink_rate(40.0)    # Tinggi
    
    assert normal_score >= 80.0
    assert high_score <= 50.0
    print(f"✅ Blink rate scoring: Normal={normal_score:.1f}, Low={low_score:.1f}, High={high_score:.1f}")
    
    # Test head variance scoring
    stable_score = calculator._score_head_variance(10.0)    # Stabil
    unstable_score = calculator._score_head_variance(500.0) # Tidak stabil
    
    assert stable_score >= 85.0
    assert unstable_score <= 30.0
    print(f"✅ Head variance scoring: Stable={stable_score:.1f}, Unstable={unstable_score:.1f}")
    
    print("🎉 Semua test scoring logic berhasil!")

def test_focus_descriptions():
    """Test deskripsi level fokus"""
    print("\n🧪 Testing focus descriptions...")
    
    calculator = FocusMetricCalculator()
    
    descriptions = [
        (90.0, "Sangat Fokus"),
        (70.0, "Fokus Baik"),
        (50.0, "Fokus Sedang"),
        (30.0, "Kurang Fokus"),
        (10.0, "Tidak Fokus")
    ]
    
    for score, expected_keyword in descriptions:
        desc = calculator.get_focus_level_description(score)
        assert expected_keyword in desc
        print(f"✅ Score {score}: {desc}")
    
    print("🎉 Semua test descriptions berhasil!")

if __name__ == "__main__":
    try:
        test_focus_metric_basic()
        test_scoring_logic()
        test_focus_descriptions()
        print("\n🎊 SEMUA TEST BERHASIL! 🎊")
        print("Real-Time Focus Monitor siap digunakan!")
        
    except Exception as e:
        print(f"\n❌ TEST GAGAL: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)