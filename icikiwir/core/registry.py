"""
Plugin Registry untuk ICikiwir
Sistem singleton untuk discover, register, dan manage plugins

(c) 2025 Radhitya Guntoro Adhi
"""

import importlib.metadata
from typing import Dict, Type, List, Union, Any, Iterator
import logging
from functools import wraps

from .protocols import VisionPlugin, AnalyticsPlugin, InterventionPlugin


class PluginRegistry:
    """
    Central registry untuk mengelola semua plugins dalam sistem ICikiwir
    Menggunakan pattern singleton untuk memastikan satu instance global
    """
    
    _instance = None
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            self._vision_plugins: Dict[str, Type[VisionPlugin]] = {}
            self._analytics_plugins: Dict[str, Type[AnalyticsPlugin]] = {}
            self._intervention_plugins: Dict[str, Type[InterventionPlugin]] = {}
            self._instances: Dict[str, Union[VisionPlugin, AnalyticsPlugin, InterventionPlugin]] = {}
            self.logger = logging.getLogger(__name__)
            PluginRegistry._initialized = True
    
    def discover(self, entry_point_group: str = "icikiwir.plugins") -> int:
        """
        Auto-discover plugins melalui entry points
        
        Args:
            entry_point_group: Nama group entry point untuk dicari
            
        Returns:
            int: Jumlah plugin yang berhasil di-load
        """
        loaded_count = 0
        
        try:
            # Gunakan importlib.metadata untuk Python 3.10+
            entry_points = importlib.metadata.entry_points()
            
            # Filter berdasarkan group
            if hasattr(entry_points, 'select'):
                # Python 3.10+ style
                plugin_entries = entry_points.select(group=entry_point_group)
            else:
                # Fallback untuk versi lama
                plugin_entries = entry_points.get(entry_point_group, [])
            
            for entry_point in plugin_entries:
                try:
                    plugin_class = entry_point.load()
                    self.register(entry_point.name, plugin_class)
                    loaded_count += 1
                    self.logger.info(f"Plugin berhasil dimuat: {entry_point.name}")
                except Exception as e:
                    self.logger.error(f"Gagal memuat plugin {entry_point.name}: {e}")
                    
        except Exception as e:
            self.logger.error(f"Error saat discover plugins: {e}")
        
        self.logger.info(f"Total {loaded_count} plugin berhasil dimuat dari entry points")
        return loaded_count
    
    def register(self, name: str, plugin_cls: Type, category: str = None) -> None:
        """
        Register plugin class secara manual
        
        Args:
            name: Nama unik plugin
            plugin_cls: Class plugin yang akan diregister
            category: Kategori plugin ('vision', 'analytics', 'intervention')
                     Jika None, akan auto-detect berdasarkan interface
        """
        if category is None:
            category = self._detect_plugin_category(plugin_cls)
        
        if category == "vision":
            self._vision_plugins[name] = plugin_cls
        elif category == "analytics":
            self._analytics_plugins[name] = plugin_cls
        elif category == "intervention":
            self._intervention_plugins[name] = plugin_cls
        else:
            raise ValueError(f"Kategori plugin tidak dikenal: {category}")
        
        self.logger.info(f"Plugin {name} berhasil diregister dalam kategori {category}")
    
    def _detect_plugin_category(self, plugin_cls: Type) -> str:
        """
        Auto-detect kategori plugin berdasarkan method yang dimiliki
        
        Args:
            plugin_cls: Class plugin
            
        Returns:
            str: Kategori plugin
        """
        if hasattr(plugin_cls, 'process_frame'):
            return "vision"
        elif hasattr(plugin_cls, 'analyze_metrics'):
            return "analytics"
        elif hasattr(plugin_cls, 'trigger_intervention'):
            return "intervention"
        else:
            raise ValueError(f"Plugin class {plugin_cls.__name__} tidak mengimplementasi interface yang dikenal")
    
    def get_plugins(self, category: str) -> Iterator[Type]:
        """
        Mendapatkan iterator plugin berdasarkan kategori
        
        Args:
            category: Kategori plugin ('vision', 'analytics', 'intervention')
            
        Yields:
            Type: Plugin class
        """
        if category == "vision":
            for plugin_cls in self._vision_plugins.values():
                yield plugin_cls
        elif category == "analytics":
            for plugin_cls in self._analytics_plugins.values():
                yield plugin_cls
        elif category == "intervention":
            for plugin_cls in self._intervention_plugins.values():
                yield plugin_cls
        else:
            raise ValueError(f"Kategori tidak valid: {category}")
    
    def create_instance(self, name: str, config: Dict[str, Any] = None) -> Union[VisionPlugin, AnalyticsPlugin, InterventionPlugin]:
        """
        Membuat instance plugin yang sudah dikonfigurasi
        
        Args:
            name: Nama plugin
            config: Konfigurasi plugin (optional)
            
        Returns:
            Instance plugin yang sudah dikonfigurasi
        """
        plugin_cls = None
        
        # Cari plugin di semua kategori
        if name in self._vision_plugins:
            plugin_cls = self._vision_plugins[name]
        elif name in self._analytics_plugins:
            plugin_cls = self._analytics_plugins[name]
        elif name in self._intervention_plugins:
            plugin_cls = self._intervention_plugins[name]
        
        if plugin_cls is None:
            raise ValueError(f"Plugin '{name}' tidak ditemukan")
        
        # Buat instance
        instance = plugin_cls()
        
        # Terapkan konfigurasi jika ada
        if config is not None:
            instance.configure(config)
        
        # Simpan instance untuk reuse
        self._instances[name] = instance
        
        self.logger.info(f"Instance plugin {name} berhasil dibuat")
        return instance
    
    def get_instance(self, name: str) -> Union[VisionPlugin, AnalyticsPlugin, InterventionPlugin]:
        """
        Mendapatkan instance plugin yang sudah dibuat
        
        Args:
            name: Nama plugin
            
        Returns:
            Instance plugin
        """
        if name not in self._instances:
            raise ValueError(f"Instance plugin '{name}' belum dibuat")
        
        return self._instances[name]
    
    def list_plugins(self) -> Dict[str, List[str]]:
        """
        Mendapatkan daftar semua plugin yang terdaftar
        
        Returns:
            Dict dengan kategori sebagai key dan list nama plugin sebagai value
        """
        return {
            "vision": list(self._vision_plugins.keys()),
            "analytics": list(self._analytics_plugins.keys()),
            "intervention": list(self._intervention_plugins.keys())
        }
    
    def get_plugin_info(self, name: str) -> Dict[str, Any]:
        """
        Mendapatkan informasi detail plugin
        
        Args:
            name: Nama plugin
            
        Returns:
            Dict dengan informasi plugin
        """
        plugin_cls = None
        category = None
        
        if name in self._vision_plugins:
            plugin_cls = self._vision_plugins[name]
            category = "vision"
        elif name in self._analytics_plugins:
            plugin_cls = self._analytics_plugins[name]
            category = "analytics"
        elif name in self._intervention_plugins:
            plugin_cls = self._intervention_plugins[name]
            category = "intervention"
        
        if plugin_cls is None:
            raise ValueError(f"Plugin '{name}' tidak ditemukan")
        
        # Buat temporary instance untuk mendapatkan info
        temp_instance = plugin_cls()
        
        return {
            "name": temp_instance.name,
            "version": temp_instance.version,
            "description": temp_instance.description,
            "category": category,
            "class_name": plugin_cls.__name__,
            "module": plugin_cls.__module__
        }
    
    def cleanup_all(self) -> None:
        """
        Cleanup semua plugin instances
        """
        for name, instance in self._instances.items():
            try:
                instance.cleanup()
                self.logger.info(f"Plugin {name} berhasil di-cleanup")
            except Exception as e:
                self.logger.error(f"Error cleanup plugin {name}: {e}")
        
        self._instances.clear()
    
    def unregister(self, name: str) -> None:
        """
        Unregister plugin
        
        Args:
            name: Nama plugin yang akan di-unregister
        """
        # Cleanup instance jika ada
        if name in self._instances:
            try:
                self._instances[name].cleanup()
                del self._instances[name]
            except Exception as e:
                self.logger.error(f"Error cleanup instance {name}: {e}")
        
        # Remove dari registry
        removed = False
        if name in self._vision_plugins:
            del self._vision_plugins[name]
            removed = True
        elif name in self._analytics_plugins:
            del self._analytics_plugins[name]
            removed = True
        elif name in self._intervention_plugins:
            del self._intervention_plugins[name]
            removed = True
        
        if removed:
            self.logger.info(f"Plugin {name} berhasil di-unregister")
        else:
            self.logger.warning(f"Plugin {name} tidak ditemukan untuk di-unregister")


def register_plugin(category: str):
    """
    Decorator untuk auto-register plugin class
    
    Args:
        category: Kategori plugin ('vision', 'analytics', 'intervention')
    
    Usage:
        @register_plugin("vision")
        class MyVisionPlugin:
            ...
    """
    def decorator(plugin_cls):
        registry.register(plugin_cls.__name__.lower(), plugin_cls, category)
        return plugin_cls
    return decorator


# Global singleton instance
registry = PluginRegistry()