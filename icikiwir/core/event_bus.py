"""
Event Bus untuk ICikiwir
Sistem publish-subscribe menggunakan asyncio untuk komunikasi antar modul

(c) 2025 Radhitya Guntoro Adhi
"""

import asyncio
from typing import Dict, List, Callable, Any, Optional, TypeVar, Generic
from dataclasses import dataclass, field
from datetime import datetime
import logging
from pydantic import BaseModel
import uuid


class Event(BaseModel):
    """
    Base class untuk semua event dalam sistem ICikiwir
    """
    event_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    event_type: str
    timestamp: datetime = field(default_factory=datetime.now)
    source: str
    data: Dict[str, Any] = field(default_factory=dict)
    
    class Config:
        arbitrary_types_allowed = True


class VisionEvent(Event):
    """Event untuk hasil pemrosesan vision"""
    event_type: str = "vision"


class AnalyticsEvent(Event):
    """Event untuk hasil analisis"""
    event_type: str = "analytics"


class InterventionEvent(Event):
    """Event untuk trigger intervensi"""
    event_type: str = "intervention"


class FocusMetricEvent(Event):
    """Event untuk focus metric data"""
    event_type: str = "focus_metric"


class SystemEvent(Event):
    """Event untuk sistem (startup, shutdown, error)"""
    event_type: str = "system"


EventType = TypeVar('EventType', bound=Event)
EventHandler = Callable[[EventType], None]
AsyncEventHandler = Callable[[EventType], asyncio.Future]


class EventBus:
    """
    Event bus untuk komunikasi asinkron antar komponen
    Menggunakan pattern publish-subscribe dengan asyncio.Queue
    """
    
    def __init__(self, max_queue_size: int = 1000):
        """
        Inisialisasi EventBus
        
        Args:
            max_queue_size: Maksimal ukuran queue untuk setiap subscriber
        """
        self._subscribers: Dict[str, List[asyncio.Queue]] = {}
        self._handlers: Dict[str, List[Callable]] = {}
        self._max_queue_size = max_queue_size
        self._running = False
        self._tasks: List[asyncio.Task] = []
        self.logger = logging.getLogger(__name__)
    
    async def start(self) -> None:
        """Memulai event bus"""
        if self._running:
            return
        
        self._running = True
        self.logger.info("Event bus dimulai")
    
    async def stop(self) -> None:
        """Menghentikan event bus dan cleanup"""
        if not self._running:
            return
        
        self._running = False
        
        # Cancel semua tasks
        for task in self._tasks:
            if not task.done():
                task.cancel()
        
        # Tunggu semua tasks selesai
        if self._tasks:
            await asyncio.gather(*self._tasks, return_exceptions=True)
        
        self._tasks.clear()
        self.logger.info("Event bus dihentikan")
    
    def subscribe(self, event_type: str, handler: Callable[[Event], None]) -> str:
        """
        Subscribe ke event type tertentu dengan handler function
        
        Args:
            event_type: Tipe event yang ingin di-subscribe
            handler: Function yang akan dipanggil saat event diterima
            
        Returns:
            str: Subscription ID untuk unsubscribe
        """
        if event_type not in self._handlers:
            self._handlers[event_type] = []
        
        self._handlers[event_type].append(handler)
        subscription_id = f"{event_type}_{len(self._handlers[event_type])}"
        
        self.logger.info(f"Handler berhasil subscribe ke event '{event_type}'")
        return subscription_id
    
    def subscribe_async(self, event_type: str) -> asyncio.Queue:
        """
        Subscribe ke event type dengan asyncio.Queue
        
        Args:
            event_type: Tipe event yang ingin di-subscribe
            
        Returns:
            asyncio.Queue: Queue untuk menerima event
        """
        if event_type not in self._subscribers:
            self._subscribers[event_type] = []
        
        queue = asyncio.Queue(maxsize=self._max_queue_size)
        self._subscribers[event_type].append(queue)
        
        self.logger.info(f"Queue berhasil subscribe ke event '{event_type}'")
        return queue
    
    async def publish(self, event: Event) -> None:
        """
        Publish event ke semua subscriber
        
        Args:
            event: Event yang akan dipublish
        """
        if not self._running:
            self.logger.warning("Event bus belum dimulai, event diabaikan")
            return
        
        event_type = event.event_type
        
        # Kirim ke handler functions
        if event_type in self._handlers:
            for handler in self._handlers[event_type]:
                try:
                    if asyncio.iscoroutinefunction(handler):
                        # Async handler
                        task = asyncio.create_task(handler(event))
                        self._tasks.append(task)
                    else:
                        # Sync handler - jalankan di thread pool
                        loop = asyncio.get_event_loop()
                        task = loop.run_in_executor(None, handler, event)
                        self._tasks.append(asyncio.create_task(asyncio.wrap_future(task)))
                except Exception as e:
                    self.logger.error(f"Error menjalankan handler untuk event {event_type}: {e}")
        
        # Kirim ke queue subscribers
        if event_type in self._subscribers:
            for queue in self._subscribers[event_type]:
                try:
                    if queue.full():
                        # Jika queue penuh, buang event lama
                        try:
                            queue.get_nowait()
                        except asyncio.QueueEmpty:
                            pass
                    
                    await queue.put(event)
                except Exception as e:
                    self.logger.error(f"Error mengirim event ke queue: {e}")
        
        self.logger.debug(f"Event {event_type} berhasil dipublish (ID: {event.event_id})")
    
    def publish_sync(self, event: Event) -> None:
        """
        Publish event secara synchronous (untuk compatibility)
        
        Args:
            event: Event yang akan dipublish
        """
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # Jika sudah ada event loop, buat task
                asyncio.create_task(self.publish(event))
            else:
                # Jika belum ada event loop, jalankan
                loop.run_until_complete(self.publish(event))
        except RuntimeError:
            # Jika tidak ada event loop, buat baru
            asyncio.run(self.publish(event))
    
    def unsubscribe(self, event_type: str, handler: Callable = None, queue: asyncio.Queue = None) -> bool:
        """
        Unsubscribe dari event type
        
        Args:
            event_type: Tipe event
            handler: Handler function yang ingin di-unsubscribe (optional)
            queue: Queue yang ingin di-unsubscribe (optional)
            
        Returns:
            bool: True jika berhasil unsubscribe
        """
        success = False
        
        if handler and event_type in self._handlers:
            try:
                self._handlers[event_type].remove(handler)
                success = True
                self.logger.info(f"Handler berhasil unsubscribe dari event '{event_type}'")
            except ValueError:
                self.logger.warning(f"Handler tidak ditemukan untuk event '{event_type}'")
        
        if queue and event_type in self._subscribers:
            try:
                self._subscribers[event_type].remove(queue)
                success = True
                self.logger.info(f"Queue berhasil unsubscribe dari event '{event_type}'")
            except ValueError:
                self.logger.warning(f"Queue tidak ditemukan untuk event '{event_type}'")
        
        return success
    
    def get_subscriber_count(self, event_type: str) -> int:
        """
        Mendapatkan jumlah subscriber untuk event type
        
        Args:
            event_type: Tipe event
            
        Returns:
            int: Jumlah subscriber
        """
        handler_count = len(self._handlers.get(event_type, []))
        queue_count = len(self._subscribers.get(event_type, []))
        return handler_count + queue_count
    
    def get_event_types(self) -> List[str]:
        """
        Mendapatkan daftar semua event type yang memiliki subscriber
        
        Returns:
            List[str]: Daftar event type
        """
        event_types = set()
        event_types.update(self._handlers.keys())
        event_types.update(self._subscribers.keys())
        return list(event_types)
    
    def clear_all_subscribers(self) -> None:
        """Menghapus semua subscriber"""
        self._handlers.clear()
        self._subscribers.clear()
        self.logger.info("Semua subscriber berhasil dihapus")
    
    async def wait_for_event(self, event_type: str, timeout: Optional[float] = None) -> Optional[Event]:
        """
        Menunggu event tertentu dengan timeout
        
        Args:
            event_type: Tipe event yang ditunggu
            timeout: Timeout dalam detik (None = tanpa timeout)
            
        Returns:
            Event jika diterima, None jika timeout
        """
        queue = self.subscribe_async(event_type)
        
        try:
            if timeout:
                event = await asyncio.wait_for(queue.get(), timeout=timeout)
            else:
                event = await queue.get()
            
            return event
        except asyncio.TimeoutError:
            self.logger.warning(f"Timeout menunggu event '{event_type}'")
            return None
        finally:
            # Cleanup subscription
            self.unsubscribe(event_type, queue=queue)


# Global singleton instance
event_bus = EventBus()


# Convenience functions untuk kemudahan penggunaan
async def publish_vision_event(source: str, data: Dict[str, Any]) -> None:
    """Publish vision event"""
    event = VisionEvent(source=source, data=data)
    await event_bus.publish(event)


async def publish_analytics_event(source: str, data: Dict[str, Any]) -> None:
    """Publish analytics event"""
    event = AnalyticsEvent(source=source, data=data)
    await event_bus.publish(event)


async def publish_intervention_event(source: str, data: Dict[str, Any]) -> None:
    """Publish intervention event"""
    event = InterventionEvent(source=source, data=data)
    await event_bus.publish(event)


async def publish_focus_metric_event(source: str, data: Dict[str, Any]) -> None:
    """Publish focus metric event"""
    event = FocusMetricEvent(source=source, data=data)
    await event_bus.publish(event)


async def publish_system_event(source: str, data: Dict[str, Any]) -> None:
    """Publish system event"""
    event = SystemEvent(source=source, data=data)
    await event_bus.publish(event)


def subscribe_to_vision_events(handler: Callable[[VisionEvent], None]) -> str:
    """Subscribe ke vision events"""
    return event_bus.subscribe("vision", handler)


def subscribe_to_analytics_events(handler: Callable[[AnalyticsEvent], None]) -> str:
    """Subscribe ke analytics events"""
    return event_bus.subscribe("analytics", handler)


def subscribe_to_intervention_events(handler: Callable[[InterventionEvent], None]) -> str:
    """Subscribe ke intervention events"""
    return event_bus.subscribe("intervention", handler)


def subscribe_to_system_events(handler: Callable[[SystemEvent], None]) -> str:
    """Subscribe ke system events"""
    return event_bus.subscribe("system", handler)