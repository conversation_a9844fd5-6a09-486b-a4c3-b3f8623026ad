"""
Protocol Interface untuk Plugin System ICikiwir
Definisi kontrak interface untuk Vision, Analytics, dan Intervention plugins

(c) 2025 Radhitya Guntoro Adhi
"""

from typing import Protocol, Dict, Any, Optional
from abc import abstractmethod
import numpy as np


class VisionPlugin(Protocol):
    """
    Interface untuk vision processing plugins
    Digunakan untuk memproses frame video dan mengekstrak fitur visual
    """
    
    @property
    @abstractmethod
    def name(self) -> str:
        """Nama unik plugin (contoh: 'mediapipe_face_tracker')"""
        ...
    
    @property
    @abstractmethod
    def version(self) -> str:
        """Versi plugin menggunakan semantic versioning (contoh: '1.0.0')"""
        ...
    
    @property
    @abstractmethod
    def description(self) -> str:
        """Deskripsi singkat fungsi plugin dalam bahasa Indonesia"""
        ...
    
    @abstractmethod
    def process_frame(self, frame: np.ndarray) -> Dict[str, Any]:
        """
        Memproses single video frame dan mengekstrak fitur
        
        Args:
            frame: RGB image array dengan shape (H, W, 3)
            
        Returns:
            Dict dengan keys wajib:
            - face_detected: bool - apakah wajah terdeteksi
            - metrics: Dict[str, float] - nilai metrik (ear, head_pose, dll)
            - confidence: float - tingkat kepercayaan deteksi (0.0-1.0)
            - annotations: Optional[np.ndarray] - frame dengan overlay visual
            - metadata: Dict[str, Any] - informasi tambahan
        """
        ...
    
    @abstractmethod
    def get_config_schema(self) -> Dict[str, Any]:
        """
        Mengembalikan JSON Schema untuk validasi konfigurasi plugin
        
        Returns:
            Dict: JSON Schema specification
        """
        ...
    
    @abstractmethod
    def configure(self, config: Dict[str, Any]) -> None:
        """
        Menerapkan konfigurasi ke plugin
        
        Args:
            config: Dictionary konfigurasi yang sudah divalidasi
        """
        ...
    
    @abstractmethod
    def cleanup(self) -> None:
        """Membersihkan resources saat plugin di-unload"""
        ...


class AnalyticsPlugin(Protocol):
    """
    Interface untuk analytics processing plugins
    Digunakan untuk menganalisis metrik dan menghasilkan insights
    """
    
    @property
    @abstractmethod
    def name(self) -> str:
        """Nama unik plugin (contoh: 'focus_score_analyzer')"""
        ...
    
    @property
    @abstractmethod
    def version(self) -> str:
        """Versi plugin menggunakan semantic versioning"""
        ...
    
    @property
    @abstractmethod
    def description(self) -> str:
        """Deskripsi singkat fungsi plugin dalam bahasa Indonesia"""
        ...
    
    @abstractmethod
    def analyze_metrics(self, metrics: Dict[str, Any]) -> Dict[str, Any]:
        """
        Menganalisis raw metrics dan menghasilkan insights
        
        Args:
            metrics: Raw metrics dari vision plugins dan sumber lain
            
        Returns:
            Dict dengan keys:
            - focus_score: float - skor fokus (0.0-1.0)
            - attention_level: str - level atensi ('low', 'medium', 'high')
            - insights: List[str] - daftar insight dalam bahasa Indonesia
            - recommendations: List[str] - rekomendasi aksi
            - metadata: Dict[str, Any] - data tambahan
        """
        ...
    
    @abstractmethod
    def get_config_schema(self) -> Dict[str, Any]:
        """Mengembalikan JSON Schema untuk validasi konfigurasi"""
        ...
    
    @abstractmethod
    def configure(self, config: Dict[str, Any]) -> None:
        """Menerapkan konfigurasi ke plugin"""
        ...
    
    @abstractmethod
    def cleanup(self) -> None:
        """Membersihkan resources saat plugin di-unload"""
        ...


class InterventionPlugin(Protocol):
    """
    Interface untuk intervention plugins
    Digunakan untuk memberikan feedback dan intervensi kepada user
    """
    
    @property
    @abstractmethod
    def name(self) -> str:
        """Nama unik plugin (contoh: 'tts_feedback_engine')"""
        ...
    
    @property
    @abstractmethod
    def version(self) -> str:
        """Versi plugin menggunakan semantic versioning"""
        ...
    
    @property
    @abstractmethod
    def description(self) -> str:
        """Deskripsi singkat fungsi plugin dalam bahasa Indonesia"""
        ...
    
    @abstractmethod
    def trigger_intervention(self, context: Dict[str, Any]) -> bool:
        """
        Memicu intervensi berdasarkan context yang diberikan
        
        Args:
            context: Context data untuk decision making dengan keys:
            - focus_score: float - skor fokus saat ini
            - attention_level: str - level atensi
            - session_duration: int - durasi sesi dalam menit
            - last_intervention: Optional[float] - timestamp intervensi terakhir
            - user_preferences: Dict[str, Any] - preferensi user
            
        Returns:
            bool: True jika intervensi berhasil dipicu, False jika tidak
        """
        ...
    
    @abstractmethod
    def is_available(self) -> bool:
        """
        Mengecek apakah plugin siap untuk digunakan
        
        Returns:
            bool: True jika plugin tersedia dan siap
        """
        ...
    
    @abstractmethod
    def get_config_schema(self) -> Dict[str, Any]:
        """Mengembalikan JSON Schema untuk validasi konfigurasi"""
        ...
    
    @abstractmethod
    def configure(self, config: Dict[str, Any]) -> None:
        """Menerapkan konfigurasi ke plugin"""
        ...
    
    @abstractmethod
    def cleanup(self) -> None:
        """Membersihkan resources saat plugin di-unload"""
        ...