"""
ICikiwir CLI - Command Line Interface
Tool untuk mengelola plugin dan konfigurasi ICikiwir

(c) 2025 Radhitya Guntoro Adhi
"""

import typer
from typing import Optional
import json
from rich.console import Console
from rich.table import Table
from rich import print as rprint

# Import core modules
try:
    from icikiwir.core import registry, event_bus
    from src import initialize_icikiwir
except ImportError as e:
    typer.echo(f"❌ Error import: {e}")
    typer.echo("Pastikan ICikiwir sudah terinstall dengan benar")
    raise typer.Exit(1)

app = typer.Typer(
    name="icikiwir",
    help="ICikiwir - Cognitive-Aware Study Companion CLI",
    add_completion=False
)
console = Console()


@app.command("list-plugins")
def list_plugins(
    category: Optional[str] = typer.Option(
        None, 
        "--category", 
        "-c",
        help="Filter berdasarkan kategori (vision, analytics, intervention)"
    ),
    detailed: bool = typer.Option(
        False,
        "--detailed",
        "-d", 
        help="Tampilkan informasi detail plugin"
    )
):
    """
    Menampilkan daftar plugin yang terdaftar dalam sistem
    """
    try:
        # Pastikan registry sudah diinisialisasi
        plugin_count = registry.discover()
        
        plugins = registry.list_plugins()
        
        if not any(plugins.values()):
            rprint("❌ Tidak ada plugin yang terdaftar")
            return
        
        # Filter berdasarkan kategori jika diminta
        if category:
            if category not in plugins:
                rprint(f"❌ Kategori '{category}' tidak valid. Pilihan: vision, analytics, intervention")
                raise typer.Exit(1)
            plugins = {category: plugins[category]}
        
        # Tampilkan dalam tabel
        table = Table(title="🔌 Plugin ICikiwir yang Terdaftar")
        
        if detailed:
            table.add_column("Kategori", style="cyan")
            table.add_column("Nama", style="green")
            table.add_column("Versi", style="yellow")
            table.add_column("Deskripsi", style="white")
            
            for cat, plugin_names in plugins.items():
                for plugin_name in plugin_names:
                    try:
                        info = registry.get_plugin_info(plugin_name)
                        table.add_row(
                            cat,
                            info['name'],
                            info['version'],
                            info['description'][:50] + "..." if len(info['description']) > 50 else info['description']
                        )
                    except Exception as e:
                        table.add_row(cat, plugin_name, "❌", f"Error: {e}")
        else:
            table.add_column("Kategori", style="cyan")
            table.add_column("Jumlah Plugin", style="green")
            table.add_column("Nama Plugin", style="white")
            
            for cat, plugin_names in plugins.items():
                if plugin_names:
                    table.add_row(
                        cat,
                        str(len(plugin_names)),
                        ", ".join(plugin_names)
                    )
        
        console.print(table)
        
        # Summary
        total_plugins = sum(len(names) for names in plugins.values())
        rprint(f"\n✅ Total: {total_plugins} plugin terdaftar")
        
    except Exception as e:
        rprint(f"❌ Error: {e}")
        raise typer.Exit(1)


@app.command("plugin-info")
def plugin_info(
    name: str = typer.Argument(..., help="Nama plugin yang ingin dilihat detailnya")
):
    """
    Menampilkan informasi detail plugin tertentu
    """
    try:
        registry.discover()
        info = registry.get_plugin_info(name)
        
        rprint(f"\n🔌 [bold green]Plugin: {info['name']}[/bold green]")
        rprint(f"📦 Versi: {info['version']}")
        rprint(f"📂 Kategori: {info['category']}")
        rprint(f"🏷️  Class: {info['class_name']}")
        rprint(f"📁 Module: {info['module']}")
        rprint(f"📝 Deskripsi:")
        rprint(f"   {info['description']}")
        
        # Tampilkan config schema jika ada
        try:
            temp_instance = registry.create_instance(name)
            schema = temp_instance.get_config_schema()
            
            rprint(f"\n⚙️  [bold]Konfigurasi yang Didukung:[/bold]")
            if 'properties' in schema:
                for prop, details in schema['properties'].items():
                    prop_type = details.get('type', 'unknown')
                    default = details.get('default', 'N/A')
                    desc = details.get('description', 'Tidak ada deskripsi')
                    rprint(f"   • {prop} ({prop_type}): {desc}")
                    rprint(f"     Default: {default}")
            
            # Cleanup instance
            temp_instance.cleanup()
            
        except Exception as e:
            rprint(f"⚠️  Tidak dapat memuat config schema: {e}")
        
    except Exception as e:
        rprint(f"❌ Plugin '{name}' tidak ditemukan atau error: {e}")
        raise typer.Exit(1)


@app.command("test-plugin")
def test_plugin(
    name: str = typer.Argument(..., help="Nama plugin yang akan ditest"),
    config_file: Optional[str] = typer.Option(
        None,
        "--config",
        "-c",
        help="Path ke file konfigurasi JSON"
    )
):
    """
    Test plugin dengan konfigurasi tertentu
    """
    try:
        registry.discover()
        
        # Load konfigurasi jika ada
        config = {}
        if config_file:
            with open(config_file, 'r') as f:
                config = json.load(f)
            rprint(f"📄 Menggunakan konfigurasi dari: {config_file}")
        
        # Buat instance plugin
        rprint(f"🔧 Membuat instance plugin '{name}'...")
        instance = registry.create_instance(name, config)
        
        rprint(f"✅ Plugin '{name}' berhasil dibuat dan dikonfigurasi")
        rprint(f"📦 Versi: {instance.version}")
        rprint(f"📝 Deskripsi: {instance.description}")
        
        # Test basic functionality berdasarkan kategori
        info = registry.get_plugin_info(name)
        category = info['category']
        
        if category == "vision":
            rprint("🎥 Testing vision plugin...")
            # Buat dummy frame untuk test
            import numpy as np
            dummy_frame = np.zeros((480, 640, 3), dtype=np.uint8)
            result = instance.process_frame(dummy_frame)
            rprint(f"   Hasil test: {list(result.keys())}")
            
        elif category == "analytics":
            rprint("📊 Testing analytics plugin...")
            dummy_metrics = {"test_metric": 0.5}
            result = instance.analyze_metrics(dummy_metrics)
            rprint(f"   Hasil test: {list(result.keys())}")
            
        elif category == "intervention":
            rprint("🔔 Testing intervention plugin...")
            dummy_context = {"focus_score": 0.3, "attention_level": "low"}
            available = instance.is_available()
            rprint(f"   Plugin tersedia: {available}")
            if available:
                result = instance.trigger_intervention(dummy_context)
                rprint(f"   Intervention triggered: {result}")
        
        # Cleanup
        instance.cleanup()
        rprint("🧹 Plugin berhasil di-cleanup")
        
    except Exception as e:
        rprint(f"❌ Error testing plugin: {e}")
        raise typer.Exit(1)


@app.command("version")
def version():
    """
    Menampilkan versi ICikiwir
    """
    try:
        from src import __version__, __author__
        rprint(f"🎯 ICikiwir v{__version__}")
        rprint(f"👨‍💻 Dibuat oleh: {__author__}")
        rprint("🔗 https://github.com/radhityaguntoro/icikiwir")
    except ImportError:
        rprint("❌ Tidak dapat memuat informasi versi")


@app.command("init")
def init_system():
    """
    Inisialisasi sistem ICikiwir dan discover plugins
    """
    try:
        rprint("🚀 Menginisialisasi sistem ICikiwir...")
        
        # Initialize core system
        initialize_icikiwir()
        
        # Discover plugins
        plugin_count = registry.discover()
        
        rprint(f"✅ Sistem berhasil diinisialisasi dengan {plugin_count} plugin")
        
        # Tampilkan ringkasan
        plugins = registry.list_plugins()
        for category, names in plugins.items():
            if names:
                rprint(f"   📦 {category}: {len(names)} plugin")
        
    except Exception as e:
        rprint(f"❌ Error inisialisasi: {e}")
        raise typer.Exit(1)


@app.command("export-csv")
def export_csv(
    output_file: Optional[str] = typer.Option(
        None,
        "--output",
        "-o",
        help="Path file output CSV (default: focus_metrics_YYYYMMDD.csv)"
    ),
    date_filter: Optional[str] = typer.Option(
        None,
        "--date",
        "-d",
        help="Filter berdasarkan tanggal (format: YYYY-MM-DD)"
    ),
    limit: Optional[int] = typer.Option(
        None,
        "--limit",
        "-l",
        help="Batasi jumlah record yang diekspor"
    )
):
    """
    Ekspor data focus metrics ke file CSV
    """
    try:
        from src.storage.logger import FocusLogger
        from datetime import datetime
        import os
        
        rprint("📊 Mengekspor data focus metrics...")
        
        # Inisialisasi logger
        logger = FocusLogger()
        
        # Generate nama file jika tidak disediakan
        if not output_file:
            today = datetime.now().strftime("%Y%m%d")
            output_file = f"focus_metrics_{today}.csv"
        
        # Pastikan direktori output ada
        output_dir = os.path.dirname(output_file) if os.path.dirname(output_file) else "."
        os.makedirs(output_dir, exist_ok=True)
        
        # Ekspor data
        rprint(f"📁 Mengekspor ke: {output_file}")
        
        # Build filter parameters
        filters = {}
        if date_filter:
            try:
                # Validasi format tanggal
                datetime.strptime(date_filter, "%Y-%m-%d")
                filters['date'] = date_filter
                rprint(f"📅 Filter tanggal: {date_filter}")
            except ValueError:
                rprint("❌ Format tanggal tidak valid. Gunakan format: YYYY-MM-DD")
                raise typer.Exit(1)
        
        if limit:
            filters['limit'] = limit
            rprint(f"📊 Limit record: {limit}")
        
        # Panggil method export_to_csv dari logger
        success = logger.export_to_csv(output_file, **filters)
        
        if success:
            # Tampilkan informasi file yang dihasilkan
            if os.path.exists(output_file):
                file_size = os.path.getsize(output_file)
                rprint(f"✅ Ekspor berhasil!")
                rprint(f"📁 File: {output_file}")
                rprint(f"📏 Ukuran: {file_size:,} bytes")
                
                # Hitung jumlah baris (minus header)
                with open(output_file, 'r') as f:
                    line_count = sum(1 for _ in f) - 1  # Minus header
                rprint(f"📊 Total record: {line_count:,}")
            else:
                rprint("⚠️  File berhasil dibuat tapi tidak dapat diverifikasi")
        else:
            rprint("❌ Ekspor gagal atau tidak ada data untuk diekspor")
            raise typer.Exit(1)
        
    except ImportError as e:
        rprint(f"❌ Error import: {e}")
        rprint("Pastikan modul storage sudah tersedia")
        raise typer.Exit(1)
    except Exception as e:
        rprint(f"❌ Error ekspor: {e}")
        raise typer.Exit(1)


@app.command("generate-report")
def generate_report(
    date: Optional[str] = typer.Option(
        None,
        "--date",
        "-d",
        help="Tanggal laporan (format: YYYY-MM-DD). Default: hari ini"
    ),
    output_dir: Optional[str] = typer.Option(
        None,
        "--output",
        "-o",
        help="Direktori output laporan. Default: data/reports"
    )
):
    """
    Generate laporan harian dalam format Markdown
    """
    try:
        from src.reporting.generator import generate_daily_report
        from src.storage.logger import FocusLogger
        from datetime import datetime, date as date_obj
        import pandas as pd
        
        rprint("📝 Generating laporan harian...")
        
        # Parse tanggal atau gunakan hari ini
        if date:
            try:
                target_date = datetime.strptime(date, "%Y-%m-%d").date()
                rprint(f"📅 Tanggal laporan: {target_date}")
            except ValueError:
                rprint("❌ Format tanggal tidak valid. Gunakan format: YYYY-MM-DD")
                raise typer.Exit(1)
        else:
            target_date = date_obj.today()
            rprint(f"📅 Tanggal laporan: {target_date} (hari ini)")
        
        # Inisialisasi logger untuk mengambil data
        logger = FocusLogger()
        
        # Ambil data metrics untuk tanggal tersebut
        rprint("📊 Mengambil data metrics...")
        
        # Simulasi data jika tidak ada data real (untuk testing)
        # Dalam implementasi nyata, ini akan mengambil dari database
        try:
            # Coba ambil data dari database
            # Untuk sementara, kita buat dummy data untuk testing
            import numpy as np
            
            # Generate dummy data untuk testing
            timestamps = pd.date_range(
                start=f"{target_date} 08:00:00",
                end=f"{target_date} 17:00:00",
                freq='1min'
            )
            
            # Simulasi data fokus yang realistis
            np.random.seed(42)  # Untuk konsistensi
            focus_scores = 70 + 20 * np.sin(np.linspace(0, 4*np.pi, len(timestamps))) + 10 * np.random.randn(len(timestamps))
            focus_scores = np.clip(focus_scores, 0, 100)
            
            blink_rates = 15 + 5 * np.random.randn(len(timestamps))
            blink_rates = np.clip(blink_rates, 5, 30)
            
            metrics_df = pd.DataFrame({
                'timestamp': timestamps,
                'focus_score': focus_scores,
                'blink_rate': blink_rates,
                'head_pose_yaw': 5 * np.random.randn(len(timestamps)),
                'head_pose_pitch': 3 * np.random.randn(len(timestamps))
            })
            
            rprint(f"📈 Data ditemukan: {len(metrics_df)} record")
            
        except Exception as e:
            rprint(f"⚠️  Menggunakan dummy data untuk testing: {e}")
            # Fallback ke dummy data minimal
            metrics_df = pd.DataFrame({
                'timestamp': [datetime.now()],
                'focus_score': [75.0],
                'blink_rate': [15.0],
                'head_pose_yaw': [0.0],
                'head_pose_pitch': [0.0]
            })
        
        # Generate laporan
        rprint("📝 Generating laporan markdown...")
        report_path = generate_daily_report(target_date, metrics_df, output_dir)
        
        if report_path and report_path.exists():
            rprint(f"✅ Laporan berhasil dibuat!")
            rprint(f"📁 File: {report_path}")
            rprint(f"📏 Ukuran: {report_path.stat().st_size:,} bytes")
            
            # Tampilkan preview isi laporan
            with open(report_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
                preview_lines = lines[:10]  # Tampilkan 10 baris pertama
                
            rprint("\n📖 Preview laporan:")
            rprint("─" * 50)
            for line in preview_lines:
                rprint(line)
            if len(lines) > 10:
                rprint(f"... dan {len(lines) - 10} baris lainnya")
            rprint("─" * 50)
            
        else:
            rprint("❌ Gagal membuat laporan")
            raise typer.Exit(1)
        
    except ImportError as e:
        rprint(f"❌ Error import: {e}")
        rprint("Pastikan modul reporting sudah tersedia")
        raise typer.Exit(1)
    except Exception as e:
        rprint(f"❌ Error generate report: {e}")
        import traceback
        rprint(f"Detail error: {traceback.format_exc()}")
        raise typer.Exit(1)


if __name__ == "__main__":
    app()