"""
Script untuk download dan setup pre-trained models
Mengunduh model ONNX untuk expression detection dan gesture recognition

(c) 2025 Radhitya Guntoro Adhi
"""

import os
import sys
import urllib.request
from pathlib import Path
import hashlib

def get_models_dir() -> Path:
    """
    Mendapatkan path direktori models
    
    Returns:
        Path: Path ke direktori models
    """
    script_dir = Path(__file__).parent
    models_dir = script_dir.parent / "src" / "vision" / "models"
    models_dir.mkdir(parents=True, exist_ok=True)
    return models_dir

def download_file(url: str, destination: Path, expected_hash: str = None) -> bool:
    """
    Download file dari URL ke destination
    
    Args:
        url: URL file yang akan didownload
        destination: Path tujuan file
        expected_hash: Hash SHA256 yang diharapkan (optional)
        
    Returns:
        bool: True jika berhasil, False jika gagal
    """
    try:
        print(f"📥 Mengunduh {destination.name}...")
        print(f"   URL: {url}")
        
        # Download dengan progress
        def show_progress(block_num, block_size, total_size):
            downloaded = block_num * block_size
            if total_size > 0:
                percent = min(100, (downloaded * 100) // total_size)
                print(f"\r   Progress: {percent}% ({downloaded}/{total_size} bytes)", end="")
        
        urllib.request.urlretrieve(url, destination, show_progress)
        print()  # New line setelah progress
        
        # Verifikasi hash jika diberikan
        if expected_hash:
            print(f"🔍 Memverifikasi hash file...")
            with open(destination, 'rb') as f:
                file_hash = hashlib.sha256(f.read()).hexdigest()
            
            if file_hash != expected_hash:
                print(f"❌ Hash tidak cocok!")
                print(f"   Expected: {expected_hash}")
                print(f"   Actual:   {file_hash}")
                destination.unlink()  # Hapus file yang corrupt
                return False
            else:
                print(f"✅ Hash verified!")
        
        print(f"✅ {destination.name} berhasil diunduh")
        return True
        
    except Exception as e:
        print(f"❌ Gagal mengunduh {destination.name}: {e}")
        if destination.exists():
            destination.unlink()
        return False

def setup_models():
    """
    Setup semua model yang diperlukan
    """
    print("🚀 Memulai setup models untuk ICikiwir...")
    print("=" * 50)
    
    models_dir = get_models_dir()
    print(f"📁 Models directory: {models_dir}")
    
    # Daftar model yang akan didownload
    # NOTE: URL ini adalah placeholder, akan diganti dengan URL model yang sebenarnya
    models = [
        {
            "name": "expression_vit.onnx",
            "url": "https://example.com/models/expression_vit.onnx",
            "hash": "placeholder_hash_expression_model",
            "description": "Vision Transformer untuk deteksi ekspresi wajah"
        },
        {
            "name": "gesture_cnn.onnx", 
            "url": "https://example.com/models/gesture_cnn.onnx",
            "hash": "placeholder_hash_gesture_model",
            "description": "CNN untuk deteksi gesture merokok dan tic"
        }
    ]
    
    success_count = 0
    total_models = len(models)
    
    for model in models:
        print(f"\n📦 Model: {model['name']}")
        print(f"   Deskripsi: {model['description']}")
        
        destination = models_dir / model['name']
        
        # Skip jika file sudah ada dan valid
        if destination.exists():
            print(f"   File sudah ada, memverifikasi...")
            if model.get('hash'):
                with open(destination, 'rb') as f:
                    file_hash = hashlib.sha256(f.read()).hexdigest()
                if file_hash == model['hash']:
                    print(f"   ✅ File valid, skip download")
                    success_count += 1
                    continue
                else:
                    print(f"   ⚠️ Hash tidak cocok, download ulang...")
            else:
                print(f"   ✅ File ada, skip download")
                success_count += 1
                continue
        
        # Download model
        if download_file(model['url'], destination, model.get('hash')):
            success_count += 1
    
    print("\n" + "=" * 50)
    print(f"📊 Hasil setup models:")
    print(f"   Berhasil: {success_count}/{total_models}")
    print(f"   Gagal: {total_models - success_count}/{total_models}")
    
    if success_count == total_models:
        print("🎉 Semua models berhasil di-setup!")
        return True
    else:
        print("⚠️ Beberapa models gagal di-setup")
        print("   Aplikasi mungkin tidak berfungsi dengan optimal")
        return False

def create_placeholder_models():
    """
    Membuat file placeholder untuk development
    Digunakan saat model asli belum tersedia
    """
    print("🔧 Membuat placeholder models untuk development...")
    
    models_dir = get_models_dir()
    
    placeholder_models = [
        "expression_vit.onnx",
        "gesture_cnn.onnx"
    ]
    
    for model_name in placeholder_models:
        placeholder_path = models_dir / model_name
        if not placeholder_path.exists():
            # Buat file placeholder kosong
            with open(placeholder_path, 'wb') as f:
                f.write(b"PLACEHOLDER_MODEL_FILE")
            print(f"   ✅ Created placeholder: {model_name}")
        else:
            print(f"   ⏭️ Placeholder sudah ada: {model_name}")

def main():
    """
    Fungsi utama script setup
    """
    print("🧠 ICikiwir Models Setup")
    print("(c) 2025 Radhitya Guntoro Adhi")
    print()
    
    # Parse command line arguments
    if len(sys.argv) > 1 and sys.argv[1] == "--placeholder":
        create_placeholder_models()
        return
    
    # Coba download model asli
    success = setup_models()
    
    if not success:
        print("\n🔧 Fallback: Membuat placeholder models...")
        create_placeholder_models()
        print("\n💡 Tips:")
        print("   - Periksa koneksi internet")
        print("   - Coba jalankan ulang script ini nanti")
        print("   - Gunakan --placeholder untuk development")

if __name__ == "__main__":
    main()