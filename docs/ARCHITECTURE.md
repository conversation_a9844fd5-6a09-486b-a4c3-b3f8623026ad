# ARSITEKTUR TEKNIS – Cognitive-Aware Study Companion
(c) 2025 Radhitya Guntoro Adhi

## 1. OVERVIEW ARSITEKTUR

### 1.1 Prinsip Desain
- **Modular & Loosely Coupled**: <PERSON>iap komponen dapat dikembangkan/ditest independen
- **Event-Driven Pipeline**: Komunikasi antar modul via event queue
- **Privacy-First**: <PERSON><PERSON><PERSON> pem<PERSON> on-device, tanpa cloud dependency
- **Resource-Efficient**: Optimasi untuk laptop spek menengah (RAM 8GB, CPU 4-core)
- **Extensible**: Mudah menambah detector/analyzer baru

### 1.2 Tech Stack Utama
- **Bahasa**: Python 3.10+
- **GUI Framework**: Flet (cross-platform, native performance)
- **Computer Vision**: OpenCV + MediaPipe + ONNX Runtime
- **Text-to-Speech**: pyttsx3 (offline) / edge-tts (quality lebih baik)
- **Data Storage**: SQLite (metadata) + CSV (time-series logs)
- **Visualisasi**: Altair (declarative) + Plotly (interactive)
- **Packaging**: PyInstaller (single executable)

## 2. ARSITEKTUR PIPELINE

### 2.1 Data Flow Diagram (ASCII)

```
┌─────────────────┐     ┌──────────────────┐     ┌─────────────────┐
│   Webcam Input  │────▶│  Frame Processor │────▶│ Feature Extract │
│   (30 FPS)      │     │  (OpenCV)        │     │ (MediaPipe)     │
└─────────────────┘     └──────────────────┘     └────────┬────────┘
                                                           │
                                                           ▼
┌─────────────────┐     ┌──────────────────┐     ┌─────────────────┐
│  TTS Engine     │◀────│   Rule Engine    │◀────│ State Analyzer  │
│  (pyttsx3)      │     │  (Thresholds)    │     │ (ML Models)     │
└─────────────────┘     └──────────────────┘     └────────┬────────┘
                                                           │
                                                           ▼
┌─────────────────┐     ┌──────────────────┐     ┌─────────────────┐
│ Flet Dashboard  │◀────│ Report Generator │◀────│  Data Logger    │
│ (Real-time UI)  │     │ (Altair/Plotly)  │     │ (SQLite/CSV)    │
└─────────────────┘     └──────────────────┘     └─────────────────┘
```

### 2.2 Event Flow
1. **Capture**: Webcam menangkap frame @ 30fps
2. **Process**: Resize, color correction, face detection
3. **Extract**: Landmark detection, blink rate, head pose, expression
4. **Analyze**: Hitung cognitive load score, emotion state, gesture detection
5. **Decide**: Rule engine cek threshold, trigger intervention jika perlu
6. **Act**: TTS speak nudge / Log event / Update dashboard
7. **Store**: Simpan aggregated metrics per menit ke database

## 3. STRUKTUR FOLDER

```
icikiwir/
├── src/
│   ├── __init__.py
│   ├── main.py                    # Entry point aplikasi
│   ├── config.py                  # Konfigurasi global & user preferences
│   │
│   ├── capture/                   # Modul akuisisi video
│   │   ├── __init__.py
│   │   ├── webcam.py              # Webcam handler dengan threading
│   │   └── frame_buffer.py        # Circular buffer untuk frames
│   │
│   ├── vision/                    # Computer vision processing
│   │   ├── __init__.py
│   │   ├── face_detector.py       # MediaPipe face detection
│   │   ├── landmark_extractor.py  # 468 facial landmarks
│   │   ├── blink_analyzer.py      # Hitung blink rate & duration
│   │   ├── head_pose.py           # Estimasi orientasi kepala
│   │   ├── expression_analyzer.py # Mikro-ekspresi (ONNX model)
│   │   ├── gesture_detector.py    # Deteksi merokok & tic
│   │   └── models/                # Pre-trained ONNX models
│   │       ├── expression_vit.onnx
│   │       └── gesture_cnn.onnx
│   │
│   ├── analytics/                 # State analysis & decision
│   │   ├── __init__.py
│   │   ├── cognitive_load.py      # Kalkulasi beban kognitif
│   │   ├── emotion_tracker.py     # Track emotional state
│   │   ├── focus_scorer.py        # Skor fokus composite
│   │   ├── habit_monitor.py       # Monitor kebiasaan (merokok/tic)
│   │   └── hydration_timer.py     # Timer minum air
│   │
│   ├── intervention/              # Feedback & nudging system
│   │   ├── __init__.py
│   │   ├── rule_engine.py         # If-then rules dengan priority
│   │   ├── tts_manager.py         # Text-to-speech wrapper
│   │   ├── nudge_templates.py     # Template kalimat nudge
│   │   └── adaptive_threshold.py  # Dynamic threshold adjustment
│   │
│   ├── storage/                   # Data persistence
│   │   ├── __init__.py
│   │   ├── database.py            # SQLite schema & queries
│   │   ├── csv_logger.py          # Time-series CSV writer
│   │   ├── aggregator.py          # Minute/hour/day aggregation
│   │   └── schemas.py             # Data models (Pydantic)
│   │
│   ├── reporting/                 # Report generation
│   │   ├── __init__.py
│   │   ├── chart_builder.py       # Altair/Plotly chart factory
│   │   ├── insight_generator.py   # Auto-generate text insights
│   │   ├── report_templates.py    # HTML/PDF templates
│   │   └── export_manager.py      # Export ke berbagai format
│   │
│   └── gui/                       # Flet UI components
│       ├── __init__.py
│       ├── app.py                 # Main Flet app
│       ├── dashboard_view.py      # Real-time monitoring view
│       ├── settings_view.py       # User preferences panel
│       ├── reports_view.py        # Historical reports viewer
│       ├── components/            # Reusable UI components
│       │   ├── focus_gauge.py     # Circular progress indicator
│       │   ├── emotion_chart.py   # Real-time emotion plot
│       │   ├── session_timer.py   # Pomodoro timer widget
│       │   └── notification.py    # Toast notifications
│       └── assets/                # Icons, fonts, sounds
│
├── tests/                         # Unit & integration tests
│   ├── test_vision/
│   ├── test_analytics/
│   ├── test_storage/
│   └── test_integration.py
│
├── data/                          # User data (gitignored)
│   ├── icikiwir.db               # SQLite database
│   ├── logs/                      # CSV time-series logs
│   └── reports/                   # Generated reports
│
├── docs/                          # Documentation
│   ├── ARCHITECTURE.md            # This file
│   ├── API.md                     # Internal API docs
│   ├── USER_GUIDE.md              # Panduan pengguna
│   └── PRIVACY.md                 # Privacy policy
│
├── scripts/                       # Utility scripts
│   ├── setup_models.py            # Download pre-trained models
│   ├── migrate_db.py              # Database migration
│   └── benchmark.py               # Performance testing
│
├── requirements.txt               # Python dependencies
├── pyproject.toml                 # Project metadata
├── README.md                      # Project overview
├── LICENSE                        # MIT License
└── .gitignore                     # Ignore patterns
```

## 4. KOMPONEN DETAIL & LIBRARY

### 4.1 Computer Vision Stack
- **OpenCV** (4.8+): Frame capture, preprocessing, basic CV operations
- **MediaPipe** (0.10+): Face mesh (468 landmarks), face detection
- **ONNX Runtime** (1.16+): Inference untuk custom models (expression, gesture)
- **NumPy** (1.24+): Array operations & mathematical computations

### 4.2 Audio/TTS Stack  
- **pyttsx3** (2.90+): Offline TTS, multi-voice support
- **edge-tts** (optional): Higher quality voices (requires internet)
- **simpleaudio**: Play notification sounds

### 4.3 Data & Analytics Stack
- **SQLite3** (built-in): Relational storage untuk metadata & aggregates
- **Pandas** (2.0+): Time-series manipulation & analysis
- **Altair** (5.0+): Declarative visualization (Vega-Lite based)
- **Plotly** (5.17+): Interactive charts untuk dashboard
- **Pydantic** (2.0+): Data validation & serialization

### 4.4 GUI Stack
- **Flet** (0.19+): Flutter-based Python UI framework
- **Pillow** (10.0+): Image processing untuk UI display
- **asyncio**: Async event handling

### 4.5 Utility Libraries
- **python-dotenv**: Environment variable management
- **loguru**: Structured logging
- **schedule**: Cron-like job scheduling
- **pytest**: Testing framework

## 5. STRATEGI DATA STORAGE & AGGREGATION

### 5.1 Multi-Tier Storage

```
┌─────────────────────────────────────────────────────────┐
│                    REAL-TIME BUFFER                     │
│         In-memory circular buffer (last 5 mins)         │
└────────────────────────┬────────────────────────────────┘
                         │ Every 60s
                         ▼
┌─────────────────────────────────────────────────────────┐
│                    MINUTE LOGS (CSV)                    │
│    timestamp, focus_score, blink_rate, emotion, ...     │
└────────────────────────┬────────────────────────────────┘
                         │ Every hour
                         ▼
┌─────────────────────────────────────────────────────────┐
│                   HOURLY AGGREGATES (SQLite)            │
│    hour_id, avg_focus, total_blinks, emotion_dist, ... │
└────────────────────────┬────────────────────────────────┘
                         │ Every day
                         ▼
┌─────────────────────────────────────────────────────────┐
│                   DAILY SUMMARIES (SQLite)              │
│  date, sessions, total_focus_time, top_emotions, ...    │
└─────────────────────────────────────────────────────────┘
```

### 5.2 Database Schema (SQLite)

```sql
-- Tabel sesi belajar
CREATE TABLE sessions (
    id INTEGER PRIMARY KEY,
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    total_duration_minutes REAL,
    avg_focus_score REAL,
    interruption_count INTEGER
);

-- Tabel agregat per jam
CREATE TABLE hourly_metrics (
    id INTEGER PRIMARY KEY,
    session_id INTEGER REFERENCES sessions(id),
    hour_timestamp TIMESTAMP,
    avg_focus_score REAL,
    avg_blink_rate REAL,
    dominant_emotion TEXT,
    tic_count INTEGER,
    smoke_gesture_count INTEGER
);

-- Tabel daily summary
CREATE TABLE daily_summaries (
    date DATE PRIMARY KEY,
    total_sessions INTEGER,
    total_focus_minutes REAL,
    avg_daily_focus REAL,
    top_3_emotions TEXT,  -- JSON array
    total_tics INTEGER,
    total_smokes INTEGER,
    hydration_reminders INTEGER
);

-- Tabel user preferences
CREATE TABLE preferences (
    key TEXT PRIMARY KEY,
    value TEXT,
    updated_at TIMESTAMP
);
```

### 5.3 CSV Time-Series Format

```csv
timestamp,focus_score,blink_rate,head_pitch,head_yaw,emotion,tic_detected,smoke_detected
2025-01-22T13:40:00,0.82,15.2,-5.3,2.1,neutral,0,0
2025-01-22T13:40:01,0.79,14.8,-4.9,1.8,neutral,0,0
2025-01-22T13:40:02,0.75,18.3,-8.2,5.4,tired,1,0
```

## 6. IMPLEMENTATION ROADMAP PER PHASE

| Phase | Nama | Mode | Deskripsi | Tasks |
|-------|------|------|-----------|-------|
| A | Real-Time Focus Monitor | code | Implementasi core monitoring system untuk deteksi fokus real-time | 1. Setup webcam capture dengan threading<br>2. Implement MediaPipe face detection & landmarks<br>3. Build blink rate analyzer<br>4. Create head pose estimator<br>5. Develop cognitive load calculator<br>6. Unit tests untuk accuracy metrics |
| B | Adaptive Feedback TTS | code | Sistem feedback audio yang adaptif dan non-intrusive | 1. Setup pyttsx3 engine dengan voice selection<br>2. Create nudge template system<br>3. Build rule engine dengan priority queue<br>4. Implement adaptive threshold<br>5. Test TTS latency & quality |
| C | Session Logger & Dashboard | code | Dashboard real-time dan sistem logging untuk tracking progress | 1. Design Flet app structure<br>2. Create real-time dashboard view<br>3. Build focus gauge component<br>4. Implement CSV logger dengan rotation<br>5. Setup SQLite schema & migrations<br>6. Create session timer widget |
| D | Multi-Scale Reports & Analytics | code | Sistem reporting komprehensif dengan visualisasi data | 1. Build chart factory untuk 10+ chart types<br>2. Implement data aggregator<br>3. Create insight generator dengan NLP templates<br>4. Design report templates HTML/PDF<br>5. Build reports viewer in Flet<br>6. Test report generation performance |
| E | Balanced Habit Tracker (Beta) | code → debug | Monitoring kebiasaan dengan pendekatan non-judgmental | 1. Integrate gesture detection model<br>2. Build habit monitoring logic<br>3. Create non-judgmental notification system<br>4. Add habit settings to preferences panel<br>5. Collect training data untuk improve accuracy<br>6. Debug false positive rates |
| F | Hydration Reminder (Light) | code | Reminder sederhana untuk menjaga hidrasi | 1. Implement hydration timer<br>2. Add water intake tracking to dashboard<br>3. Create gentle reminder notifications<br>4. Add hydration settings to preferences |

## 7. PERFORMANCE TARGETS

- **Frame Processing**: < 33ms per frame (maintain 30 FPS)
- **Model Inference**: < 50ms untuk emotion/gesture detection
- **TTS Latency**: < 200ms dari trigger ke audio output
- **Memory Usage**: < 500MB RAM steady state
- **CPU Usage**: < 25% on quad-core processor
- **Storage Growth**: < 10MB per day of usage

## 8. DEVELOPMENT GUIDELINES

### 8.1 Coding Standards
- Gunakan type hints untuk semua functions
- Docstring bahasa Indonesia untuk public APIs
- Variable & function names dalam bahasa Inggris
- Comments dalam bahasa Indonesia
- Follow PEP 8 dengan line length 100

### 8.2 Testing Strategy
- Unit test coverage minimum 80%
- Integration tests untuk critical paths
- Performance benchmarks untuk CV pipeline
- Manual testing checklist untuk UI/UX

### 8.3 Git Workflow
- Branch naming: `feature/epic-X-description`
- Commit message: `[EpicX] Deskripsi singkat`
- PR requires: tests passing, code review
- Main branch always deployable

## 9. DEPLOYMENT & DISTRIBUTION

### 9.1 Build Process
```bash
# Install dependencies
pip install -r requirements.txt

# Download pre-trained models
python scripts/setup_models.py

# Run tests
pytest tests/

# Build executable
pyinstaller --onefile --windowed src/main.py
```

### 9.2 Distribution
- GitHub Releases dengan pre-built binaries
- README lengkap dengan screenshots
- Video tutorial penggunaan
- Installer script untuk Windows/Mac/Linux

## 10. PRIVACY & SECURITY

- Semua data disimpan lokal di folder user
- Tidak ada network requests kecuali user eksplisit mengizinkan
- Video frames tidak pernah disimpan, hanya metrics
- Enkripsi database dengan user-provided password (optional)
- Clear data ownership: 100% milik user

## 11. EVOLVABILITY & PLUG-IN DESIGN

### 11.1 Prinsip Evolvability

ICikiwir dirancang dengan 10 prinsip evolvability untuk memastikan sistem dapat berkembang dan diperluas dengan mudah:

#### Diagram Arsitektur Plugin

```
┌─────────────────────────────────────────────────────────┐
│                    ICikiwir Core                        │
├─────────────────────────────────────────────────────────┤
│                  Plugin Registry                        │
├─────────────────┬─────────────────┬─────────────────────┤
│  Vision Plugins │ Analytics Plugins│ Intervention Plugins│
├─────────────────┼─────────────────┼─────────────────────┤
│ Built-in:       │ Built-in:       │ Built-in:           │
│ • MediaPipe     │ • Focus Scorer  │ • TTS Engine        │
│ • Face Tracker  │ • Emotion Track │ • Rule Engine       │
├─────────────────┼─────────────────┼─────────────────────┤
│ External:       │ External:       │ External:           │
│ • Custom ONNX   │ • EEG Analyzer  │ • Smart Light       │
│ • Gesture Det.  │ • Biometric     │ • IoT Integration   │
└─────────────────┴─────────────────┴─────────────────────┘
```

#### 10 Prinsip Evolvability

1. **Layered-Hexagonal Architecture**
   - Core domain logic terpisah dari infrastructure
   - Dependency injection untuk loose coupling
   - Port & adapter pattern untuk integrasi eksternal

2. **Plug-in Registry Pattern**
   - Dynamic loading via Python entry_points
   - Auto-discovery saat startup aplikasi
   - Lazy loading untuk optimasi performa

3. **Event-Driven Communication**
   - Event bus untuk komunikasi antar modul
   - Publish-subscribe pattern implementation
   - Async message passing untuk responsivitas

4. **Configuration-First Design**
   - YAML/JSON config untuk behavior tuning
   - Environment variable override support
   - Hot-reload config tanpa restart aplikasi

5. **Protocol-Based Typing**
   - Python Protocol untuk interface contracts
   - Duck typing dengan type safety
   - Clear API boundaries antar komponen

6. **Testing Pyramid Strategy**
   - Unit tests > Integration tests > E2E tests
   - Mock protocols untuk isolated testing
   - Property-based testing untuk edge cases

7. **Semantic Versioning**
   - Major.Minor.Patch versioning scheme
   - Breaking changes hanya di major version
   - Backward compatibility commitment

8. **Documentation as Code**
   - Docstrings auto-generate API docs
   - Markdown documentation di repository
   - Example code yang executable dan teruji

9. **Feature Flags System**
   - Gradual rollout capabilities
   - A/B testing framework built-in
   - Kill switch untuk experimental features

10. **Public Development Roadmap**
    - GitHub Projects untuk progress tracking
    - Community input via Issues dan Discussions
    - Transparent feature prioritization

### 11.2 Plugin Interface Specification

```python
# src/plugins/base.py
from typing import Protocol, Dict, Any, Optional
from abc import abstractmethod
import numpy as np

class VisionPlugin(Protocol):
    """
    Interface untuk vision processing plugins
    (c) 2025 Radhitya Guntoro Adhi
    """
    
    @property
    @abstractmethod
    def name(self) -> str:
        """Nama unik plugin (e.g. 'custom_emotion_detector')"""
        ...
    
    @property
    @abstractmethod
    def version(self) -> str:
        """Versi plugin menggunakan semantic versioning"""
        ...
    
    @property
    @abstractmethod
    def description(self) -> str:
        """Deskripsi singkat fungsi plugin"""
        ...
    
    @abstractmethod
    def process_frame(self, frame: np.ndarray) -> Dict[str, Any]:
        """
        Process single video frame
        
        Args:
            frame: RGB image array (H, W, 3)
            
        Returns:
            Dict dengan keys:
            - metrics: Dict[str, float] - nilai metrik
            - annotations: Optional[np.ndarray] - overlay visual
            - metadata: Dict[str, Any] - info tambahan
        """
        ...
    
    @abstractmethod
    def get_config_schema(self) -> Dict[str, Any]:
        """Return JSON Schema untuk validasi konfigurasi"""
        ...
    
    @abstractmethod
    def configure(self, config: Dict[str, Any]) -> None:
        """Apply konfigurasi ke plugin"""
        ...
    
    @abstractmethod
    def cleanup(self) -> None:
        """Cleanup resources saat plugin di-unload"""
        ...

class AnalyticsPlugin(Protocol):
    """
    Interface untuk analytics processing plugins
    (c) 2025 Radhitya Guntoro Adhi
    """
    
    @property
    @abstractmethod
    def name(self) -> str:
        """Nama unik plugin"""
        ...
    
    @abstractmethod
    def analyze_metrics(self, metrics: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze raw metrics dan return insights
        
        Args:
            metrics: Raw metrics dari vision plugins
            
        Returns:
            Dict dengan analyzed results
        """
        ...

class InterventionPlugin(Protocol):
    """
    Interface untuk intervention plugins
    (c) 2025 Radhitya Guntoro Adhi
    """
    
    @property
    @abstractmethod
    def name(self) -> str:
        """Nama unik plugin"""
        ...
    
    @abstractmethod
    def trigger_intervention(self, context: Dict[str, Any]) -> bool:
        """
        Trigger intervention berdasarkan context
        
        Args:
            context: Context data untuk decision making
            
        Returns:
            bool: True jika intervention berhasil triggered
        """
        ...
```

### 11.3 Plugin Registry Implementation

```python
# src/plugins/registry.py
import importlib.metadata
from typing import Dict, Type, List, Union
import logging
from pathlib import Path

class PluginRegistry:
    """
    Central registry untuk manage plugins
    (c) 2025 Radhitya Guntoro Adhi
    """
    
    def __init__(self):
        self._vision_plugins: Dict[str, Type[VisionPlugin]] = {}
        self._analytics_plugins: Dict[str, Type[AnalyticsPlugin]] = {}
        self._intervention_plugins: Dict[str, Type[InterventionPlugin]] = {}
        self._instances: Dict[str, Union[VisionPlugin, AnalyticsPlugin, InterventionPlugin]] = {}
        self.logger = logging.getLogger(__name__)
    
    def discover_plugins(self) -> None:
        """Auto-discover plugins via entry points"""
        for entry_point in importlib.metadata.entry_points(
            group='icikiwir.plugins'
        ):
            try:
                plugin_class = entry_point.load()
                self.register_plugin(entry_point.name, plugin_class)
                self.logger.info(f"Plugin loaded: {entry_point.name}")
            except Exception as e:
                self.logger.error(f"Failed to load plugin {entry_point.name}: {e}")
    
    def register_plugin(self, name: str, plugin_class: Type) -> None:
        """Register plugin class berdasarkan type"""
        if hasattr(plugin_class, 'process_frame'):
            self._vision_plugins[name] = plugin_class
        elif hasattr(plugin_class, 'analyze_metrics'):
            self._analytics_plugins[name] = plugin_class
        elif hasattr(plugin_class, 'trigger_intervention'):
            self._intervention_plugins[name] = plugin_class
        else:
            raise ValueError(f"Unknown plugin type: {plugin_class}")
    
    def create_instance(self, name: str, config: Dict[str, Any]) -> Union[VisionPlugin, AnalyticsPlugin, InterventionPlugin]:
        """Create configured plugin instance"""
        plugin_class = None
        
        if name in self._vision_plugins:
            plugin_class = self._vision_plugins[name]
        elif name in self._analytics_plugins:
            plugin_class = self._analytics_plugins[name]
        elif name in self._intervention_plugins:
            plugin_class = self._intervention_plugins[name]
        
        if plugin_class is None:
            raise ValueError(f"Plugin '{name}' not found")
        
        instance = plugin_class()
        instance.configure(config)
        self._instances[name] = instance
        return instance
    
    def list_plugins(self) -> Dict[str, List[str]]:
        """List semua registered plugins"""
        return {
            "vision": list(self._vision_plugins.keys()),
            "analytics": list(self._analytics_plugins.keys()),
            "intervention": list(self._intervention_plugins.keys())
        }
```

### 11.4 Entry Points Configuration

Untuk membuat plugin eksternal, tambahkan entry point di `pyproject.toml`:

```toml
# Contoh pyproject.toml untuk plugin eksternal
[project.entry-points."icikiwir.plugins"]
my_emotion_detector = "my_plugin.emotion:EmotionDetectorPlugin"
my_gesture_tracker = "my_plugin.gesture:GestureTrackerPlugin"
my_smart_light = "my_plugin.light:SmartLightPlugin"
```

### 11.5 Plugin Configuration

```yaml
# config/plugins.yaml
plugins:
  enabled:
    - my_emotion_detector
    - my_gesture_tracker
  
  configs:
    my_emotion_detector:
      model_path: "/path/to/emotion_model.onnx"
      confidence_threshold: 0.7
      face_min_size: 30
    
    my_gesture_tracker:
      detection_interval: 1.0
      sensitivity: 0.8
```

### 11.6 Plugin Activation via CLI

```bash
# List available plugins
python -m icikiwir --list-plugins

# Enable specific plugins
python -m icikiwir --enable-plugin my_emotion_detector,my_gesture_tracker

# Run dengan plugin config
python -m icikiwir --plugin-config config/plugins.yaml
```

### 11.7 Versioning & Compatibility

- **Plugin API Version**: Setiap plugin harus declare API version yang didukung
- **Backward Compatibility**: ICikiwir maintain compatibility untuk 1 major version
- **Deprecation Policy**: 6 bulan notice sebelum breaking changes
- **Migration Guide**: Dokumentasi lengkap untuk setiap major version upgrade

Untuk panduan lengkap membuat plugin, lihat [`docs/PLUGIN_GUIDE.md`](docs/PLUGIN_GUIDE.md).

## 12. MARKDOWN REPORT RENDERING

### 12.1 Struktur File Report

ICikiwir menggunakan format Markdown untuk laporan harian dengan struktur standar:

```markdown
---
title: "Laporan Sesi Belajar"
date: "2025-06-22"
author: "Radhitya Guntoro Adhi"
total_sessions: 3
total_focus_time: 185
avg_focus_score: 78.5
top_emotions: ["focused", "neutral", "tired"]
---

# Laporan Sesi Belajar - 22 Juni 2025

## Ringkasan Eksekutif
[Auto-generated insights]

## Visualisasi Data
[Embedded charts]

## Detail Sesi
[Session breakdowns]
```

### 12.2 Lokasi Penyimpanan

Semua laporan disimpan dengan konvensi penamaan standar:

```
data/
└── reports/
    ├── 2025-06-22.md    # Laporan harian
    ├── 2025-W25.md      # Laporan mingguan
    └── 2025-06.md       # Laporan bulanan
```

### 12.3 Front-matter YAML

Setiap laporan dimulai dengan metadata YAML yang berisi:

```yaml
---
# Metadata wajib
title: string              # Judul laporan
date: string              # Format YYYY-MM-DD
author: string            # Nama pengguna

# Statistik utama
total_sessions: number    # Total sesi belajar
total_focus_time: number  # Total menit fokus
avg_focus_score: number   # Rata-rata skor fokus (0-100)

# Data agregat
top_emotions: array       # 3 emosi teratas
interruption_count: number # Total interupsi
tic_count: number         # Total tic terdeteksi
hydration_reminders: number # Total reminder minum

# Metadata opsional
tags: array               # Tag untuk kategorisasi
version: string           # Versi format laporan
---
```

### 12.4 Embedding Grafik

ICikiwir mendukung dua metode untuk embed grafik dalam laporan:

#### Metode 1: PNG Base64
```markdown
![Focus Trend](data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUA...)
```

Keuntungan:
- Self-contained, tidak perlu file eksternal
- Kompatibel dengan semua Markdown viewer
- Mudah di-share sebagai single file

#### Metode 2: HTML/Plotly Interactive
```html
<div id="focus-chart">
<script type="text/javascript">
  var data = [{x: [...], y: [...], type: 'scatter'}];
  var layout = {title: 'Trend Fokus Harian'};
  Plotly.newPlot('focus-chart', data, layout);
</script>
</div>
```

Keuntungan:
- Grafik interaktif (zoom, pan, hover)
- Ukuran file lebih kecil
- Dapat di-update secara dinamis

### 12.5 Viewer di GUI (Flet)

Implementasi viewer menggunakan Flet Markdown widget:

```python
# src/gui/components/report_viewer.py
import flet as ft
from pathlib import Path
import base64

class ReportViewer(ft.UserControl):
    """
    Markdown report viewer dengan dukungan grafik
    (c) 2025 Radhitya Guntoro Adhi
    """
    
    def __init__(self, report_path: Path):
        super().__init__()
        self.report_path = report_path
        
    def build(self):
        # Baca konten markdown
        content = self.report_path.read_text(encoding='utf-8')
        
        # Parse front-matter
        if content.startswith('---'):
            _, front_matter, markdown = content.split('---', 2)
            # Process metadata if needed
        else:
            markdown = content
        
        # Create markdown viewer
        md_viewer = ft.Markdown(
            markdown,
            selectable=True,
            extension_set=ft.MarkdownExtensionSet.GITHUB_WEB,
            code_theme="monokai",
            on_tap_link=self.handle_link_tap,
            # Custom image handler untuk base64
            img_error_content=ft.Text("Failed to load image"),
        )
        
        return ft.Container(
            content=ft.Column([
                # Header dengan metadata
                self.build_header(front_matter),
                ft.Divider(),
                # Markdown content dengan scroll
                ft.Container(
                    content=md_viewer,
                    height=600,
                    padding=20,
                    bgcolor=ft.colors.SURFACE_VARIANT,
                    border_radius=10,
                ),
                # Export buttons
                self.build_export_buttons()
            ]),
            padding=20
        )
    
    def build_header(self, metadata: dict) -> ft.Control:
        """Build header dari metadata"""
        return ft.Row([
            ft.Icon(ft.icons.ANALYTICS, size=40),
            ft.Column([
                ft.Text(metadata.get('title', 'Laporan'),
                       size=24, weight=ft.FontWeight.BOLD),
                ft.Text(f"Tanggal: {metadata.get('date', 'N/A')}",
                       size=14, color=ft.colors.SECONDARY),
            ], expand=True),
            ft.Chip(
                label=ft.Text(f"Skor: {metadata.get('avg_focus_score', 0):.1f}"),
                bgcolor=self.get_score_color(metadata.get('avg_focus_score', 0))
            )
        ])
    
    def build_export_buttons(self) -> ft.Control:
        """Build export action buttons"""
        return ft.Row([
            ft.ElevatedButton(
                "Export PDF",
                icon=ft.icons.PICTURE_AS_PDF,
                on_click=self.export_pdf
            ),
            ft.ElevatedButton(
                "Share",
                icon=ft.icons.SHARE,
                on_click=self.share_report
            ),
        ], alignment=ft.MainAxisAlignment.END)
    
    def get_score_color(self, score: float) -> str:
        """Return color based on score"""
        if score >= 80:
            return ft.colors.GREEN_400
        elif score >= 60:
            return ft.colors.AMBER_400
        else:
            return ft.colors.RED_400
```

### 12.6 Integrasi dengan Report Generator

```python
# src/reporting/markdown_renderer.py
from typing import Dict, Any
import yaml
from datetime import datetime
from pathlib import Path

class MarkdownReportRenderer:
    """
    Render laporan dalam format Markdown
    (c) 2025 Radhitya Guntoro Adhi
    """
    
    def __init__(self, template_path: Path = None):
        self.template_path = template_path
        
    def render_daily_report(self, data: Dict[str, Any]) -> str:
        """Render laporan harian"""
        # Generate front-matter
        front_matter = {
            'title': 'Laporan Sesi Belajar',
            'date': data['date'].strftime('%Y-%m-%d'),
            'author': 'Radhitya Guntoro Adhi',
            'total_sessions': data['total_sessions'],
            'total_focus_time': data['total_focus_time'],
            'avg_focus_score': data['avg_focus_score'],
            'top_emotions': data['top_emotions'][:3],
        }
        
        # Build markdown content
        content = []
        content.append('---')
        content.append(yaml.dump(front_matter, default_flow_style=False))
        content.append('---')
        content.append('')
        content.append(f"# Laporan Sesi Belajar - {data['date'].strftime('%d %B %Y')}")
        content.append('')
        
        # Executive summary
        content.append('## Ringkasan Eksekutif')
        content.extend(self.generate_insights(data))
        content.append('')
        
        # Visualizations
        content.append('## Visualisasi Data')
        content.extend(self.embed_charts(data))
        content.append('')
        
        # Session details
        content.append('## Detail Sesi')
        content.extend(self.format_sessions(data['sessions']))
        
        return '\n'.join(content)
    
    def embed_charts(self, data: Dict[str, Any]) -> List[str]:
        """Embed charts as base64 PNG or HTML"""
        charts = []
        
        # Focus trend chart
        focus_chart_b64 = self.generate_focus_chart(data)
        charts.append(f'### Trend Fokus')
        charts.append(f'![Trend Fokus](data:image/png;base64,{focus_chart_b64})')
        charts.append('')
        
        # Emotion distribution
        emotion_html = self.generate_emotion_chart(data)
        charts.append('### Distribusi Emosi')
        charts.append('<div id="emotion-chart">')
        charts.append(emotion_html)
        charts.append('</div>')
        
        return charts
```

---

"Arsitektur yang baik adalah yang memudahkan perubahan."