# Rencana Implementasi Evolvability & Plug-in Design
(c) 2025 Radhitya Guntoro Adhi

## 📋 Ringkasan Tugas

Menambahkan dokumentasi komprehensif tentang prinsip evolvability dan sistem plug-in ke dalam proyek ICikiwir untuk memudahkan developer masa depan dalam menambah modul dan ekstensi.

## 🎯 Tujuan

1. Mendokumentasikan 10 prinsip evolvability yang sudah dirangkum
2. Menyediakan panduan teknis untuk membuat plug-in eksternal
3. Memperbarui dokumentasi kontribusi dengan link yang relevan
4. Memastikan arsitektur aplikasi mudah diperluas tanpa mengubah core

## 📝 Daftar Perubahan

### 1. Update `docs/ARCHITECTURE.md`

**Lokasi**: Setelah section 10 (Privacy & Security)

**Tambahkan Section 11: Evolvability & Plug-in Design**

#### Konten yang akan ditambahkan:

##### 11.1 Prinsip Evolvability

```mermaid
graph TD
    A[ICikiwir Core] --> B[Plugin Registry]
    B --> C[Vision Plugins]
    B --> D[Analytics Plugins]
    B --> E[Intervention Plugins]
    
    C --> F[Built-in: MediaPipe]
    C --> G[External: Custom ONNX]
    
    D --> H[Built-in: Focus Scorer]
    D --> I[External: EEG Analyzer]
    
    E --> J[Built-in: TTS]
    E --> K[External: Smart Light]
```

**10 Prinsip Evolvability:**

1. **Layered-Hexagonal Architecture**
   - Core domain logic terpisah dari infrastructure
   - Dependency injection untuk loose coupling
   - Port & adapter pattern untuk integrasi eksternal

2. **Plug-in Registry Pattern**
   - Dynamic loading via Python entry_points
   - Auto-discovery saat startup
   - Lazy loading untuk performa optimal

3. **Event-Driven Communication**
   - Event bus untuk komunikasi antar modul
   - Publish-subscribe pattern
   - Async message passing

4. **Configuration-First Design**
   - YAML/JSON config untuk behavior tuning
   - Environment variable override
   - Hot-reload config tanpa restart

5. **Protocol-Based Typing**
   - Python Protocol untuk interface contracts
   - Duck typing dengan type safety
   - Clear API boundaries

6. **Testing Pyramid Strategy**
   - Unit tests > Integration tests > E2E tests
   - Mock protocols untuk isolated testing
   - Property-based testing untuk edge cases

7. **Semantic Versioning**
   - Major.Minor.Patch versioning
   - Breaking changes di major version
   - Backward compatibility commitment

8. **Documentation as Code**
   - Docstrings auto-generate API docs
   - Markdown docs di repo
   - Example code yang executable

9. **Feature Flags System**
   - Gradual rollout capabilities
   - A/B testing framework
   - Kill switch untuk features

10. **Public Development Roadmap**
    - GitHub Projects untuk tracking
    - Community input via Issues
    - Transparent prioritization

##### 11.2 Plugin Interface Specification

```python
# src/plugins/base.py
from typing import Protocol, Dict, Any, Optional
from abc import abstractmethod
import numpy as np

class VisionPlugin(Protocol):
    """
    Interface untuk vision processing plugins
    (c) 2025 Radhitya Guntoro Adhi
    """
    
    @property
    @abstractmethod
    def name(self) -> str:
        """Nama unik plugin (e.g. 'custom_emotion_detector')"""
        ...
    
    @property
    @abstractmethod
    def version(self) -> str:
        """Versi plugin menggunakan semantic versioning"""
        ...
    
    @property
    @abstractmethod
    def description(self) -> str:
        """Deskripsi singkat fungsi plugin"""
        ...
    
    @abstractmethod
    def process_frame(self, frame: np.ndarray) -> Dict[str, Any]:
        """
        Process single video frame
        
        Args:
            frame: RGB image array (H, W, 3)
            
        Returns:
            Dict dengan keys:
            - metrics: Dict[str, float] - nilai metrik
            - annotations: Optional[np.ndarray] - overlay visual
            - metadata: Dict[str, Any] - info tambahan
        """
        ...
    
    @abstractmethod
    def get_config_schema(self) -> Dict[str, Any]:
        """Return JSON Schema untuk validasi konfigurasi"""
        ...
    
    @abstractmethod
    def configure(self, config: Dict[str, Any]) -> None:
        """Apply konfigurasi ke plugin"""
        ...
    
    @abstractmethod
    def cleanup(self) -> None:
        """Cleanup resources saat plugin di-unload"""
        ...
```

##### 11.3 Plugin Registry Implementation

```python
# src/plugins/registry.py
import importlib.metadata
from typing import Dict, Type, List
import logging

class PluginRegistry:
    """
    Central registry untuk manage plugins
    (c) 2025 Radhitya Guntoro Adhi
    """
    
    def __init__(self):
        self._plugins: Dict[str, Type[VisionPlugin]] = {}
        self._instances: Dict[str, VisionPlugin] = {}
        self.logger = logging.getLogger(__name__)
    
    def discover_plugins(self) -> None:
        """Auto-discover plugins via entry points"""
        for entry_point in importlib.metadata.entry_points(
            group='icikiwir.plugins'
        ):
            try:
                plugin_class = entry_point.load()
                self.register(entry_point.name, plugin_class)
                self.logger.info(f"Loaded plugin: {entry_point.name}")
            except Exception as e:
                self.logger.error(f"Failed to load {entry_point.name}: {e}")
    
    def register(self, name: str, plugin_class: Type[VisionPlugin]) -> None:
        """Register plugin class"""
        self._plugins[name] = plugin_class
    
    def create_instance(self, name: str, config: Dict[str, Any]) -> VisionPlugin:
        """Create configured plugin instance"""
        if name not in self._plugins:
            raise ValueError(f"Plugin '{name}' not found")
        
        instance = self._plugins[name]()
        instance.configure(config)
        self._instances[name] = instance
        return instance
```

##### 11.4 Entry Points Configuration

```toml
# Contoh pyproject.toml untuk plugin eksternal
[project.entry-points."icikiwir.plugins"]
my_emotion_detector = "my_plugin.emotion:EmotionDetectorPlugin"
my_gesture_tracker = "my_plugin.gesture:GestureTrackerPlugin"
```

### 2. Buat File Baru `docs/PLUGIN_GUIDE.md`

#### Struktur konten:

```markdown
# Panduan Membuat Plugin ICikiwir
(c) 2025 Radhitya Guntoro Adhi

## 📋 Daftar Isi

1. [Pendahuluan](#pendahuluan)
2. [Kapan Membuat Plugin](#kapan-membuat-plugin)
3. [Setup Project](#setup-project)
4. [Implementasi Plugin](#implementasi-plugin)
5. [Testing](#testing)
6. [Packaging & Publishing](#packaging-publishing)
7. [Aktivasi di ICikiwir](#aktivasi-di-icikiwir)
8. [Troubleshooting](#troubleshooting)

## 1. Pendahuluan

Plugin system ICikiwir memungkinkan Anda menambah fungsionalitas tanpa mengubah core code...

## 2. Kapan Membuat Plugin

**Buat plugin eksternal jika:**
- Fungsionalitas spesifik untuk use case tertentu
- Menggunakan model/library proprietary
- Ingin distribute terpisah dari ICikiwir core

**Buat modul internal jika:**
- Fungsionalitas umum yang bermanfaat untuk semua user
- Perbaikan/enhancement core features
- Tidak ada dependency eksternal yang berat

## 3. Setup Project

### Struktur Folder
```
my-icikiwir-plugin/
├── src/
│   └── my_plugin/
│       ├── __init__.py
│       ├── emotion.py
│       └── models/
├── tests/
│   └── test_emotion.py
├── pyproject.toml
├── README.md
└── LICENSE
```

### pyproject.toml Template
```toml
[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[project]
name = "icikiwir-emotion-plugin"
version = "0.1.0"
authors = [{name = "Your Name", email = "<EMAIL>"}]
description = "Custom emotion detector for ICikiwir"
dependencies = [
    "numpy>=1.24.0",
    "opencv-python>=4.8.0",
    "onnxruntime>=1.16.0"
]

[project.entry-points."icikiwir.plugins"]
custom_emotion = "my_plugin.emotion:EmotionDetectorPlugin"
```

## 4. Implementasi Plugin

### Contoh Lengkap
```python
# src/my_plugin/emotion.py
from typing import Dict, Any, Optional
import numpy as np
import cv2
import onnxruntime as ort

class EmotionDetectorPlugin:
    """
    Custom emotion detector using ONNX model
    """
    
    def __init__(self):
        self._model: Optional[ort.InferenceSession] = None
        self._config: Dict[str, Any] = {}
    
    @property
    def name(self) -> str:
        return "custom_emotion_detector"
    
    @property
    def version(self) -> str:
        return "0.1.0"
    
    @property
    def description(self) -> str:
        return "Deteksi 7 emosi dasar menggunakan custom CNN model"
    
    def get_config_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "model_path": {"type": "string"},
                "confidence_threshold": {"type": "number", "minimum": 0, "maximum": 1},
                "face_min_size": {"type": "integer", "minimum": 20}
            },
            "required": ["model_path"]
        }
    
    def configure(self, config: Dict[str, Any]) -> None:
        self._config = config
        self._model = ort.InferenceSession(config["model_path"])
    
    def process_frame(self, frame: np.ndarray) -> Dict[str, Any]:
        # Detect faces
        faces = self._detect_faces(frame)
        
        emotions = []
        for face in faces:
            # Preprocess
            face_img = self._preprocess_face(face)
            
            # Inference
            outputs = self._model.run(None, {"input": face_img})
            emotion_probs = outputs[0][0]
            
            # Parse results
            emotion_labels = ["angry", "disgust", "fear", "happy", "sad", "surprise", "neutral"]
            max_idx = np.argmax(emotion_probs)
            
            emotions.append({
                "label": emotion_labels[max_idx],
                "confidence": float(emotion_probs[max_idx])
            })
        
        return {
            "metrics": {
                "emotion_count": len(emotions),
                "dominant_emotion": emotions[0]["label"] if emotions else "none"
            },
            "metadata": {
                "emotions": emotions,
                "face_count": len(faces)
            }
        }
    
    def cleanup(self) -> None:
        self._model = None
```

## 5. Testing

### Unit Test Example
```python
# tests/test_emotion.py
import pytest
import numpy as np
from my_plugin.emotion import EmotionDetectorPlugin

def test_plugin_properties():
    plugin = EmotionDetectorPlugin()
    assert plugin.name == "custom_emotion_detector"
    assert plugin.version == "0.1.0"

def test_process_frame_empty():
    plugin = EmotionDetectorPlugin()
    plugin.configure({"model_path": "tests/mock_model.onnx"})
    
    # Black frame
    frame = np.zeros((480, 640, 3), dtype=np.uint8)
    result = plugin.process_frame(frame)
    
    assert result["metrics"]["emotion_count"] == 0
    assert result["metrics"]["dominant_emotion"] == "none"
```

## 6. Packaging & Publishing

### Build Package
```bash
pip install build
python -m build
```

### Upload ke PyPI
```bash
pip install twine
twine upload dist/*
```

## 7. Aktivasi di ICikiwir

### Install Plugin
```bash
pip install icikiwir-emotion-plugin
```

### Enable via CLI
```bash
python -m icikiwir --enable-plugin custom_emotion --plugin-config emotion_config.yaml
```

### Config File Example
```yaml
# emotion_config.yaml
custom_emotion:
  model_path: "/path/to/emotion_model.onnx"
  confidence_threshold: 0.7
  face_min_size: 30
```

## 8. Troubleshooting

### Plugin Tidak Terdeteksi
- Pastikan entry point terdaftar di pyproject.toml
- Cek `pip show <package>` untuk konfirmasi instalasi
- Jalankan `python -m icikiwir --list-plugins`

### Import Error
- Verifikasi semua dependencies terinstall
- Cek compatibility dengan ICikiwir version

### Performance Issues
- Profile dengan cProfile
- Gunakan threading untuk heavy processing
- Cache hasil yang expensive
```

### 3. Update `CONTRIBUTING.md`

**Lokasi**: Setelah section "Development Roadmap" (line 332)

**Tambahkan section baru:**

```markdown
## 🔌 Menambah Modul Baru

### Modul Internal vs Plugin Eksternal

Sebelum menambah fungsionalitas baru, tentukan apakah sebaiknya dibuat sebagai:

**Modul Internal** - Jika:
- Fungsionalitas core yang dibutuhkan mayoritas user
- Terintegrasi erat dengan komponen existing
- Tidak memerlukan dependency eksternal yang besar

**Plugin Eksternal** - Jika:
- Fungsionalitas opsional/spesifik
- Menggunakan model atau library proprietary
- Ingin maintain/distribute terpisah

### Panduan Lengkap

Untuk panduan detail membuat plugin eksternal, lihat [Plugin Development Guide](docs/PLUGIN_GUIDE.md).

### Quick Start Plugin Development

```bash
# Clone template
git clone https://github.com/icikiwir/plugin-template my-plugin
cd my-plugin

# Setup environment
python -m venv venv
source venv/bin/activate
pip install -e .

# Run tests
pytest tests/
```
```

## 📊 Estimasi Waktu

- Update `docs/ARCHITECTURE.md`: 30 menit
- Buat `docs/PLUGIN_GUIDE.md`: 45 menit
- Update `CONTRIBUTING.md`: 15 menit
- Review & Polish: 30 menit

**Total: ~2 jam**

## ✅ Checklist Implementasi

- [ ] Tambah Section 11 di ARCHITECTURE.md
- [ ] Buat file PLUGIN_GUIDE.md lengkap
- [ ] Update CONTRIBUTING.md dengan link
- [ ] Pastikan semua code examples valid Python
- [ ] Cek konsistensi bahasa (Indonesia untuk docs, English untuk code)
- [ ] Verifikasi semua credit ke Radhitya Guntoro Adhi

## 🚀 Next Steps

Setelah review rencana ini, kita akan switch ke mode `code` untuk implementasi aktual.