# Panduan Membuat Plugin ICikiwir
(c) 2025 Ra<PERSON>tya Guntoro Adhi

## 📋 Daftar Isi

1. [Pendahuluan](#1-pendahuluan)
2. [<PERSON><PERSON> Membuat Plugin](#2-kapan-membuat-plugin)
3. [Setup Project](#3-setup-project)
4. [Implementasi Plugin](#4-implementasi-plugin)
5. [Testing](#5-testing)
6. [Packaging & Publishing](#6-packaging--publishing)
7. [Aktivasi di ICikiwir](#7-aktivasi-di-icikiwir)
8. [Troubleshooting](#8-troubleshooting)

## 1. Pendahuluan

Plugin system ICikiwir memungkinkan Anda menambah fungsionalitas tanpa mengubah core code. Sistem ini menggunakan Python entry points untuk auto-discovery dan Protocol typing untuk interface contracts yang jelas.

### Keuntungan Plugin System

- **Modular**: Fungsionalitas terpisah dari core aplikasi
- **Extensible**: Mudah menambah capabilities baru
- **Maintainable**: Plugin dapat diupdate independen
- **Distributable**: Dapat dipublish ke PyPI secara terpisah

## 2. <PERSON><PERSON> Membuat Plugin

### Buat Plugin Eksternal Jika:

- ✅ Fungsionalitas spesifik untuk use case tertentu
- ✅ Menggunakan model/library proprietary
- ✅ Ingin distribute terpisah dari ICikiwir core
- ✅ Memerlukan dependency yang berat/opsional
- ✅ Experimental features yang belum stable

### Buat Modul Internal Jika:

- ✅ Fungsionalitas umum yang bermanfaat untuk semua user
- ✅ Perbaikan/enhancement core features
- ✅ Tidak ada dependency eksternal yang berat
- ✅ Terintegrasi erat dengan komponen existing

## 3. Setup Project

### 3.1 Struktur Folder

```
my-icikiwir-plugin/
├── src/
│   └── my_plugin/
│       ├── __init__.py
│       ├── emotion.py          # Vision plugin
│       ├── analyzer.py         # Analytics plugin
│       ├── notifier.py         # Intervention plugin
│       └── models/
│           └── emotion_model.onnx
├── tests/
│   ├── __init__.py
│   ├── test_emotion.py
│   ├── test_analyzer.py
│   └── fixtures/
│       └── test_image.jpg
├── docs/
│   └── README.md
├── pyproject.toml
├── README.md
├── LICENSE
└── .gitignore
```

### 3.2 pyproject.toml Template

```toml
[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "icikiwir-emotion-plugin"
version = "0.1.0"
authors = [
    {name = "Your Name", email = "<EMAIL>"}
]
description = "Custom emotion detector plugin untuk ICikiwir"
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.10"
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
]
dependencies = [
    "numpy>=1.24.0",
    "opencv-python>=4.8.0",
    "onnxruntime>=1.16.0",
    "pydantic>=2.0.0"
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "mypy>=1.0.0"
]

[project.entry-points."icikiwir.plugins"]
custom_emotion = "my_plugin.emotion:EmotionDetectorPlugin"
custom_analyzer = "my_plugin.analyzer:CustomAnalyticsPlugin"
custom_notifier = "my_plugin.notifier:CustomNotifierPlugin"

[project.urls]
Homepage = "https://github.com/yourusername/icikiwir-emotion-plugin"
Repository = "https://github.com/yourusername/icikiwir-emotion-plugin"
Issues = "https://github.com/yourusername/icikiwir-emotion-plugin/issues"

[tool.setuptools.packages.find]
where = ["src"]

[tool.black]
line-length = 100
target-version = ['py310']

[tool.isort]
profile = "black"
line_length = 100

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
```

### 3.3 Setup Development Environment

```bash
# Clone template atau buat folder baru
mkdir my-icikiwir-plugin
cd my-icikiwir-plugin

# Setup virtual environment
python -m venv venv
source venv/bin/activate  # Linux/Mac
# atau
venv\Scripts\activate     # Windows

# Install ICikiwir untuk development
pip install -e /path/to/icikiwir

# Install development dependencies
pip install -e ".[dev]"
```

## 4. Implementasi Plugin

### 4.1 Vision Plugin Example

```python
# src/my_plugin/emotion.py
from typing import Dict, Any, Optional
import numpy as np
import cv2
import onnxruntime as ort
from pathlib import Path
import logging

class EmotionDetectorPlugin:
    """
    Custom emotion detector menggunakan ONNX model
    (c) 2025 Radhitya Guntoro Adhi
    """
    
    def __init__(self):
        self._model: Optional[ort.InferenceSession] = None
        self._config: Dict[str, Any] = {}
        self._face_cascade = cv2.CascadeClassifier(
            cv2.data.haarcascades + 'haarcascade_frontalface_default.xml'
        )
        self.logger = logging.getLogger(__name__)
    
    @property
    def name(self) -> str:
        return "custom_emotion_detector"
    
    @property
    def version(self) -> str:
        return "0.1.0"
    
    @property
    def description(self) -> str:
        return "Deteksi 7 emosi dasar menggunakan custom CNN model"
    
    def get_config_schema(self) -> Dict[str, Any]:
        """JSON Schema untuk validasi konfigurasi"""
        return {
            "type": "object",
            "properties": {
                "model_path": {
                    "type": "string",
                    "description": "Path ke ONNX model file"
                },
                "confidence_threshold": {
                    "type": "number",
                    "minimum": 0,
                    "maximum": 1,
                    "default": 0.7,
                    "description": "Minimum confidence untuk deteksi"
                },
                "face_min_size": {
                    "type": "integer",
                    "minimum": 20,
                    "default": 30,
                    "description": "Minimum ukuran wajah untuk deteksi"
                },
                "enable_visualization": {
                    "type": "boolean",
                    "default": true,
                    "description": "Enable overlay visual pada frame"
                }
            },
            "required": ["model_path"]
        }
    
    def configure(self, config: Dict[str, Any]) -> None:
        """Apply konfigurasi ke plugin"""
        self._config = config
        
        # Load ONNX model
        model_path = Path(config["model_path"])
        if not model_path.exists():
            raise FileNotFoundError(f"Model file tidak ditemukan: {model_path}")
        
        try:
            self._model = ort.InferenceSession(str(model_path))
            self.logger.info(f"Model loaded: {model_path}")
        except Exception as e:
            raise RuntimeError(f"Gagal load model: {e}")
    
    def process_frame(self, frame: np.ndarray) -> Dict[str, Any]:
        """
        Process single video frame untuk deteksi emosi
        
        Args:
            frame: RGB image array (H, W, 3)
            
        Returns:
            Dict dengan hasil deteksi emosi
        """
        if self._model is None:
            raise RuntimeError("Plugin belum dikonfigurasi")
        
        # Convert ke grayscale untuk face detection
        gray = cv2.cvtColor(frame, cv2.COLOR_RGB2GRAY)
        
        # Detect faces
        faces = self._face_cascade.detectMultiScale(
            gray,
            scaleFactor=1.1,
            minNeighbors=5,
            minSize=(self._config.get("face_min_size", 30), 
                    self._config.get("face_min_size", 30))
        )
        
        emotions = []
        annotations = frame.copy() if self._config.get("enable_visualization", True) else None
        
        for (x, y, w, h) in faces:
            # Extract face region
            face_roi = gray[y:y+h, x:x+w]
            
            # Preprocess untuk model
            face_processed = self._preprocess_face(face_roi)
            
            # Inference
            try:
                outputs = self._model.run(None, {"input": face_processed})
                emotion_probs = outputs[0][0]
                
                # Parse hasil
                emotion_labels = ["marah", "jijik", "takut", "senang", "sedih", "terkejut", "netral"]
                max_idx = np.argmax(emotion_probs)
                confidence = float(emotion_probs[max_idx])
                
                if confidence >= self._config.get("confidence_threshold", 0.7):
                    emotion_result = {
                        "label": emotion_labels[max_idx],
                        "confidence": confidence,
                        "bbox": [int(x), int(y), int(w), int(h)]
                    }
                    emotions.append(emotion_result)
                    
                    # Add visualization
                    if annotations is not None:
                        self._draw_emotion_annotation(annotations, emotion_result)
                        
            except Exception as e:
                self.logger.error(f"Error dalam inference: {e}")
        
        # Hitung dominant emotion
        dominant_emotion = "netral"
        if emotions:
            dominant_emotion = max(emotions, key=lambda x: x["confidence"])["label"]
        
        return {
            "metrics": {
                "emotion_count": len(emotions),
                "dominant_emotion": dominant_emotion,
                "face_count": len(faces),
                "avg_confidence": np.mean([e["confidence"] for e in emotions]) if emotions else 0.0
            },
            "annotations": annotations,
            "metadata": {
                "emotions": emotions,
                "processing_time": 0.0  # Bisa ditambah timing
            }
        }
    
    def _preprocess_face(self, face_roi: np.ndarray) -> np.ndarray:
        """Preprocess face untuk model input"""
        # Resize ke ukuran yang diharapkan model (contoh: 48x48)
        face_resized = cv2.resize(face_roi, (48, 48))
        
        # Normalize
        face_normalized = face_resized.astype(np.float32) / 255.0
        
        # Add batch dimension dan channel dimension
        face_input = np.expand_dims(np.expand_dims(face_normalized, axis=0), axis=0)
        
        return face_input
    
    def _draw_emotion_annotation(self, frame: np.ndarray, emotion: Dict[str, Any]) -> None:
        """Draw emotion annotation pada frame"""
        x, y, w, h = emotion["bbox"]
        label = emotion["label"]
        confidence = emotion["confidence"]
        
        # Draw bounding box
        cv2.rectangle(frame, (x, y), (x+w, y+h), (0, 255, 0), 2)
        
        # Draw label
        text = f"{label}: {confidence:.2f}"
        cv2.putText(frame, text, (x, y-10), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
    
    def cleanup(self) -> None:
        """Cleanup resources"""
        if self._model:
            self._model = None
        self.logger.info("Plugin cleanup completed")
```

### 4.2 Analytics Plugin Example

```python
# src/my_plugin/analyzer.py
from typing import Dict, Any
import numpy as np
from collections import deque
import time

class CustomAnalyticsPlugin:
    """
    Custom analytics untuk emotion trend analysis
    (c) 2025 Radhitya Guntoro Adhi
    """
    
    def __init__(self):
        self._config: Dict[str, Any] = {}
        self._emotion_history = deque(maxlen=100)  # Keep last 100 readings
        self._start_time = time.time()
    
    @property
    def name(self) -> str:
        return "custom_emotion_analyzer"
    
    @property
    def version(self) -> str:
        return "0.1.0"
    
    @property
    def description(self) -> str:
        return "Analisis trend emosi dan pattern recognition"
    
    def get_config_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "window_size": {
                    "type": "integer",
                    "minimum": 10,
                    "maximum": 500,
                    "default": 100
                },
                "trend_threshold": {
                    "type": "number",
                    "minimum": 0.1,
                    "maximum": 1.0,
                    "default": 0.3
                }
            }
        }
    
    def configure(self, config: Dict[str, Any]) -> None:
        self._config = config
        window_size = config.get("window_size", 100)
        self._emotion_history = deque(maxlen=window_size)
    
    def analyze_metrics(self, metrics: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze emotion metrics dan return insights
        """
        current_time = time.time()
        
        # Store current emotion
        if "dominant_emotion" in metrics:
            self._emotion_history.append({
                "emotion": metrics["dominant_emotion"],
                "confidence": metrics.get("avg_confidence", 0.0),
                "timestamp": current_time
            })
        
        # Calculate trends
        trends = self._calculate_emotion_trends()
        patterns = self._detect_patterns()
        
        return {
            "emotion_trends": trends,
            "detected_patterns": patterns,
            "session_duration": current_time - self._start_time,
            "total_readings": len(self._emotion_history)
        }
    
    def _calculate_emotion_trends(self) -> Dict[str, Any]:
        """Calculate emotion distribution dan trends"""
        if len(self._emotion_history) < 5:
            return {"status": "insufficient_data"}
        
        # Count emotion frequencies
        emotion_counts = {}
        recent_emotions = list(self._emotion_history)[-20:]  # Last 20 readings
        
        for reading in recent_emotions:
            emotion = reading["emotion"]
            emotion_counts[emotion] = emotion_counts.get(emotion, 0) + 1
        
        # Calculate percentages
        total = len(recent_emotions)
        emotion_percentages = {
            emotion: (count / total) * 100 
            for emotion, count in emotion_counts.items()
        }
        
        return {
            "distribution": emotion_percentages,
            "dominant_recent": max(emotion_counts, key=emotion_counts.get),
            "stability_score": self._calculate_stability_score()
        }
    
    def _detect_patterns(self) -> Dict[str, Any]:
        """Detect emotion patterns"""
        if len(self._emotion_history) < 10:
            return {"patterns": []}
        
        patterns = []
        
        # Detect stress pattern (rapid emotion changes)
        if self._detect_stress_pattern():
            patterns.append({
                "type": "stress_indicator",
                "description": "Perubahan emosi yang cepat terdeteksi",
                "confidence": 0.8
            })
        
        # Detect focus decline (increasing negative emotions)
        if self._detect_focus_decline():
            patterns.append({
                "type": "focus_decline",
                "description": "Penurunan fokus terdeteksi",
                "confidence": 0.7
            })
        
        return {"patterns": patterns}
    
    def _calculate_stability_score(self) -> float:
        """Calculate emotional stability score (0-1)"""
        if len(self._emotion_history) < 5:
            return 0.5
        
        # Count unique emotions in recent history
        recent_emotions = [r["emotion"] for r in list(self._emotion_history)[-10:]]
        unique_emotions = len(set(recent_emotions))
        
        # More unique emotions = less stable
        stability = max(0.0, 1.0 - (unique_emotions / 7.0))  # 7 total emotions
        return stability
    
    def _detect_stress_pattern(self) -> bool:
        """Detect rapid emotion changes indicating stress"""
        if len(self._emotion_history) < 10:
            return False
        
        recent = list(self._emotion_history)[-10:]
        changes = 0
        
        for i in range(1, len(recent)):
            if recent[i]["emotion"] != recent[i-1]["emotion"]:
                changes += 1
        
        # If more than 60% of readings are different emotions
        return (changes / len(recent)) > 0.6
    
    def _detect_focus_decline(self) -> bool:
        """Detect increasing negative emotions"""
        if len(self._emotion_history) < 15:
            return False
        
        negative_emotions = {"marah", "jijik", "takut", "sedih"}
        recent = list(self._emotion_history)[-15:]
        
        # Count negative emotions in first half vs second half
        mid = len(recent) // 2
        first_half_negative = sum(1 for r in recent[:mid] if r["emotion"] in negative_emotions)
        second_half_negative = sum(1 for r in recent[mid:] if r["emotion"] in negative_emotions)
        
        # Focus decline if negative emotions increased significantly
        return second_half_negative > first_half_negative * 1.5
    
    def cleanup(self) -> None:
        """Cleanup resources"""
        self._emotion_history.clear()
```

### 4.3 Intervention Plugin Example

```python
# src/my_plugin/notifier.py
from typing import Dict, Any
import requests
import json
import logging

class CustomNotifierPlugin:
    """
    Custom notifier untuk smart home integration
    (c) 2025 Radhitya Guntoro Adhi
    """
    
    def __init__(self):
        self._config: Dict[str, Any] = {}
        self.logger = logging.getLogger(__name__)
    
    @property
    def name(self) -> str:
        return "smart_home_notifier"
    
    @property
    def version(self) -> str:
        return "0.1.0"
    
    @property
    def description(self) -> str:
        return "Integrasi dengan smart home devices untuk intervention"
    
    def get_config_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "webhook_url": {
                    "type": "string",
                    "format": "uri",
                    "description": "URL webhook untuk smart home hub"
                },
                "light_device_id": {
                    "type": "string",
                    "description": "ID device lampu pintar"
                },
                "enable_light_feedback": {
                    "type": "boolean",
                    "default": true
                }
            },
            "required": ["webhook_url"]
        }
    
    def configure(self, config: Dict[str, Any]) -> None:
        self._config = config
    
    def trigger_intervention(self, context: Dict[str, Any]) -> bool:
        """
        Trigger intervention berdasarkan context
        """
        try:
            intervention_type = context.get("type", "unknown")
            
            if intervention_type == "focus_decline":
                return self._handle_focus_decline(context)
            elif intervention_type == "stress_detected":
                return self._handle_stress_detection(context)
            elif intervention_type == "break_reminder":
                return self._handle_break_reminder(context)
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error dalam intervention: {e}")
            return False
    
    def _handle_focus_decline(self, context: Dict[str, Any]) -> bool:
        """Handle focus decline dengan gentle lighting"""
        if not self._config.get("enable_light_feedback", True):
            return True
        
        payload = {
            "device_id": self._config.get("light_device_id"),
            "action": "adjust_brightness",
            "params": {
                "brightness": 80,  # Slightly brighter
                "color": "#FFE4B5",  # Warm white
                "duration": 5  # 5 seconds transition
            },
            "reason": "focus_support"
        }
        
        return self._send_webhook(payload)
    
    def _handle_stress_detection(self, context: Dict[str, Any]) -> bool:
        """Handle stress dengan calming colors"""
        payload = {
            "device_id": self._config.get("light_device_id"),
            "action": "breathing_effect",
            "params": {
                "color": "#87CEEB",  # Sky blue
                "duration": 30,  # 30 seconds
                "breathing_rate": 6  # 6 breaths per minute
            },
            "reason": "stress_relief"
        }
        
        return self._send_webhook(payload)
    
    def _handle_break_reminder(self, context: Dict[str, Any]) -> bool:
        """Handle break reminder dengan subtle notification"""
        payload = {
            "device_id": self._config.get("light_device_id"),
            "action": "pulse",
            "params": {
                "color": "#98FB98",  # Pale green
                "pulses": 3,
                "interval": 1  # 1 second between pulses
            },
            "reason": "break_reminder"
        }
        
        return self._send_webhook(payload)
    
    def _send_webhook(self, payload: Dict[str, Any]) -> bool:
        """Send webhook ke smart home hub"""
        try:
            response = requests.post(
                self._config["webhook_url"],
                json=payload,
                timeout=5
            )
            
            if response.status_code == 200:
                self.logger.info(f"Webhook sent successfully: {payload['reason']}")
                return True
            else:
                self.logger.warning(f"Webhook failed: {response.status_code}")
                return False
                
        except requests.RequestException as e:
            self.logger.error(f"Webhook request failed: {e}")
            return False
    
    def cleanup(self) -> None:
        """Cleanup resources"""
        # Turn off any active effects
        if self._config.get("light_device_id"):
            payload = {
                "device_id": self._config["light_device_id"],
                "action": "reset",
                "reason": "plugin_cleanup"
            }
            self._send_webhook(payload)
```

## 5. Testing

### 5.1 Unit Test Example

```python
# tests/test_emotion.py
import pytest
import numpy as np
from unittest.mock import Mock, patch
from my_plugin.emotion import EmotionDetectorPlugin

class TestEmotionDetectorPlugin:
    
    def setup_method(self):
        self.plugin = EmotionDetectorPlugin()
    
    def test_plugin_properties(self):
        """Test basic plugin properties"""
        assert self.plugin.name == "custom_emotion_detector"
        assert self.plugin.version == "0.1.0"
        assert "emosi" in self.plugin.description.lower()
    
    def test_config_schema(self):
        """Test configuration schema"""
        schema = self.plugin.get_config_schema()
        assert "model_path" in schema["required"]
        assert schema["properties"]["confidence_threshold"]["minimum"] == 0
        assert schema["properties"]["confidence_threshold"]["maximum"] == 1
    
    @patch('onnxruntime.InferenceSession')
    def test_configure_success(self, mock_ort):
        """Test successful configuration"""
        config = {
            "model_path": "tests/fixtures/mock_model.onnx",
            "confidence_threshold": 0.8
        }
        
        # Mock model file existence
        with patch('pathlib.Path.exists', return_value=True):
            self.plugin.configure(config)
            assert self.plugin._config == config
            mock_ort.assert_called_once()
    
    def test_configure_missing_model(self):
        """Test configuration dengan model file tidak ada"""
        config = {"model_path": "nonexistent_model.onnx"}
        
        with pytest.raises(FileNotFoundError):
            self.plugin.configure(config)
    
    @patch('cv2.CascadeClassifier.detectMultiScale')
    @patch('onnxruntime.InferenceSession')
    def test_process_frame_no_faces(self, mock_ort, mock_detect):
        """Test process frame tanpa wajah terdeteksi"""
        # Setup
        mock_detect.return_value = []  # No faces detected
        
        with patch('pathlib.Path.exists', return_value=True):
            self.plugin.configure({"model_path": "mock.onnx"})
        
        # Test
        frame = np.zeros((480, 640, 3), dtype=np.uint8)
        result = self.plugin.process_frame(frame)
        
        # Assertions
        assert result["metrics"]["face_count"] == 0
        assert result["metrics"]["emotion_count"] == 0
        assert result["metrics"]["dominant_emotion"] == "netral"
    
    @patch('cv2.CascadeClassifier.detectMultiScale')
    @patch('onnxruntime.InferenceSession')
    def test_process_frame_with_faces(self, mock_ort, mock_detect):
        """Test process frame dengan wajah terdeteksi"""
        # Setup mocks
        mock_detect.return_value = [(100, 100, 50, 50)]  # One face
        mock_session = Mock()
        mock_session.run.return_value = [np.array([[0.1, 0.1, 0.1, 0.9, 0.1, 0.1, 0.1]])]
        mock_ort.return_value = mock_session
        
        with patch('pathlib.Path.exists', return_value=True):
            self.plugin.configure({
                "model_path": "mock.onnx",
                "confidence_threshold": 0.7
            })
        
        # Test
        frame = np.ones((480, 640, 3), dtype=np.uint8) * 128
        result = self.plugin.process_frame(frame)
        
        # Assertions
        assert result["metrics"]["face_count"] == 1
        assert result["metrics"]["emotion_count"] == 1
        assert result["metrics"]["dominant_emotion"] == "senang"
        assert len(result["metadata"]["emotions"]) == 1
    
    def test_cleanup(self):
        """Test cleanup functionality"""
        with patch('pathlib.Path.exists', return_value=True):
            with patch('onnxruntime.InferenceSession'):
                self.plugin.configure({"model_path": "mock.onnx"})
        
        self.plugin.cleanup()
        assert self.plugin._model is None

# tests/test_analyzer.py
import pytest
from my_plugin.analyzer import CustomAnalyticsPlugin

class TestCustomAnalyticsPlugin:
    
    def setup_method(self):
        self.plugin = CustomAnalyticsPlugin()
        self.plugin.configure({"window_size": 20})
    
    def test_analyze_insufficient_data(self):
        """Test analysis dengan data tidak cukup"""
        metrics = {"dominant_emotion": "senang", "avg_confidence": 0.8}
        result = self.plugin.analyze_metrics(metrics)
        
        assert result["emotion_trends"]["status"] == "insufficient_data"
    
    def test_analyze_with_data(self):
        """Test analysis dengan data cukup"""
        # Add multiple readings
        emotions = ["senang", "senang", "netral", "senang", "sedih"] * 3
        
        for emotion in emotions:
            metrics = {"dominant_emotion": emotion, "avg_confidence": 0.8}
            self.plugin.analyze_metrics(metrics)
        
        # Final analysis
        result = self.plugin.analyze_metrics({"dominant_emotion": "senang", "avg_confidence": 0.8})
        
        assert "distribution" in result["emotion_trends"]
        assert result["emotion_trends"]["dominant_recent"] == "senang"
        assert result["total_readings"] > 0
```

### 5.2 Integration Test

```python
# tests/test_integration.py
import pytest
from unittest.mock import patch, Mock
from my_plugin.emotion import EmotionDetectorPlugin
from my_plugin.analyzer import CustomAnalyticsPlugin

class TestPluginIntegration:
    
    def setup_method(self):
        self.emotion_plugin = EmotionDetectorPlugin()
        self.analyzer_plugin = CustomAnalyticsPlugin()
    
    @patch('onnxruntime.InferenceSession')
    @patch('cv2.CascadeClassifier.detectMultiScale')
    def test_emotion_to_analyzer_pipeline(self, mock_detect, mock_ort):
        """Test pipeline dari emotion detection ke analysis"""
        # Setup emotion plugin
        mock_detect.return_value = [(100, 100, 50, 50)]
        mock_session = Mock()
        mock_session.run.return_value = [np.array([[0.1, 0.1, 0.1, 0.8, 0.1, 0.1, 0.1]])]
        mock_ort.return_value = mock_session
        
        with patch('pathlib.Path.exists', return_value=True):
            self.emotion_plugin.configure({"model_path": "mock.onnx"})
            self.analyzer_plugin.configure({"window_size": 10})
        
        # Process frame
        frame = np.ones((480, 640, 3), dtype=np.uint8) * 128
        emotion_result = self.emotion_plugin.process_frame(frame)
        
        # Analyze hasil emotion
        analysis_result = self.analyzer_plugin.analyze_metrics(emotion_result["metrics"])
        
        # Assertions
        assert "emotion_trends" in analysis_result
        assert "session_duration" in analysis_result
```

### 5.3 Performance Test

```python
# tests/test_performance.py
import pytest
import time
import numpy as np
from unittest.mock import patch, Mock
from my_plugin.emotion import EmotionDetectorPlugin

class TestPerformance:
    
    @patch('onnxruntime.InferenceSession')
    @patch('cv2.CascadeClassifier.detectMultiScale')
    def test_processing_speed(self, mock_detect, mock_ort):
        """Test processing speed requirement"""
        plugin = EmotionDetectorPlugin()
        
        # Setup mocks
        mock_detect.return_value = [(100, 100, 50, 50)]
        mock_session = Mock()
        mock_session.run.return_value = [np.array([[0.1, 0.1, 0.1, 0.8, 0.1, 0.1, 0.1]])]
        mock_ort.return_value = mock_session
        
        with patch('pathlib.Path.exists', return_value=True):
            plugin.configure({"model_path": "mock.onnx"})
        
        # Test processing time
        frame = np.ones((480, 640, 3), dtype=np.uint8) * 128
        
        start_time = time.time()
        for _ in range(10):
            result = plugin.process_frame(frame)
        end_time = time.time()
        
        avg_time = (end_time - start_time) / 10
        
        # Should process frame dalam < 100ms
        assert avg_time < 0.1, f"Processing too slow: {avg_time:.3f}s"
```

## 6. Packaging & Publishing

### 6.1 Build Package

```bash
# Install build tools
pip install build twine

# Build package
python -m build

# Hasil akan ada di dist/
# - icikiwir-emotion-plugin-0.1.0.tar.gz
# - icikiwir_emotion_plugin-0.1.0-py3-none-any.whl
```

### 6.2 Test Package Locally

```bash
# Install dari wheel file
pip install dist/icikiwir_emotion_plugin-0.1.0-py3-none-any.whl

# Test plugin discovery
python -c "
import importlib.metadata
for ep in importlib.metadata.entry_points(group='icikiwir.plugins'):
    print(f'Found plugin: {ep.name} -> {ep.value}')
"
```

### 6.3 Upload ke PyPI

```bash
# Upload ke TestPyPI dulu
twine upload --repository testpypi dist/*

# Test install dari TestPyPI
pip install --index-url https://test.pypi.org/simple/ icikiwir-emotion-plugin

# Jika OK, upload ke PyPI production
twine upload dist/*
```

### 6.4 README.md Template

```markdown
# ICikiwir Emotion Plugin

Custom emotion detection plugin untuk ICikiwir Cognitive-Aware Study Companion.

## Features

- 7 emotion detection (marah, jijik, takut, senang, sedih, terkejut, netral)
- Real-time processing dengan ONNX runtime
- Configurable confidence threshold
- Visual annotations support

## Installation

```bash
pip install icikiwir-emotion-plugin
```

## Usage

```bash
# Enable plugin
python -m icikiwir --enable-plugin custom_emotion --plugin-config emotion_config.yaml
```

## Configuration

```yaml
custom_emotion:
  model_path: "/path/to/emotion_model.onnx"
  confidence_threshold: 0.7
  face_min_size: 30
  enable_visualization: true
```

## Requirements

- Python >= 3.10
- ICikiwir >= 0.1.0
- OpenCV >= 4.8.0
- ONNX Runtime >= 1.16.0

## License

MIT License - (c) 2025 Your Name
```

## 7. Aktivasi di ICikiwir

### 7.1 Install Plugin

```bash
# Install dari PyPI
pip install icikiwir-emotion-plugin

# Atau install dari source
git clone https://github.com/yourusername/icikiwir-emotion-plugin
cd icikiwir-emotion-plugin
pip install -e .
```

### 7.2 Verify Installation

```bash
# List available plugins
python -m icikiwir --list-plugins

# Output:
# Available plugins:
# Vision Plugins:
#   - custom_emotion (v0.1.0): Deteksi 7 emosi dasar menggunakan custom CNN model
# Analytics Plugins:
#   - custom_analyzer (v0.1.0): Analisis trend emosi dan pattern recognition
# Intervention Plugins:
#   - smart_home_notifier (v0.1.0): Integrasi dengan smart home devices
```

### 7.3 Configuration File

```yaml
# config/plugins.yaml
plugins:
  enabled:
    - custom_emotion
    - custom_analyzer
    - smart_home_notifier
  
  configs:
    custom_emotion:
      model_path: "models/emotion_detector.onnx"
      confidence_threshold: 0.75
      face_min_size: 40
      enable_visualization: true
    
    custom_analyzer:
      window_size: 50
      trend_threshold: 0.4
    
    smart_home_notifier:
      webhook_url: "http://*************:8080/webhook"
      light_device_id: "living_room_light"
      enable_light_feedback: true
```

### 7.4 Run dengan Plugins

```bash
# Enable specific plugins
python -m icikiwir --enable-plugin custom_emotion,custom_analyzer

# Run dengan config file
python -m icikiwir --plugin-config config/plugins.yaml

# Run dengan debug mode
python -m icikiwir --plugin-config config/plugins.yaml --debug
```

### 7.5 Plugin Management via GUI

ICikiwir GUI akan memiliki Plugin Manager untuk:

- ✅ List installed plugins
- ✅ Enable/disable plugins
- ✅ Configure plugin settings
- ✅ View plugin status dan logs
- ✅ Update plugins

## 8. Troubleshooting

### 8.1 Plugin Tidak Terdeteksi

**Problem**: Plugin tidak muncul di `--list-plugins`

**Solutions**:
```bash
# 1. Cek instalasi package
pip show icikiwir-emotion-plugin

# 2. Cek entry points
python -c "
import importlib.metadata
eps = importlib.metadata.entry_points(group='icikiwir.plugins')
print([ep.name for ep in eps])
"

# 3. Reinstall plugin
pip uninstall icikiwir-emotion-plugin
pip install icikiwir-emotion-plugin
```

### 8.2 Import Error

**Problem**: `ModuleNotFoundError` saat load plugin

**Solutions**:
```bash
# 1. Cek dependencies
pip check

# 2. Install missing dependencies
pip install -r requirements.txt

# 3. Cek Python path
python -c "import sys; print(sys.path)"
```

### 8.3 Configuration Error

**Problem**: Plugin gagal configure

**Solutions**:
```bash
# 1. Validate config schema
python -c "
from my_plugin.emotion import EmotionDetectorPlugin
import json
plugin = EmotionDetectorPlugin()
schema = plugin.get_config_schema()
print(json.dumps(schema, indent=2))
"

# 2. Test config file
python -c "
import yaml
with open('config/plugins.yaml') as f:
    config = yaml.safe_load(f)
print(config)
"
```

### 8.4 Performance Issues

**Problem**: Plugin processing terlalu lambat

**Solutions**:
```python
# 1. Profile plugin
import cProfile
import pstats

def profile_plugin():
    # Your plugin code here
    pass

cProfile.run('profile_plugin()', 'plugin_profile.stats')
stats = pstats.Stats('plugin_profile.stats')
stats.sort_stats('cumulative').print_stats(10)
```

```bash
# 2. Monitor resource usage
python -m icikiwir --plugin-config config/plugins.yaml --monitor-resources
```

### 8.5 Model Loading Issues

**Problem**: ONNX model gagal load

**Solutions**:
```python
# 1. Test model file
import onnxruntime as ort
try:
    session = ort.InferenceSession("path/to/model.onnx")
    print("Model loaded successfully")
    print(f"Input: {session.get_inputs()[0].name}")
    print(f"Output: {session.get_outputs()[0].name}")
except Exception as e:
    print(f"Model error: {e}")
```

```bash
# 2. Check model compatibility
python -c "
import onnxruntime as ort
print(f'ONNX Runtime version: {ort.__version__}')
print(f'Available providers: {ort.get_available_providers()}')
"
```

### 8.6 Logging dan Debugging

```python
# Enable debug logging
import logging
logging.basicConfig(level=logging.DEBUG)

# Plugin-specific logging
logger = logging.getLogger('my_plugin')
logger.setLevel(logging.DEBUG)

# Add file handler
handler = logging.FileHandler('plugin_debug.log')
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
handler.setFormatter(formatter)
logger.addHandler(handler)
```

### 8.7 Common Error Messages

| Error | Cause | Solution |
|-------|-------|----------|
| `Plugin 'xxx' not found` | Plugin tidak terinstall atau entry point salah | Reinstall plugin, cek pyproject.toml |
| `Model file not found` | Path model salah | Cek path di config, download model |
| `Configuration validation failed` | Config tidak sesuai schema | Validate config dengan schema |
| `ONNX Runtime error` | Model incompatible atau corrupt | Test model secara terpisah |
| `Webhook timeout` | Network issue atau endpoint down | Cek koneksi, test endpoint |

---

**Selamat! Anda sudah siap membuat plugin ICikiwir yang powerful dan extensible.**

Untuk pertanyaan lebih lanjut, silakan buka issue di [GitHub repository](https://github.com/radhityaguntoro/icikiwir) atau hubungi maintainer.

(c) 2025 Radhitya Guntoro Adhi