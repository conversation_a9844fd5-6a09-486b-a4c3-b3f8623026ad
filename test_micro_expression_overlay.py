#!/usr/bin/env python3
"""
Test script untuk memverifikasi overlay mikro ekspresi
Menjalankan webcam dan menampilkan overlay dalam window OpenCV
"""

import cv2
import time
import sys
import os

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from capture.stream import WebcamStream
from vision.face_tracker import FaceTracker

def main():
    """Test overlay mikro ekspresi"""
    print("🎥 Testing Micro Expression Overlay...")
    print("Tekan 'q' untuk keluar, 'r' untuk reset kalibrasi")
    
    # Initialize components
    webcam = WebcamStream()
    face_tracker = FaceTracker()
    
    try:
        # Start webcam
        if not webcam.start():
            print("❌ Gagal memulai webcam")
            return
        
        print("✅ Webcam started")
        print("📊 Tunggu 30 frame untuk kalibrasi mikro ekspresi...")
        
        frame_count = 0
        
        while True:
            # Get frame
            frame = webcam.get_frame()
            if frame is None:
                continue
            
            frame_count += 1
            
            # Process frame
            results = face_tracker.process_frame(frame)
            
            # Get annotated frame
            annotated_frame = results.get('frame_with_annotations', frame)
            
            # Show additional info
            if 'dominant_expression' in results:
                emotion, intensity = results['dominant_expression']
                print(f"Frame {frame_count}: {emotion} ({intensity:.2f})", end='\r')
            
            # Display frame
            cv2.imshow('Micro Expression Test', annotated_frame)
            
            # Handle key press
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
            elif key == ord('r'):
                print("\n🔄 Resetting calibration...")
                face_tracker.reset_statistics()
                frame_count = 0
            
            # Small delay
            time.sleep(0.033)  # ~30 FPS
            
    except KeyboardInterrupt:
        print("\n⏹️ Interrupted by user")
    except Exception as e:
        print(f"\n❌ Error: {e}")
    finally:
        # Cleanup
        webcam.stop()
        cv2.destroyAllWindows()
        print("\n✅ Test completed")

if __name__ == "__main__":
    main()
