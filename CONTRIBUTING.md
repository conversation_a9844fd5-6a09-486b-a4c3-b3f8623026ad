# 🤝 Panduan Kontribusi - ICikiwir

Terima kasih atas minat Anda untuk berkontribusi pada **ICikiwir**! Dokumen ini berisi panduan untuk membantu Anda berkontribusi secara efektif.

## 📋 Daftar Isi

- [Code of Conduct](#code-of-conduct)
- [Cara Berkontribusi](#cara-berkontribusi)
- [Setup Development Environment](#setup-development-environment)
- [Coding Standards](#coding-standards)
- [Testing Guidelines](#testing-guidelines)
- [Pull Request Process](#pull-request-process)
- [Issue Guidelines](#issue-guidelines)

## 🤝 Code of Conduct

Proyek ini mengikuti kode etik yang ramah dan inklusif. Dengan berpartisipasi, Anda diharapkan untuk menjaga standar ini:

- Gunakan bahasa yang ramah dan inklusif
- Hormati sudut pandang dan pengalaman yang berbeda
- Terima kritik konstruktif dengan baik
- Fokus pada apa yang terbaik untuk komunitas
- Tunjukkan empati terhadap anggota komunitas lainnya

## 🚀 Cara Berkontribusi

### Jenis Kontribusi yang Diterima

- 🐛 **Bug Reports**: Laporkan bug yang Anda temukan
- 💡 **Feature Requests**: Usulkan fitur baru
- 📝 **Documentation**: Perbaiki atau tambah dokumentasi
- 🔧 **Code Contributions**: Implementasi fitur atau perbaikan bug
- 🧪 **Testing**: Tambah atau perbaiki test cases
- 🌐 **Translations**: Terjemahan ke bahasa lain

### Sebelum Memulai

1. Periksa [Issues](https://github.com/radhityaguntoro/icikiwir/issues) yang sudah ada
2. Diskusikan perubahan besar di [Discussions](https://github.com/radhityaguntoro/icikiwir/discussions)
3. Fork repository ini
4. Buat branch untuk fitur/perbaikan Anda

## 🛠️ Setup Development Environment

### Prerequisites

- Python 3.10 atau lebih baru
- Git
- Webcam (untuk testing fitur computer vision)

### Langkah Setup

```bash
# 1. Fork dan clone repository
git clone https://github.com/YOUR_USERNAME/icikiwir.git
cd icikiwir

# 2. Buat virtual environment
python -m venv venv
source venv/bin/activate  # Linux/Mac
# atau
venv\Scripts\activate     # Windows

# 3. Install dependencies
pip install -r requirements.txt

# 4. Install development dependencies
pip install pytest pytest-cov black isort flake8 mypy

# 5. Setup pre-commit hooks (optional)
pip install pre-commit
pre-commit install

# 6. Setup models (untuk development)
python scripts/setup_models.py --placeholder

# 7. Jalankan tests untuk memastikan setup benar
python -m pytest tests/ -v
```

### Struktur Proyek

```
icikiwir/
├── src/                    # Source code utama
│   ├── capture/           # Modul webcam capture
│   ├── vision/            # Computer vision processing
│   ├── analytics/         # Analisis kognitif
│   ├── intervention/      # Sistem feedback
│   ├── storage/           # Data persistence
│   ├── reporting/         # Report generation
│   └── gui/               # User interface
├── tests/                 # Test suite
├── docs/                  # Dokumentasi
├── scripts/               # Utility scripts
└── data/                  # User data (gitignored)
```

## 📏 Coding Standards

### Python Style Guide

Kami mengikuti **PEP 8** dengan beberapa modifikasi:

- **Line length**: 100 karakter (bukan 79)
- **Bahasa komentar**: Bahasa Indonesia
- **Variable/function names**: Bahasa Inggris
- **Docstrings**: Bahasa Indonesia untuk public APIs

### Contoh Code Style

```python
def hitung_skor_fokus(blink_rate: float, head_pose: tuple) -> float:
    """
    Menghitung skor fokus berdasarkan blink rate dan head pose
    
    Args:
        blink_rate: Tingkat kedipan mata per menit
        head_pose: Tuple (pitch, yaw, roll) dalam derajat
        
    Returns:
        float: Skor fokus antara 0.0 - 1.0
    """
    # Normalisasi blink rate (12-20 bpm adalah normal)
    normalized_blink = max(0, min(1, (20 - abs(blink_rate - 16)) / 8))
    
    # Hitung deviasi head pose dari posisi ideal
    pitch, yaw, roll = head_pose
    pose_deviation = (abs(pitch) + abs(yaw) + abs(roll)) / 3
    normalized_pose = max(0, 1 - pose_deviation / 30)
    
    # Kombinasi weighted score
    focus_score = (normalized_blink * 0.6) + (normalized_pose * 0.4)
    
    return focus_score
```

### Tools untuk Code Quality

```bash
# Format code dengan black
black src/ tests/

# Sort imports dengan isort
isort src/ tests/

# Lint dengan flake8
flake8 src/ tests/

# Type checking dengan mypy
mypy src/
```

## 🧪 Testing Guidelines

### Menjalankan Tests

```bash
# Jalankan semua tests
python -m pytest

# Jalankan dengan coverage
python -m pytest --cov=src --cov-report=html

# Jalankan tests tertentu
python -m pytest tests/test_capture.py -v

# Skip slow tests
python -m pytest -m "not slow"
```

### Menulis Tests

- Setiap modul harus memiliki test coverage minimal 80%
- Gunakan descriptive test names dalam bahasa Indonesia
- Mock external dependencies (camera, TTS, dll.)
- Pisahkan unit tests dan integration tests

```python
def test_webcam_stream_inisialisasi():
    """Test inisialisasi WebcamStream dengan parameter default"""
    stream = WebcamStream()
    assert stream.src == 0
    assert stream.resolution == (640, 480)
    assert not stream.is_active()

@pytest.mark.slow
def test_webcam_stream_dengan_kamera_fisik():
    """Test WebcamStream dengan kamera fisik (slow test)"""
    # Test yang memerlukan hardware
    pass
```

## 📝 Pull Request Process

### Sebelum Submit PR

1. **Update branch Anda** dengan latest main
2. **Jalankan tests** dan pastikan semua pass
3. **Format code** dengan black dan isort
4. **Update dokumentasi** jika diperlukan
5. **Tambah entry** ke CHANGELOG.md

### PR Template

```markdown
## 📋 Deskripsi

Jelaskan perubahan yang Anda buat dan mengapa.

## 🔧 Jenis Perubahan

- [ ] Bug fix (non-breaking change yang memperbaiki issue)
- [ ] New feature (non-breaking change yang menambah fungsionalitas)
- [ ] Breaking change (fix atau feature yang menyebabkan existing functionality berubah)
- [ ] Documentation update

## 🧪 Testing

- [ ] Tests pass secara lokal
- [ ] Menambah tests untuk perubahan baru
- [ ] Coverage tidak menurun

## 📝 Checklist

- [ ] Code mengikuti style guidelines
- [ ] Self-review sudah dilakukan
- [ ] Komentar ditambah di area yang kompleks
- [ ] Dokumentasi diupdate
- [ ] Tidak ada warning baru
```

### Review Process

1. **Automated checks** harus pass (CI/CD)
2. **Code review** oleh maintainer
3. **Testing** pada berbagai environment
4. **Approval** dan merge

## 🐛 Issue Guidelines

### Bug Reports

Gunakan template berikut untuk bug reports:

```markdown
**🐛 Deskripsi Bug**
Deskripsi singkat dan jelas tentang bug.

**🔄 Langkah Reproduksi**
1. Buka aplikasi
2. Klik pada '...'
3. Scroll ke bawah
4. Lihat error

**✅ Expected Behavior**
Jelaskan apa yang seharusnya terjadi.

**❌ Actual Behavior**
Jelaskan apa yang sebenarnya terjadi.

**📱 Environment**
- OS: [e.g. Windows 11, Ubuntu 22.04]
- Python Version: [e.g. 3.11.2]
- ICikiwir Version: [e.g. 0.1.0]
- Camera: [e.g. Built-in webcam, USB camera]

**📎 Screenshots/Logs**
Tambahkan screenshots atau log files jika membantu.
```

### Feature Requests

```markdown
**💡 Feature Request**

**🎯 Problem Statement**
Jelaskan masalah yang ingin diselesaikan.

**💭 Proposed Solution**
Deskripsi solusi yang Anda inginkan.

**🔄 Alternatives Considered**
Alternatif solusi yang sudah dipertimbangkan.

**📊 Additional Context**
Context tambahan, mockups, atau referensi.
```

## 🏷️ Branch Naming Convention

- `feature/epic-X-description` - Fitur baru
- `bugfix/issue-number-description` - Perbaikan bug
- `docs/description` - Update dokumentasi
- `refactor/description` - Refactoring code
- `test/description` - Menambah atau memperbaiki tests

## 📊 Commit Message Format

```
[EpicX] Tipe: Deskripsi singkat

Deskripsi lebih detail jika diperlukan.

- Bullet point untuk perubahan spesifik
- Referensi issue: Fixes #123
```

Contoh:
```
[Epic1] feat: Tambah WebcamStream dengan threading support

Implementasi kelas WebcamStream yang mendukung:
- Non-blocking video capture
- FPS monitoring
- Thread-safe frame access
- Auto-recovery dari camera errors

Fixes #15
```

## 🔌 Menambah Modul Baru

### Modul Internal vs Plugin Eksternal

Sebelum menambah fungsionalitas baru, tentukan apakah sebaiknya dibuat sebagai:

**Modul Internal** - Jika:
- ✅ Fungsionalitas core yang dibutuhkan mayoritas user
- ✅ Terintegrasi erat dengan komponen existing
- ✅ Tidak memerlukan dependency eksternal yang besar
- ✅ Perbaikan atau enhancement fitur yang sudah ada

**Plugin Eksternal** - Jika:
- ✅ Fungsionalitas opsional/spesifik untuk use case tertentu
- ✅ Menggunakan model atau library proprietary
- ✅ Ingin maintain/distribute terpisah dari ICikiwir core
- ✅ Experimental features yang belum stable

### Panduan Lengkap Plugin Development

Untuk panduan detail membuat plugin eksternal, lihat **[Plugin Development Guide](docs/PLUGIN_GUIDE.md)**.

### Quick Start Plugin Development

```bash
# Clone template atau buat project baru
mkdir my-icikiwir-plugin
cd my-icikiwir-plugin

# Setup virtual environment
python -m venv venv
source venv/bin/activate  # Linux/Mac
# atau
venv\Scripts\activate     # Windows

# Install ICikiwir untuk development
pip install -e /path/to/icikiwir

# Setup project structure
mkdir -p src/my_plugin tests docs
touch src/my_plugin/__init__.py
touch tests/__init__.py
touch pyproject.toml README.md

# Install development tools
pip install pytest black isort mypy
```

### Template Plugin Types

#### Vision Plugin Template
```python
# src/my_plugin/vision.py
from typing import Dict, Any
import numpy as np

class MyVisionPlugin:
    """Template untuk vision processing plugin"""
    
    @property
    def name(self) -> str:
        return "my_vision_plugin"
    
    def process_frame(self, frame: np.ndarray) -> Dict[str, Any]:
        # Implementasi processing logic
        return {
            "metrics": {"custom_metric": 0.0},
            "annotations": None,
            "metadata": {}
        }
```

#### Analytics Plugin Template
```python
# src/my_plugin/analytics.py
from typing import Dict, Any

class MyAnalyticsPlugin:
    """Template untuk analytics plugin"""
    
    @property
    def name(self) -> str:
        return "my_analytics_plugin"
    
    def analyze_metrics(self, metrics: Dict[str, Any]) -> Dict[str, Any]:
        # Implementasi analysis logic
        return {"insights": [], "trends": {}}
```

### Testing Plugin

```bash
# Run unit tests
pytest tests/ -v

# Test plugin discovery
python -c "
import importlib.metadata
for ep in importlib.metadata.entry_points(group='icikiwir.plugins'):
    print(f'Found: {ep.name} -> {ep.value}')
"

# Test dengan ICikiwir
python -m icikiwir --list-plugins
python -m icikiwir --enable-plugin my_plugin --debug
```

## 🎯 Development Roadmap

Lihat [ARCHITECTURE.md](docs/ARCHITECTURE.md) untuk roadmap lengkap per Epic:

- **Epic 1**: Real-Time Focus Monitor
- **Epic 2**: Adaptive Feedback TTS
- **Epic 3**: Session Logger & Dashboard
- **Epic 4**: Multi-Scale Reports & Analytics
- **Epic 5**: Balanced Habit Tracker
- **Epic 6**: Hydration Reminder

## 📞 Bantuan dan Dukungan

- 💬 **Discussions**: Untuk pertanyaan umum dan diskusi
- 🐛 **Issues**: Untuk bug reports dan feature requests
- 📧 **Email**: <EMAIL> untuk pertanyaan pribadi

## 🙏 Acknowledgments

Terima kasih kepada semua kontributor yang telah membantu mengembangkan ICikiwir!

---

**Dibuat dengan ❤️ oleh komunitas ICikiwir**

(c) 2025 Radhitya Guntoro Adhi