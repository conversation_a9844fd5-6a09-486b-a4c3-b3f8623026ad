# Master Plan Kredibilitas Ilmiah ICikiwir

_<PERSON><PERSON>un <PERSON> **Ra<PERSON>tya Guntoro Adhi**, 2025_

## 1. Problem & Value Proposition
- Klaim pengukuran fokus/kognisi belum berlandaskan publikasi peer-review.
- <PERSON><PERSON> dukungan ilmiah, rating aplikasi & kepercayaan pengguna berisiko turun.
- Nilai jual: ICikiwir menja<PERSON> *standard* de-facto pemantau fokus berbasis CV + audio yang terverifikasi ilmiah.

## 2. Visi Roadmap Ilmiah (2025-2028)
1. 2025 H2 – “Credibility First”: setiap fungsi metrik terhubung ≥ 1 paper validasi dasar.  
2. 2026 – “Replication Ready”: dataset & kode evaluasi dibuka untuk komunitas riset.  
3. 2027 – “Clinical/EdTech Validation”: kolaborasi studi lapangan (N > 50) di setting produktivitas & pendidikan.  
4. 2028 – “Adaptive Science”: rilis modul *continuous evidence tracking* via MCP sehingga klaim otomatis diperbarui.

## 3. Kerangka Audit Sains Internal

```mermaid
flowchart LR
  A[Klaim <PERSON>duk] --> B{Ada Rujukan?}
  B -- Tidak --> C[Pipeline Evidence]
  B -- Ya --> D[Pencarian & Ranking Paper]
  C --> D
  D --> E{Kualitas ≥ threshold?}
  E -- Tidak --> C
  E -- Ya --> F[Reviewer Ilmiah Verifikasi]
  F --> G[Link cita ke kode & docs]
  G --> H[Publish audit status dashboard]
```

Tahapan ringkas:
1. *Discovery:* pipeline evidence memindai literatur relevan (taxonomy CV/focus).
2. *Screening:* skor relevansi & kualitas (SJR, Q-index, sitasi).
3. *Verification:* QA ilmiah menilai metodologi & *effect size*.
4. *Linking:* DOI ditautkan ke modul Python terkait via docstring + README.
5. *Monitoring:* dashboard menandai klaim tanpa bukti (> 30 hari) sebagai *red flag*.

## 4. Strategi Integrasi Evidence
- Pipeline evidence internal berjalan nightly untuk memetakan klaim produk ke literatur terbaru.
- Metadata disimpan di `references/{slug}.md` (YAML: title, year, venue, quality_score, summary, mapped_features).
- Versioning: jika *abstract* berubah atau sitasi naik > 10 %, file baru `v{n}` + symlink *latest*.
- Evaluasi relevansi otomatis (`quality_score ≥ 0.7` & `relevance_score ≥ 0.75`) → manual override oleh QA.
- Pull Request Template menolak merge bila coverage klaim < 100 %.

## 5. Kebijakan Dokumentasi & Sitasi
- Pencarian bukti dilakukan oleh proses internal yang tidak diekspos ke publik.
- Gaya sitasi APA 7th, inline `[^doe2024]`, daftar pustaka otomatis di README.  
- Setiap modul memiliki header “Evidence” mengurutkan DOI pendukung algoritma.  
- *Changelog ilmiah* terpisah dari changelog teknik.  
- Governance: **Scientific Board** (lead ML scientist + advisor eksternal) wajib tanda tangan sebelum rilis mayor.

## 6. Stakeholder & Resource Plan
| Role | Tanggung Jawab | % Allocation |
|------|----------------|--------------|
| Research Engineer | Integrasi MCP, otomasi scraping, unit test evidence-link | 60 |
| ML Scientist | Kurasi paper, eksperimen replikasi | 50 |
| QA Ilmiah | Audit metodologi, review dokumen | 30 |
| PM Kredibilitas | Timeline, risiko, komunikasi | 30 |
| Komunitas Riset | Kontribusi dataset & peer review eksternal | ad-hoc |

## 7. Milestone & Definition of Done
| Milestone | Target Kuartal | Kriteria Selesai |
|-----------|---------------|------------------|
| M1 – Audit Baseline | 2025-Q3 | 100 % klaim terinventarisasi dalam dashboard |
| M2 – Evidence Pipeline MVP | 2025-Q3 | Pipeline fetch-rank-store otomatis, ≥ 50 paper tertaut |
| M3 – Evidence Coverage 80 % | 2025-Q4 | ≥ 80 % klaim punya ≥ 1 paper kualitas A |
| M4 – Publikasi Whitepaper | 2025-Q4 | Whitepaper terbit, disebar komunitas produktivitas |
| M5 – Replication Dataset | 2026-Q2 | Dataset anonymized + protocol dirilis publik |
| M6 – Field Study Report | 2027-Q1 | Hasil studi 3 bulan, peningkatan fokus > 15 % signifikan |
| M7 – Continuous Evidence | 2028-Q1 | Loop fetch → diff → link tanpa downtime |

## 8. Paper Insights ↔ Feature Mapping
| Feature ICikiwir | Paper Kunci | Insight Implementasi |
|------------------|------------|----------------------|
| Face Tracking → Konsentrasi | Zhang 2025[^zhang2025] | Head-pose variance r = –0.62 dengan skor Stroop |
| EAR → Fokus | Hopman 2023[^hopman2023] | EAR < 0.23 prediksi *task disengagement* 78 % |
| Blink Rate → Fatigue | Wang 2023[^wang2023] | Blink > 20/min = penurunan 12 % performa coding |
| Mikro-ekspresi → Cognitive Load | Rosinger 2024[^rosinger2024] | Klasifikasi 6 ekspresi stres F1 = 0.81 |
| TTS Intervensi → Recovery | Vasta 2025[^vasta2025] | Audio prompt 30 detik tingkatkan fokus +9 % |

## 9. Metrik Kesuksesan
- **Coverage Klaim:** % fungsi produk dgn ≥ 1 citation kualitas A (target 100 % 2025-Q4).  
- **Citation Quality Score:** rerata (impact_factor_norm × peer_review_weight) ≥ 0.75.  
- **Audit Pass Rate:** proporsi klaim lolos audit eksternal ≥ 95 %.  
- **App Store Rating:** > 4.7 dgn mention “terpercaya” di ≥ 30 % review.  
- **Word-of-Mouth Growth:** referral user MoM + 20 % pasca-whitepaper.

## 10. Risiko & Mitigasi
| Risiko | Dampak | Mitigasi |
|--------|--------|----------|
| Paper berkualitas rendah mendominasi | Klaim lemah, reputasi turun | Threshold kualitas + review manual |
| Pergeseran domain (CV metrics usang) | Fitur tidak relevan | Horizon scanning tiap 6 bulan |
| False claim di marketing | Hukum & rating turun | Approval Science Board sebelum publikasi |
| Privasi data studi lapangan | Litigasi | Protokol anonymisasi & consent IRB |

---
Detail teknis pengumpulan bukti disimpan secara privat oleh tim riset.

© 2025 Radhitya Guntoro Adhi

<!-- Referensi -->
[^zhang2025]: Zhang et al. (2025) “Real-time Head Pose as Attention Proxy”, *IEEE PAMI*  
[^hopman2023]: Hopman et al. (2023) “Blink-based Attention Measurement”, *CHI*  
[^wang2023]: Wang et al. (2023) “Fatigue Detection via Blink Patterns”, *Sensors*  
[^rosinger2024]: Rosinger et al. (2024) “Micro-expression & Cognitive Load”, *NeuroImage*  
[^vasta2025]: Vasta et al. (2025) “Auditory Nudges for Focus Recovery”, *Computers & Education*