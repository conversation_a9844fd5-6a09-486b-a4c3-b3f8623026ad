name: CI - ICikiwir Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.10.x, 3.11.x, 3.12.x]

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}

    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y libgl1-mesa-glx libglib2.0-0 libsm6 libxext6 libxrender-dev libgomp1
        # Dependencies untuk OpenCV dan MediaPipe

    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-

    - name: Install Python dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest pytest-cov pytest-asyncio

    - name: Lin<PERSON> with flake8
      run: |
        pip install flake8
        # Stop the build if there are Python syntax errors or undefined names
        flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
        # Exit-zero treats all errors as warnings. Line length set to 100
        flake8 . --count --exit-zero --max-complexity=10 --max-line-length=100 --statistics

    - name: Type check with mypy
      run: |
        pip install mypy
        mypy src --ignore-missing-imports || true
        # Ignore missing imports untuk sekarang, akan diperbaiki nanti

    - name: Test with pytest
      run: |
        python -m pytest tests/ -v --cov=src --cov-report=xml --cov-report=html
      env:
        # Set environment variables untuk testing
        PYTHONPATH: ${{ github.workspace }}/src
        # Disable GUI untuk headless testing
        DISPLAY: ""

    - name: Upload coverage reports to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella
        fail_ci_if_error: false

    - name: Archive test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: test-results-${{ matrix.python-version }}
        path: |
          htmlcov/
          coverage.xml
          pytest-report.html

  build:
    runs-on: ubuntu-latest
    needs: test
    if: github.ref == 'refs/heads/main'

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: 3.11.x

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pyinstaller

    - name: Build executable
      run: |
        # Build single executable file
        pyinstaller --onefile --windowed --name icikiwir src/__main__.py
        # Note: Untuk production, perlu konfigurasi lebih detail

    - name: Archive build artifacts
      uses: actions/upload-artifact@v3
      with:
        name: icikiwir-executable
        path: dist/

  security:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Run security scan with bandit
      run: |
        pip install bandit
        bandit -r src/ -f json -o bandit-report.json || true

    - name: Upload security scan results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: security-scan-results
        path: bandit-report.json