"""
Konfigurasi global dan user preferences untuk ICikiwir
<PERSON> pengaturan aplikasi, threshold, dan preferensi pengguna

(c) 2025 Ra<PERSON><PERSON>a Gun<PERSON>o Adhi
"""

import os
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict
import json

@dataclass
class CameraConfig:
    """Konfigurasi kamera dan video capture"""
    default_camera_index: int = 0
    resolution_width: int = 640
    resolution_height: int = 480
    target_fps: int = 30
    auto_exposure: bool = True
    brightness: float = 0.0
    contrast: float = 1.0

@dataclass
class VisionConfig:
    """Konfigurasi computer vision processing"""
    face_detection_confidence: float = 0.7
    landmark_detection_confidence: float = 0.5
    max_num_faces: int = 1
    min_face_size: int = 100
    enable_face_mesh: bool = True
    enable_iris_detection: bool = True

@dataclass
class AnalyticsConfig:
    """Konfigurasi analytics dan scoring"""
    focus_score_window_seconds: int = 60
    blink_rate_normal_range: tuple = (12, 20)  # blinks per minute
    head_pose_threshold_degrees: float = 15.0
    cognitive_load_weights: Dict[str, float] = None
    
    # Parameter baru untuk Real-Time Focus Monitor
    blink_window_sec: int = 60  # Window untuk menghitung blink rate
    head_var_window: int = 60   # Window untuk menghitung head pose variance
    focus_threshold_low: float = 30.0    # Threshold fokus rendah
    focus_threshold_medium: float = 60.0 # Threshold fokus sedang
    focus_threshold_high: float = 80.0   # Threshold fokus tinggi
    
    def __post_init__(self):
        if self.cognitive_load_weights is None:
            self.cognitive_load_weights = {
                "blink_rate": 0.3,
                "head_pose": 0.2,
                "eye_gaze": 0.25,
                "facial_expression": 0.25
            }

@dataclass
class InterventionConfig:
    """Konfigurasi sistem intervention dan feedback"""
    enable_tts: bool = True
    tts_voice_id: Optional[str] = None
    tts_rate: int = 200  # words per minute
    tts_volume: float = 0.8
    
    # Threshold untuk trigger intervention
    low_focus_threshold: float = 0.4
    high_stress_threshold: float = 0.7
    intervention_cooldown_seconds: int = 300  # 5 menit
    
    # Jenis nudge yang diaktifkan
    enable_focus_nudge: bool = True
    enable_break_reminder: bool = True
    enable_posture_reminder: bool = True
    enable_hydration_reminder: bool = True

@dataclass
class StorageConfig:
    """Konfigurasi penyimpanan data"""
    data_directory: str = "data"
    database_filename: str = "icikiwir.db"
    csv_log_rotation_days: int = 30
    max_log_file_size_mb: int = 100
    backup_enabled: bool = True
    backup_interval_hours: int = 24
    
    # Konfigurasi logging untuk Epic 3
    log_flush_sec: int = 30  # Interval flush buffer ke database
    enable_csv_fallback: bool = True  # Aktifkan CSV fallback jika SQLite gagal

@dataclass
class GUIConfig:
    """Konfigurasi antarmuka pengguna"""
    theme: str = "light"  # light, dark, auto
    window_width: int = 1200
    window_height: int = 800
    window_resizable: bool = True
    show_fps_counter: bool = False
    show_debug_info: bool = False
    language: str = "id"  # id, en
    
    # Dashboard refresh rate
    dashboard_update_interval_ms: int = 1000
    chart_animation_enabled: bool = True
    
    # Dashboard konfigurasi untuk Epic 3
    dashboard_refresh_sec: int = 5  # Auto refresh dashboard setiap 5 detik
    chart_history_minutes: int = 15  # Tampilkan data 15 menit terakhir
    enable_realtime_charts: bool = True  # Aktifkan chart realtime

@dataclass
class HydrationConfig:
    """Konfigurasi pengingat hidrasi"""
    enabled: bool = True
    reminder_interval_minutes: int = 30
    daily_target_ml: int = 2000
    glass_size_ml: int = 250
    reminder_sound_enabled: bool = True

@dataclass
class MCPConfig:
    """Konfigurasi MCP WebResearch integration"""
    enabled: bool = True
    server_url: str = "http://localhost:3001"
    timeout_seconds: int = 30
    max_retries: int = 3
    retry_delay_seconds: float = 1.0
    
    # Search configuration
    max_search_results: int = 10
    search_timeout_seconds: int = 15
    
    # Paper extraction configuration
    extract_abstract: bool = True
    extract_keywords: bool = True
    extract_citations: bool = True
    
    # Digest generation configuration
    digest_language: str = "id"  # id atau en
    digest_output_dir: str = "references"
    digest_template_style: str = "academic"  # academic, simple, detailed
    
    # Auto-update configuration
    auto_update_enabled: bool = False
    auto_update_interval_hours: int = 24
    auto_update_keywords: list = None
    
    def __post_init__(self):
        if self.auto_update_keywords is None:
            self.auto_update_keywords = [
                "cognitive load", "attention monitoring", "eye tracking",
                "focus detection", "study companion", "educational technology"
            ]

class ICikiwirConfig:
    """
    Kelas utama untuk mengelola konfigurasi aplikasi ICikiwir
    """
    
    def __init__(self, config_file: Optional[str] = None):
        """
        Inisialisasi konfigurasi
        
        Args:
            config_file: Path ke file konfigurasi (optional)
        """
        self.config_file = config_file or self._get_default_config_path()
        
        # Inisialisasi dengan nilai default
        self.camera = CameraConfig()
        self.vision = VisionConfig()
        self.analytics = AnalyticsConfig()
        self.intervention = InterventionConfig()
        self.storage = StorageConfig()
        self.gui = GUIConfig()
        self.hydration = HydrationConfig()
        self.mcp = MCPConfig()
        
        # Load konfigurasi dari file jika ada
        self.load_config()
        
    def _get_default_config_path(self) -> str:
        """
        Mendapatkan path default untuk file konfigurasi
        
        Returns:
            str: Path ke file konfigurasi
        """
        # Buat direktori config di home directory user
        config_dir = Path.home() / ".icikiwir"
        config_dir.mkdir(exist_ok=True)
        return str(config_dir / "config.json")
        
    def load_config(self) -> bool:
        """
        Load konfigurasi dari file
        
        Returns:
            bool: True jika berhasil, False jika gagal
        """
        try:
            if not os.path.exists(self.config_file):
                print(f"📄 File konfigurasi tidak ditemukan: {self.config_file}")
                print("   Menggunakan konfigurasi default")
                return False
                
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
                
            # Update konfigurasi dari file
            if 'camera' in config_data:
                self.camera = CameraConfig(**config_data['camera'])
            if 'vision' in config_data:
                self.vision = VisionConfig(**config_data['vision'])
            if 'analytics' in config_data:
                self.analytics = AnalyticsConfig(**config_data['analytics'])
            if 'intervention' in config_data:
                self.intervention = InterventionConfig(**config_data['intervention'])
            if 'storage' in config_data:
                self.storage = StorageConfig(**config_data['storage'])
            if 'gui' in config_data:
                self.gui = GUIConfig(**config_data['gui'])
            if 'hydration' in config_data:
                self.hydration = HydrationConfig(**config_data['hydration'])
            if 'mcp' in config_data:
                self.mcp = MCPConfig(**config_data['mcp'])
                
            print(f"✅ Konfigurasi berhasil dimuat dari: {self.config_file}")
            return True
            
        except Exception as e:
            print(f"❌ Gagal memuat konfigurasi: {e}")
            print("   Menggunakan konfigurasi default")
            return False
            
    def save_config(self) -> bool:
        """
        Simpan konfigurasi ke file
        
        Returns:
            bool: True jika berhasil, False jika gagal
        """
        try:
            config_data = {
                'camera': asdict(self.camera),
                'vision': asdict(self.vision),
                'analytics': asdict(self.analytics),
                'intervention': asdict(self.intervention),
                'storage': asdict(self.storage),
                'gui': asdict(self.gui),
                'hydration': asdict(self.hydration),
                'mcp': asdict(self.mcp)
            }
            
            # Buat direktori jika belum ada
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
                
            print(f"✅ Konfigurasi berhasil disimpan ke: {self.config_file}")
            return True
            
        except Exception as e:
            print(f"❌ Gagal menyimpan konfigurasi: {e}")
            return False
            
    def get_data_directory(self) -> Path:
        """
        Mendapatkan path direktori data
        
        Returns:
            Path: Path ke direktori data
        """
        data_dir = Path(self.storage.data_directory)
        if not data_dir.is_absolute():
            # Jika path relatif, buat relatif terhadap direktori proyek
            project_root = Path(__file__).parent.parent
            data_dir = project_root / data_dir
            
        data_dir.mkdir(parents=True, exist_ok=True)
        return data_dir
        
    def get_database_path(self) -> Path:
        """
        Mendapatkan path file database
        
        Returns:
            Path: Path ke file database
        """
        return self.get_data_directory() / self.storage.database_filename
        
    def reset_to_defaults(self):
        """
        Reset semua konfigurasi ke nilai default
        """
        self.camera = CameraConfig()
        self.vision = VisionConfig()
        self.analytics = AnalyticsConfig()
        self.intervention = InterventionConfig()
        self.storage = StorageConfig()
        self.gui = GUIConfig()
        self.hydration = HydrationConfig()
        self.mcp = MCPConfig()
        
        print("🔄 Konfigurasi direset ke nilai default")

# Instance global konfigurasi
config = ICikiwirConfig()

# Konstanta aplikasi
APP_NAME = "ICikiwir"
APP_VERSION = "0.1.0"
APP_AUTHOR = "Radhitya Guntoro Adhi"
APP_DESCRIPTION = "Cognitive-Aware Study Companion"
APP_LICENSE = "MIT"

# Konstanta TTS
TTS_ENABLED = True
TTS_RATE = 180  # words per minute
TTS_COOLDOWN_SEC = 20  # jarak minimal antar ucapan dalam detik

# Konstanta performa
PERFORMANCE_TARGETS = {
    "max_frame_processing_ms": 33,  # 30 FPS
    "max_model_inference_ms": 50,
    "max_tts_latency_ms": 200,
    "max_memory_usage_mb": 500,
    "max_cpu_usage_percent": 25,
    "max_storage_growth_mb_per_day": 10
}

# Konstanta UI
UI_COLORS = {
    "primary": "#2196F3",
    "secondary": "#FFC107",
    "success": "#4CAF50",
    "warning": "#FF9800",
    "error": "#F44336",
    "info": "#00BCD4"
}

# Konstanta untuk Epic 3 - Session Logger & Dashboard
DB_PATH = None  # Akan diset dari config.get_database_path()
LOG_FLUSH_SEC = 30  # Default flush interval
DASHBOARD_REFRESH_SEC = 5  # Default dashboard refresh interval

# Konstanta export
EXPORT_FORMATS = ["csv", "json", "xlsx"]
DEFAULT_EXPORT_FORMAT = "csv"

# Konstanta untuk Phase D - Markdown Reporting & Viewer
REPORT_DIR = Path("data/reports")