"""
Enhanced TTS Engine untuk sistem feedback suara ICikiwir
Menggunakan multiple backends: gTTS (Google), pyttsx3 (offline fallback)
Dengan pygame untuk audio playback yang lebih berkualitas

(c) 2025 Radhitya Guntoro Adhi
"""

import threading
import time
import tempfile
import os
from typing import Optional
from pathlib import Path
import queue

# Import TTS backends dengan fallback
try:
    from gtts import gTTS
    GTTS_AVAILABLE = True
except ImportError:
    GTTS_AVAILABLE = False
    print("⚠️ gTTS tidak tersedia, menggunakan pyttsx3 saja")

try:
    import pygame
    PYGAME_AVAILABLE = True
except ImportError:
    PYGAME_AVAILABLE = False
    print("⚠️ pygame tidak tersedia, menggunakan pyttsx3 saja")

try:
    import pyttsx3
    PYTTSX3_AVAILABLE = True
except ImportError:
    PYTTSX3_AVAILABLE = False
    print("❌ pyttsx3 tidak tersedia!")

try:
    from ..config import config
except ImportError:
    # Fallback config jika import gagal
    class FallbackConfig:
        TTS_COOLDOWN_SEC = 20
        TTS_RATE = 180
        class intervention:
            tts_volume = 0.8
            tts_voice_id = None
    config = FallbackConfig()


class EnhancedTTSEngine:
    """
    Enhanced TTS Engine dengan multiple backends untuk kualitas suara yang lebih baik
    Priority: gTTS (online, high quality) > pyttsx3 (offline, medium quality)
    """
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        """Implementasi singleton pattern thread-safe"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(EnhancedTTSEngine, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        """Inisialisasi Enhanced TTS engine"""
        if hasattr(self, '_initialized'):
            return
            
        self._initialized = True
        
        # State management
        self.is_speaking = False
        self.last_speak_time = 0
        self.cooldown_seconds = getattr(config, 'TTS_COOLDOWN_SEC', 20)
        self.speech_queue = queue.Queue()
        self.worker_thread = None
        self.stop_worker = False
        
        # Backend configuration
        self.current_backend = None
        self.backends = []
        self.rate = getattr(config, 'TTS_RATE', 180)
        self.volume = getattr(config.intervention, 'tts_volume', 0.8)
        self.language = 'id'  # Indonesian
        
        # Audio system
        self.audio_initialized = False
        
        # Temp directory untuk audio files
        self.temp_dir = Path(tempfile.gettempdir()) / "icikiwir_tts"
        self.temp_dir.mkdir(exist_ok=True)
        
        # Initialize backends dan audio
        self._initialize_backends()
        self._initialize_audio()
        self._start_worker()
        
        print(f"✅ Enhanced TTS Engine berhasil diinisialisasi dengan backend: {self.current_backend['name'] if self.current_backend else 'None'}")
    
    def _initialize_backends(self):
        """Inisialisasi semua TTS backends yang tersedia"""
        print("🔊 Menginisialisasi TTS backends...")
        
        # 1. Google TTS (gTTS) - Kualitas terbaik, memerlukan internet
        if GTTS_AVAILABLE:
            try:
                # Test gTTS dengan teks pendek
                test_tts = gTTS(text="test", lang='id', slow=False)
                self.backends.append({
                    'name': 'gTTS',
                    'quality': 'high',
                    'online': True,
                    'engine': 'gtts'
                })
                print("✅ Google TTS (gTTS) tersedia")
            except Exception as e:
                print(f"⚠️ gTTS initialization gagal: {e}")
        
        # 2. pyttsx3 - Fallback offline
        if PYTTSX3_AVAILABLE:
            try:
                engine = pyttsx3.init()
                voices = engine.getProperty('voices')
                
                self.backends.append({
                    'name': 'pyttsx3',
                    'quality': 'medium',
                    'online': False,
                    'engine': 'pyttsx3',
                    'instance': engine,
                    'voices': voices
                })
                print("✅ pyttsx3 tersedia sebagai fallback")
            except Exception as e:
                print(f"⚠️ pyttsx3 initialization gagal: {e}")
        
        # Pilih backend terbaik
        if self.backends:
            # Prioritas: gTTS > pyttsx3
            quality_order = {'high': 2, 'medium': 1}
            self.backends.sort(key=lambda x: quality_order.get(x['quality'], 0), reverse=True)
            self.current_backend = self.backends[0]
            print(f"🎯 Backend terpilih: {self.current_backend['name']} (kualitas {self.current_backend['quality']})")
        else:
            print("❌ Tidak ada TTS backend yang tersedia!")
    
    def _initialize_audio(self):
        """Inisialisasi pygame mixer untuk audio playback"""
        if PYGAME_AVAILABLE:
            try:
                pygame.mixer.init(frequency=22050, size=-16, channels=2, buffer=512)
                self.audio_initialized = True
                print("✅ Audio system (pygame) berhasil diinisialisasi")
            except Exception as e:
                print(f"❌ Audio initialization gagal: {e}")
                self.audio_initialized = False
        else:
            print("⚠️ pygame tidak tersedia, menggunakan pyttsx3 untuk audio")
    
    def _start_worker(self):
        """Memulai worker thread untuk memproses speech queue"""
        if not self.worker_thread or not self.worker_thread.is_alive():
            self.worker_thread = threading.Thread(target=self._speech_worker, daemon=True)
            self.worker_thread.start()
    
    def _speech_worker(self):
        """Worker thread untuk memproses speech queue"""
        while not self.stop_worker:
            try:
                # Ambil item dari queue dengan timeout
                speech_item = self.speech_queue.get(timeout=1.0)
                
                if speech_item and self.current_backend:
                    text = speech_item['text']
                    priority = speech_item.get('priority', False)
                    
                    self.is_speaking = True
                    
                    # Generate dan play audio
                    success = self._generate_and_play_audio(text)
                    
                    if not success:
                        # Fallback ke backend lain
                        self._try_fallback_backends(text)
                    
                    # Update waktu terakhir bicara
                    self.last_speak_time = time.time()
                    self.is_speaking = False
                    
                self.speech_queue.task_done()
                
            except queue.Empty:
                continue
            except Exception as e:
                print(f"❌ TTS Worker error: {e}")
                self.is_speaking = False
    
    def _generate_and_play_audio(self, text: str) -> bool:
        """Generate dan play audio menggunakan backend saat ini"""
        try:
            backend = self.current_backend
            
            if backend['engine'] == 'gtts':
                return self._speak_with_gtts(text)
            elif backend['engine'] == 'pyttsx3':
                return self._speak_with_pyttsx3(text)
            
        except Exception as e:
            print(f"❌ Audio generation gagal: {e}")
            return False
        
        return False
    
    def _speak_with_gtts(self, text: str) -> bool:
        """Speak menggunakan Google TTS"""
        try:
            # Generate audio file
            tts = gTTS(text=text, lang=self.language, slow=False)
            
            # Save to temp file
            temp_file = self.temp_dir / f"tts_{int(time.time())}.mp3"
            tts.save(str(temp_file))
            
            # Play audio
            if self.audio_initialized:
                pygame.mixer.music.load(str(temp_file))
                pygame.mixer.music.set_volume(self.volume)
                pygame.mixer.music.play()
                
                # Wait for playback to finish
                while pygame.mixer.music.get_busy():
                    time.sleep(0.1)
                    if self.stop_worker:  # Allow interruption
                        pygame.mixer.music.stop()
                        break
            
            # Cleanup temp file
            try:
                temp_file.unlink()
            except:
                pass
            
            print(f"🔊 gTTS selesai: '{text[:50]}{'...' if len(text) > 50 else ''}'")
            return True
            
        except Exception as e:
            print(f"❌ gTTS error: {e}")
            return False
    
    def _speak_with_pyttsx3(self, text: str) -> bool:
        """Speak menggunakan pyttsx3 (fallback)"""
        try:
            backend = self.current_backend
            engine = backend['instance']
            
            # Set properties
            engine.setProperty('rate', self.rate)
            engine.setProperty('volume', self.volume)
            
            # Set voice jika ada preferensi
            voice_id = getattr(config.intervention, 'tts_voice_id', None)
            if voice_id and backend.get('voices'):
                for voice in backend['voices']:
                    if voice.id == voice_id:
                        engine.setProperty('voice', voice.id)
                        break
            
            # Speak
            engine.say(text)
            engine.runAndWait()
            
            print(f"🔊 pyttsx3 selesai: '{text[:50]}{'...' if len(text) > 50 else ''}'")
            return True
            
        except Exception as e:
            print(f"❌ pyttsx3 error: {e}")
            return False
    
    def _try_fallback_backends(self, text: str):
        """Coba backend lain jika backend utama gagal"""
        for backend in self.backends[1:]:  # Skip current backend
            print(f"🔄 Mencoba fallback backend: {backend['name']}")
            
            original_backend = self.current_backend
            self.current_backend = backend
            
            success = self._generate_and_play_audio(text)
            
            if success:
                print(f"✅ Fallback berhasil dengan {backend['name']}")
                # Kembalikan ke backend original untuk next time
                self.current_backend = original_backend
                return
            else:
                self.current_backend = original_backend
        
        print("❌ Semua TTS backend gagal")
    
    def get_available_voices(self) -> list:
        """
        Mendapatkan daftar suara yang tersedia dari semua backends
        
        Returns:
            list: Daftar voice yang tersedia dengan info nama dan ID
        """
        voice_list = []
        
        for backend in self.backends:
            if backend['engine'] == 'pyttsx3' and 'voices' in backend:
                try:
                    for voice in backend['voices']:
                        voice_info = {
                            'id': voice.id,
                            'name': voice.name,
                            'backend': 'pyttsx3',
                            'languages': getattr(voice, 'languages', []),
                            'gender': getattr(voice, 'gender', 'Unknown')
                        }
                        voice_list.append(voice_info)
                except Exception as e:
                    print(f"⚠️ Error mendapatkan voice dari pyttsx3: {e}")
            
            elif backend['engine'] == 'gtts':
                # gTTS mendukung berbagai bahasa
                gtts_voices = [
                    {'id': 'gtts_id', 'name': 'Google TTS Indonesian', 'backend': 'gtts', 'languages': ['id'], 'gender': 'Female'},
                    {'id': 'gtts_en', 'name': 'Google TTS English', 'backend': 'gtts', 'languages': ['en'], 'gender': 'Female'}
                ]
                voice_list.extend(gtts_voices)
        
        return voice_list
    
    def set_voice(self, voice_id: str) -> bool:
        """
        Mengatur voice yang digunakan
        
        Args:
            voice_id: ID voice yang ingin digunakan
            
        Returns:
            bool: True jika berhasil, False jika gagal
        """
        try:
            # Cek apakah voice_id untuk gTTS
            if voice_id.startswith('gtts_'):
                if voice_id == 'gtts_id':
                    self.language = 'id'
                elif voice_id == 'gtts_en':
                    self.language = 'en'
                print(f"✅ gTTS language diubah ke: {self.language}")
                return True
            
            # Cek voice pyttsx3
            for backend in self.backends:
                if backend['engine'] == 'pyttsx3' and 'voices' in backend:
                    for voice in backend['voices']:
                        if voice.id == voice_id:
                            backend['instance'].setProperty('voice', voice.id)
                            print(f"✅ pyttsx3 voice berhasil diubah ke: {voice.name}")
                            return True
            
            print(f"⚠️ Voice dengan ID '{voice_id}' tidak ditemukan")
            return False
            
        except Exception as e:
            print(f"❌ Error mengatur voice: {e}")
            return False
    
    def set_rate(self, rate: int):
        """
        Mengatur kecepatan bicara
        
        Args:
            rate: Kecepatan dalam words per minute (default: 180)
        """
        self.rate = rate
        
        # Update rate untuk pyttsx3 backends
        for backend in self.backends:
            if backend['engine'] == 'pyttsx3':
                try:
                    backend['instance'].setProperty('rate', rate)
                    print(f"✅ Rate TTS diubah ke: {rate} WPM")
                except Exception as e:
                    print(f"❌ Error mengatur rate: {e}")
    
    def is_cooldown_active(self) -> bool:
        """
        Cek apakah masih dalam periode cooldown
        
        Returns:
            bool: True jika masih cooldown, False jika sudah boleh bicara
        """
        current_time = time.time()
        return (current_time - self.last_speak_time) < self.cooldown_seconds
    
    def speak(self, text: str, voice: Optional[str] = None, rate: Optional[int] = None, priority: bool = False) -> bool:
        """
        Mengucapkan teks dengan TTS menggunakan queue system
        
        Args:
            text: Teks yang akan diucapkan
            voice: ID voice yang ingin digunakan (optional)
            rate: Kecepatan bicara (optional)
            priority: Apakah speech ini prioritas (optional)
            
        Returns:
            bool: True jika berhasil menambahkan ke queue, False jika gagal atau sedang cooldown
        """
        if not self.current_backend:
            print("⚠️ Tidak ada TTS backend yang tersedia")
            return False
        
        if not priority and self.is_cooldown_active():
            remaining = self.cooldown_seconds - (time.time() - self.last_speak_time)
            print(f"⚠️ TTS dalam cooldown, sisa {remaining:.1f} detik")
            return False
        
        try:
            # Set voice sementara jika diminta
            if voice:
                self.set_voice(voice)
            
            # Set rate sementara jika diminta
            if rate:
                self.set_rate(rate)
            
            # Tambahkan ke queue
            speech_item = {
                'text': text,
                'priority': priority,
                'timestamp': time.time()
            }
            
            if priority:
                # Clear queue untuk priority speech
                while not self.speech_queue.empty():
                    try:
                        self.speech_queue.get_nowait()
                    except queue.Empty:
                        break
            
            self.speech_queue.put(speech_item)
            
            print(f"🔊 TTS ditambahkan ke queue: '{text[:50]}{'...' if len(text) > 50 else ''}'")
            return True
            
        except Exception as e:
            print(f"❌ Error menambahkan TTS ke queue: {e}")
            return False
    
    def stop(self):
        """Menghentikan TTS yang sedang berjalan dan clear queue"""
        try:
            # Clear queue
            while not self.speech_queue.empty():
                try:
                    self.speech_queue.get_nowait()
                except queue.Empty:
                    break
            
            # Stop audio playback
            if self.audio_initialized and pygame.mixer.get_init():
                pygame.mixer.music.stop()
            
            # Stop pyttsx3 engines
            for backend in self.backends:
                if backend['engine'] == 'pyttsx3':
                    try:
                        backend['instance'].stop()
                    except:
                        pass
            
            self.is_speaking = False
            print("🛑 TTS dihentikan dan queue dibersihkan")
            
        except Exception as e:
            print(f"❌ Error menghentikan TTS: {e}")
    
    def is_available(self) -> bool:
        """
        Cek apakah TTS engine tersedia dan siap digunakan
        
        Returns:
            bool: True jika tersedia, False jika tidak
        """
        return self.current_backend is not None and not self.is_speaking
    
    def get_backend_info(self) -> dict:
        """
        Mendapatkan informasi backend yang sedang aktif
        
        Returns:
            dict: Informasi backend aktif
        """
        if self.current_backend:
            return {
                'name': self.current_backend['name'],
                'quality': self.current_backend['quality'],
                'online': self.current_backend['online'],
                'available_backends': len(self.backends),
                'audio_system': 'pygame' if self.audio_initialized else 'pyttsx3'
            }
        return {'name': 'None', 'available_backends': 0}
    
    def cleanup(self):
        """Cleanup resources saat shutdown"""
        try:
            self.stop_worker = True
            self.stop()
            
            if self.worker_thread and self.worker_thread.is_alive():
                self.worker_thread.join(timeout=2.0)
            
            if self.audio_initialized:
                pygame.mixer.quit()
            
            # Cleanup temp files
            if self.temp_dir.exists():
                for temp_file in self.temp_dir.glob("tts_*.mp3"):
                    try:
                        temp_file.unlink()
                    except:
                        pass
            
            print("✅ Enhanced TTS Engine cleanup selesai")
            
        except Exception as e:
            print(f"❌ Error during TTS cleanup: {e}")


# Backward compatibility - buat instance dengan nama lama
class TTSEngine(EnhancedTTSEngine):
    """Backward compatibility wrapper untuk TTSEngine lama"""
    pass


# Instance global TTS engine (singleton)
tts_engine = EnhancedTTSEngine()


def speak(text: str, voice: Optional[str] = None, rate: int = 180) -> bool:
    """
    Fungsi helper untuk menggunakan Enhanced TTS engine global
    
    Args:
        text: Teks yang akan diucapkan
        voice: ID voice yang ingin digunakan (optional)
        rate: Kecepatan bicara dalam WPM (default: 180)
        
    Returns:
        bool: True jika berhasil, False jika gagal
    """
    return tts_engine.speak(text, voice, rate)


def get_available_voices() -> list:
    """
    Fungsi helper untuk mendapatkan daftar voice yang tersedia
    
    Returns:
        list: Daftar voice yang tersedia
    """
    return tts_engine.get_available_voices()


def is_tts_speaking() -> bool:
    """
    Fungsi helper untuk cek apakah TTS sedang berbicara
    
    Returns:
        bool: True jika sedang berbicara, False jika tidak
    """
    return tts_engine.is_speaking


def stop_tts():
    """Fungsi helper untuk menghentikan TTS"""
    tts_engine.stop()


def get_tts_backend_info() -> dict:
    """
    Fungsi helper untuk mendapatkan info backend TTS
    
    Returns:
        dict: Informasi backend yang aktif
    """
    return tts_engine.get_backend_info()
    
    def get_available_voices(self) -> list:
        """
        Mendapatkan daftar suara yang tersedia
        
        Returns:
            list: Daftar voice yang tersedia dengan info nama dan ID
        """
        if not self.engine:
            return []
        
        try:
            voices = self.engine.getProperty('voices')
            voice_list = []
            
            for voice in voices:
                voice_info = {
                    'id': voice.id,
                    'name': voice.name,
                    'languages': getattr(voice, 'languages', []),
                    'gender': getattr(voice, 'gender', 'Unknown')
                }
                voice_list.append(voice_info)
            
            return voice_list
            
        except Exception as e:
            print(f"⚠️ Error mendapatkan daftar voice: {e}")
            return []
    
    def set_voice(self, voice_id: str) -> bool:
        """
        Mengatur voice yang digunakan
        
        Args:
            voice_id: ID voice yang ingin digunakan
            
        Returns:
            bool: True jika berhasil, False jika gagal
        """
        if not self.engine:
            return False
        
        try:
            voices = self.engine.getProperty('voices')
            for voice in voices:
                if voice.id == voice_id:
                    self.engine.setProperty('voice', voice.id)
                    print(f"✅ Voice berhasil diubah ke: {voice.name}")
                    return True
            
            print(f"⚠️ Voice dengan ID '{voice_id}' tidak ditemukan")
            return False
            
        except Exception as e:
            print(f"❌ Error mengatur voice: {e}")
            return False
    
    def set_rate(self, rate: int):
        """
        Mengatur kecepatan bicara
        
        Args:
            rate: Kecepatan dalam words per minute (default: 180)
        """
        if not self.engine:
            return
        
        try:
            self.engine.setProperty('rate', rate)
            print(f"✅ Rate TTS diubah ke: {rate} WPM")
        except Exception as e:
            print(f"❌ Error mengatur rate: {e}")
    
    def is_cooldown_active(self) -> bool:
        """
        Cek apakah masih dalam periode cooldown
        
        Returns:
            bool: True jika masih cooldown, False jika sudah boleh bicara
        """
        current_time = time.time()
        return (current_time - self.last_speak_time) < self.cooldown_seconds
    
    def speak(self, text: str, voice: Optional[str] = None, rate: Optional[int] = None) -> bool:
        """
        Mengucapkan teks dengan TTS
        
        Args:
            text: Teks yang akan diucapkan
            voice: ID voice yang ingin digunakan (optional)
            rate: Kecepatan bicara (optional)
            
        Returns:
            bool: True jika berhasil memulai TTS, False jika gagal atau sedang cooldown
        """
        if not self.engine:
            print("⚠️ TTS Engine tidak tersedia")
            return False
        
        if self.is_speaking:
            print("⚠️ TTS sedang berbicara, skip pesan baru")
            return False
        
        if self.is_cooldown_active():
            remaining = self.cooldown_seconds - (time.time() - self.last_speak_time)
            print(f"⚠️ TTS dalam cooldown, sisa {remaining:.1f} detik")
            return False
        
        try:
            # Set voice sementara jika diminta
            original_voice = None
            if voice:
                original_voice = self.engine.getProperty('voice')
                if not self.set_voice(voice):
                    print(f"⚠️ Gagal mengatur voice {voice}, menggunakan voice default")
            
            # Set rate sementara jika diminta
            original_rate = None
            if rate:
                original_rate = self.engine.getProperty('rate')
                self.set_rate(rate)
            
            # Mulai berbicara dalam thread terpisah
            def speak_thread():
                try:
                    self.is_speaking = True
                    self.engine.say(text)
                    self.engine.runAndWait()
                    
                    # Update waktu terakhir bicara
                    self.last_speak_time = time.time()
                    
                    print(f"🔊 TTS selesai: '{text[:50]}{'...' if len(text) > 50 else ''}'")
                    
                except Exception as e:
                    print(f"❌ Error saat TTS: {e}")
                finally:
                    self.is_speaking = False
                    
                    # Kembalikan pengaturan asli
                    if original_voice:
                        self.engine.setProperty('voice', original_voice)
                    if original_rate:
                        self.engine.setProperty('rate', original_rate)
            
            # Jalankan TTS dalam thread terpisah
            thread = threading.Thread(target=speak_thread, daemon=True)
            thread.start()
            
            print(f"🔊 Memulai TTS: '{text[:50]}{'...' if len(text) > 50 else ''}'")
            return True
            
        except Exception as e:
            print(f"❌ Error memulai TTS: {e}")
            self.is_speaking = False
            return False
    
    def stop(self):
        """Menghentikan TTS yang sedang berjalan"""
        if not self.engine:
            return
        
        try:
            if self.is_speaking:
                self.engine.stop()
                self.is_speaking = False
                print("🛑 TTS dihentikan")
        except Exception as e:
            print(f"❌ Error menghentikan TTS: {e}")
    
    def is_available(self) -> bool:
        """
        Cek apakah TTS engine tersedia dan siap digunakan
        
        Returns:
            bool: True jika tersedia, False jika tidak
        """
        return self.engine is not None and not self.is_speaking


# Instance global TTS engine (singleton)
tts_engine = TTSEngine()


def speak(text: str, voice: Optional[str] = None, rate: int = 180) -> bool:
    """
    Fungsi helper untuk menggunakan TTS engine global
    
    Args:
        text: Teks yang akan diucapkan
        voice: ID voice yang ingin digunakan (optional)
        rate: Kecepatan bicara dalam WPM (default: 180)
        
    Returns:
        bool: True jika berhasil, False jika gagal
    """
    return tts_engine.speak(text, voice, rate)


def get_available_voices() -> list:
    """
    Fungsi helper untuk mendapatkan daftar voice yang tersedia
    
    Returns:
        list: Daftar voice yang tersedia
    """
    return tts_engine.get_available_voices()


def is_tts_speaking() -> bool:
    """
    Fungsi helper untuk cek apakah TTS sedang berbicara
    
    Returns:
        bool: True jika sedang berbicara, False jika tidak
    """
    return tts_engine.is_speaking


def stop_tts():
    """Fungsi helper untuk menghentikan TTS"""
    tts_engine.stop()