"""
Rule Engine untuk sistem feedback adaptif ICikiwir
Mengevaluasi skor fokus dan menentukan pesan yang tepat untuk pengguna

(c) 2025 Radhitya Guntoro Adhi
"""

from typing import Optional, Dict, Any
import time
from ..config import config


class FocusRuleEngine:
    """
    Rule Engine untuk mengevaluasi skor fokus dan memberikan feedback yang sesuai
    Menggunakan aturan berbasis threshold dengan tracking perubahan skor
    """
    
    def __init__(self):
        """Inisialisasi rule engine"""
        self.previous_score = None
        self.previous_category = None
        self.last_feedback_time = 0
        self.score_history = []  # Untuk tracking perubahan
        
        # Threshold dari konfigurasi
        self.low_threshold = getattr(config.analytics, 'focus_threshold_low', 30.0)
        self.medium_threshold = getattr(config.analytics, 'focus_threshold_medium', 60.0)
        self.high_threshold = getattr(config.analytics, 'focus_threshold_high', 80.0)
        
        # Pesan feedback untuk setiap kondisi
        self.feedback_messages = {
            'very_low': [
                "Kayak<PERSON> kamu lelah, istirahat sebentar yuk.",
                "Waktunya break sejenak, mata kamu butuh istirahat.",
                "Fokus sudah menurun drastis, coba ambil napas dalam dan istirahat."
            ],
            'low': [
                "Fokus menurun, coba tarik napas dalam.",
                "Ayo fokus lagi, kamu bisa!",
                "Konsentrasi mulai buyar, coba duduk lebih tegak."
            ],
            'recovery': [
                "Good job, fokusmu kembali!",
                "Bagus! Konsentrasi sudah membaik.",
                "Excellent! Kamu berhasil fokus kembali."
            ],
            'excellent': [
                "Luar biasa! Fokusmu sangat baik.",
                "Perfect! Pertahankan konsentrasi ini.",
                "Amazing! Kamu dalam zona fokus optimal."
            ]
        }
    
    def _get_focus_category(self, score: float) -> str:
        """
        Menentukan kategori fokus berdasarkan skor
        
        Args:
            score: Skor fokus (0-100)
            
        Returns:
            str: Kategori fokus ('very_low', 'low', 'medium', 'high')
        """
        if score < self.low_threshold:
            return 'very_low'
        elif score < self.medium_threshold:
            return 'low'
        elif score < self.high_threshold:
            return 'medium'
        else:
            return 'high'
    
    def _detect_recovery(self, current_score: float, current_category: str) -> bool:
        """
        Deteksi apakah terjadi recovery (peningkatan fokus signifikan)
        
        Args:
            current_score: Skor fokus saat ini
            current_category: Kategori fokus saat ini
            
        Returns:
            bool: True jika terjadi recovery, False jika tidak
        """
        if self.previous_score is None or self.previous_category is None:
            return False
        
        # Recovery jika:
        # 1. Sebelumnya di kategori low/very_low, sekarang medium/high
        # 2. Peningkatan skor >= 20 poin dari kondisi < 60
        recovery_conditions = [
            # Dari very_low/low ke medium/high
            (self.previous_category in ['very_low', 'low'] and 
             current_category in ['medium', 'high']),
            
            # Peningkatan signifikan (>= 20 poin) dari skor rendah
            (self.previous_score < self.medium_threshold and 
             current_score >= self.medium_threshold and 
             (current_score - self.previous_score) >= 20)
        ]
        
        return any(recovery_conditions)
    
    def _should_give_feedback(self, message_type: str) -> bool:
        """
        Menentukan apakah harus memberikan feedback berdasarkan cooldown
        
        Args:
            message_type: Jenis pesan ('warning', 'recovery', 'excellent')
            
        Returns:
            bool: True jika boleh memberikan feedback, False jika masih cooldown
        """
        current_time = time.time()
        cooldown_seconds = getattr(config, 'TTS_COOLDOWN_SEC', 20)
        
        # Cooldown berbeda untuk jenis pesan berbeda
        cooldown_multipliers = {
            'warning': 1.0,      # Peringatan normal cooldown
            'recovery': 0.5,     # Recovery feedback lebih cepat
            'excellent': 2.0     # Pujian lebih jarang
        }
        
        effective_cooldown = cooldown_seconds * cooldown_multipliers.get(message_type, 1.0)
        
        return (current_time - self.last_feedback_time) >= effective_cooldown
    
    def evaluate_focus(self, score: float) -> Optional[str]:
        """
        Evaluasi skor fokus dan tentukan pesan feedback yang sesuai
        
        Args:
            score: Skor fokus (0-100)
            
        Returns:
            str | None: Pesan feedback jika perlu diberikan, None jika tidak perlu
        """
        if not isinstance(score, (int, float)) or score < 0 or score > 100:
            return None
        
        current_category = self._get_focus_category(score)
        
        # Simpan ke history
        self.score_history.append({
            'score': score,
            'category': current_category,
            'timestamp': time.time()
        })
        
        # Batasi history (simpan 10 data terakhir)
        if len(self.score_history) > 10:
            self.score_history.pop(0)
        
        message = None
        message_type = None
        
        # Logika evaluasi berdasarkan aturan
        if score < self.low_threshold:
            # Skor sangat rendah - peringatan kuat
            if self._should_give_feedback('warning'):
                message = self.feedback_messages['very_low'][0]  # Pesan utama
                message_type = 'warning'
                
        elif self.low_threshold <= score < self.medium_threshold:
            # Skor rendah - nudge ringan
            if self._should_give_feedback('warning'):
                message = self.feedback_messages['low'][0]  # Pesan utama
                message_type = 'warning'
                
        elif self._detect_recovery(score, current_category):
            # Recovery terdeteksi - berikan pujian
            if self._should_give_feedback('recovery'):
                message = self.feedback_messages['recovery'][0]  # Pesan utama
                message_type = 'recovery'
                
        elif score >= self.high_threshold:
            # Skor sangat tinggi - pujian sesekali
            if (self.previous_category != 'high' and 
                self._should_give_feedback('excellent')):
                message = self.feedback_messages['excellent'][0]  # Pesan utama
                message_type = 'excellent'
        
        # Update state untuk evaluasi berikutnya
        if message:
            self.last_feedback_time = time.time()
            print(f"🎯 Rule Engine: {current_category} (skor: {score:.1f}) -> '{message}'")
        
        self.previous_score = score
        self.previous_category = current_category
        
        return message
    
    def get_current_state(self) -> Dict[str, Any]:
        """
        Mendapatkan state saat ini dari rule engine
        
        Returns:
            dict: Informasi state saat ini
        """
        return {
            'previous_score': self.previous_score,
            'previous_category': self.previous_category,
            'last_feedback_time': self.last_feedback_time,
            'score_history_count': len(self.score_history),
            'thresholds': {
                'low': self.low_threshold,
                'medium': self.medium_threshold,
                'high': self.high_threshold
            }
        }
    
    def reset_state(self):
        """Reset state rule engine ke kondisi awal"""
        self.previous_score = None
        self.previous_category = None
        self.last_feedback_time = 0
        self.score_history.clear()
        print("🔄 Rule Engine state direset")
    
    def update_thresholds(self, low: float = None, medium: float = None, high: float = None):
        """
        Update threshold untuk evaluasi fokus
        
        Args:
            low: Threshold fokus rendah (optional)
            medium: Threshold fokus sedang (optional)
            high: Threshold fokus tinggi (optional)
        """
        if low is not None:
            self.low_threshold = low
        if medium is not None:
            self.medium_threshold = medium
        if high is not None:
            self.high_threshold = high
        
        print(f"⚙️ Threshold diupdate: low={self.low_threshold}, "
              f"medium={self.medium_threshold}, high={self.high_threshold}")


# Instance global rule engine
rule_engine = FocusRuleEngine()


def evaluate_focus(score: float) -> Optional[str]:
    """
    Fungsi helper untuk evaluasi fokus menggunakan rule engine global
    
    Args:
        score: Skor fokus (0-100)
        
    Returns:
        str | None: Pesan feedback jika perlu, None jika tidak
    """
    return rule_engine.evaluate_focus(score)


def get_rule_engine_state() -> Dict[str, Any]:
    """
    Fungsi helper untuk mendapatkan state rule engine
    
    Returns:
        dict: State saat ini
    """
    return rule_engine.get_current_state()


def reset_rule_engine():
    """Fungsi helper untuk reset rule engine"""
    rule_engine.reset_state()