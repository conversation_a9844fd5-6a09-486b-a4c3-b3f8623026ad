"""
Storage Logger untuk ICikiwir
Menyimpan metrik fokus ke SQLite dengan fallback CSV
Mendukung logging realtime dan batch flush

(c) 2025 Ra<PERSON><PERSON><PERSON>o Adhi
"""

import sqlite3
import csv
import asyncio
import time
from pathlib import Path
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, asdict
from datetime import datetime
import json
import logging
from contextlib import asynccontextmanager

try:
    import sqlite_utils
    SQLITE_UTILS_AVAILABLE = True
except ImportError:
    SQLITE_UTILS_AVAILABLE = False
    print("⚠️ sqlite-utils tidak tersedia, menggunakan sqlite3 standar")

from ..config import config


@dataclass
class FocusMetrics:
    """
    Data class untuk metrik fokus yang akan disimpan
    """
    timestamp: float
    blink_rate: float
    head_yaw_variance: float
    focus_score: float
    session_id: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Konversi ke dictionary untuk penyimpanan"""
        data = asdict(self)
        if data['metadata']:
            data['metadata'] = json.dumps(data['metadata'])
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'FocusMetrics':
        """Buat instance dari dictionary"""
        if data.get('metadata') and isinstance(data['metadata'], str):
            data['metadata'] = json.loads(data['metadata'])
        return cls(**data)


class FocusLogger:
    """
    Logger untuk menyimpan metrik fokus ke database SQLite dengan fallback CSV
    Mendukung async operations dan batch flush
    """
    
    def __init__(self, db_path: Optional[Path] = None, flush_interval: int = 30):
        """
        Inisialisasi FocusLogger
        
        Args:
            db_path: Path ke file database (default: dari config)
            flush_interval: Interval flush dalam detik
        """
        self.db_path = db_path or config.get_database_path()
        self.flush_interval = flush_interval
        self.csv_fallback_path = self.db_path.parent / "focus_metrics_fallback.csv"
        
        # Buffer untuk batch operations
        self._buffer: List[FocusMetrics] = []
        self._buffer_lock = asyncio.Lock()
        self._flush_task: Optional[asyncio.Task] = None
        self._running = False
        
        # Logger
        self.logger = logging.getLogger(__name__)
        
        # Inisialisasi database
        self._init_database()
        
    def _init_database(self):
        """Inisialisasi database dan tabel"""
        try:
            # Buat direktori jika belum ada
            self.db_path.parent.mkdir(parents=True, exist_ok=True)
            
            if SQLITE_UTILS_AVAILABLE:
                self._init_with_sqlite_utils()
            else:
                self._init_with_sqlite3()
                
            self.logger.info(f"✅ Database berhasil diinisialisasi: {self.db_path}")
            
        except Exception as e:
            self.logger.error(f"❌ Gagal inisialisasi database: {e}")
            self.logger.info("🔄 Akan menggunakan CSV fallback")
    
    def _init_with_sqlite_utils(self):
        """Inisialisasi menggunakan sqlite-utils"""
        import sqlite_utils
        
        db = sqlite_utils.Database(self.db_path)
        
        # Buat tabel focus_metrics jika belum ada
        if "focus_metrics" not in db.table_names():
            db["focus_metrics"].create({
                "id": int,
                "timestamp": float,
                "blink_rate": float,
                "head_yaw_variance": float,
                "focus_score": float,
                "session_id": str,
                "metadata": str,
                "created_at": str
            }, pk="id")
            
            # Buat index untuk query yang sering digunakan
            db["focus_metrics"].create_index(["timestamp"])
            db["focus_metrics"].create_index(["session_id"])
            db["focus_metrics"].create_index(["created_at"])
    
    def _init_with_sqlite3(self):
        """Inisialisasi menggunakan sqlite3 standar"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS focus_metrics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp REAL NOT NULL,
                    blink_rate REAL NOT NULL,
                    head_yaw_variance REAL NOT NULL,
                    focus_score REAL NOT NULL,
                    session_id TEXT,
                    metadata TEXT,
                    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Buat index
            conn.execute("CREATE INDEX IF NOT EXISTS idx_timestamp ON focus_metrics(timestamp)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_session_id ON focus_metrics(session_id)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_created_at ON focus_metrics(created_at)")
            
            conn.commit()
    
    async def start(self):
        """Mulai logger dengan background flush task"""
        if self._running:
            return
            
        self._running = True
        self._flush_task = asyncio.create_task(self._flush_loop())
        self.logger.info("🚀 FocusLogger dimulai")
    
    async def stop(self):
        """Hentikan logger dan flush buffer terakhir"""
        if not self._running:
            return
            
        self._running = False
        
        # Cancel flush task
        if self._flush_task:
            self._flush_task.cancel()
            try:
                await self._flush_task
            except asyncio.CancelledError:
                pass
        
        # Flush buffer terakhir
        await self._flush_buffer()
        self.logger.info("🛑 FocusLogger dihentikan")
    
    async def log_focus(self, metrics: FocusMetrics):
        """
        Log metrik fokus ke buffer
        
        Args:
            metrics: Data metrik fokus
        """
        async with self._buffer_lock:
            self._buffer.append(metrics)
            
        # Log untuk debugging
        self.logger.debug(f"📝 Metrik ditambahkan ke buffer: focus_score={metrics.focus_score:.1f}")
    
    async def _flush_loop(self):
        """Background loop untuk flush buffer secara berkala"""
        while self._running:
            try:
                await asyncio.sleep(self.flush_interval)
                await self._flush_buffer()
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"❌ Error dalam flush loop: {e}")
    
    async def _flush_buffer(self):
        """Flush buffer ke database"""
        if not self._buffer:
            return
            
        async with self._buffer_lock:
            buffer_copy = self._buffer.copy()
            self._buffer.clear()
        
        if not buffer_copy:
            return
            
        try:
            # Coba simpan ke SQLite
            await self._save_to_sqlite(buffer_copy)
            self.logger.info(f"💾 {len(buffer_copy)} metrik berhasil disimpan ke database")
            
        except Exception as e:
            self.logger.error(f"❌ Gagal simpan ke SQLite: {e}")
            
            # Fallback ke CSV
            try:
                await self._save_to_csv(buffer_copy)
                self.logger.info(f"📄 {len(buffer_copy)} metrik disimpan ke CSV fallback")
            except Exception as csv_error:
                self.logger.error(f"❌ Gagal simpan ke CSV: {csv_error}")
    
    async def _save_to_sqlite(self, metrics_list: List[FocusMetrics]):
        """Simpan metrik ke SQLite database"""
        def _save_sync():
            if SQLITE_UTILS_AVAILABLE:
                self._save_with_sqlite_utils(metrics_list)
            else:
                self._save_with_sqlite3(metrics_list)
        
        # Jalankan di thread pool untuk menghindari blocking
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(None, _save_sync)
    
    def _save_with_sqlite_utils(self, metrics_list: List[FocusMetrics]):
        """Simpan menggunakan sqlite-utils"""
        import sqlite_utils
        
        db = sqlite_utils.Database(self.db_path)
        
        # Konversi ke format yang sesuai
        records = []
        for metrics in metrics_list:
            record = metrics.to_dict()
            record['created_at'] = datetime.fromtimestamp(metrics.timestamp).isoformat()
            records.append(record)
        
        # Insert batch
        db["focus_metrics"].insert_all(records)
    
    def _save_with_sqlite3(self, metrics_list: List[FocusMetrics]):
        """Simpan menggunakan sqlite3 standar"""
        with sqlite3.connect(self.db_path) as conn:
            for metrics in metrics_list:
                conn.execute("""
                    INSERT INTO focus_metrics 
                    (timestamp, blink_rate, head_yaw_variance, focus_score, session_id, metadata, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    metrics.timestamp,
                    metrics.blink_rate,
                    metrics.head_yaw_variance,
                    metrics.focus_score,
                    metrics.session_id,
                    json.dumps(metrics.metadata) if metrics.metadata else None,
                    datetime.fromtimestamp(metrics.timestamp).isoformat()
                ))
            conn.commit()
    
    async def _save_to_csv(self, metrics_list: List[FocusMetrics]):
        """Simpan metrik ke CSV sebagai fallback"""
        def _save_csv_sync():
            # Cek apakah file sudah ada untuk menentukan perlu header atau tidak
            file_exists = self.csv_fallback_path.exists()
            
            with open(self.csv_fallback_path, 'a', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['timestamp', 'blink_rate', 'head_yaw_variance', 'focus_score', 
                             'session_id', 'metadata', 'created_at']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                # Tulis header jika file baru
                if not file_exists:
                    writer.writeheader()
                
                # Tulis data
                for metrics in metrics_list:
                    row = metrics.to_dict()
                    row['created_at'] = datetime.fromtimestamp(metrics.timestamp).isoformat()
                    writer.writerow(row)
        
        # Jalankan di thread pool
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(None, _save_csv_sync)
    
    async def query_metrics(self, 
                           start_time: Optional[float] = None,
                           end_time: Optional[float] = None,
                           session_id: Optional[str] = None,
                           limit: Optional[int] = None) -> List[FocusMetrics]:
        """
        Query metrik dari database
        
        Args:
            start_time: Timestamp mulai (optional)
            end_time: Timestamp akhir (optional)
            session_id: ID sesi (optional)
            limit: Batas jumlah record (optional)
            
        Returns:
            List[FocusMetrics]: Daftar metrik
        """
        def _query_sync():
            if SQLITE_UTILS_AVAILABLE:
                return self._query_with_sqlite_utils(start_time, end_time, session_id, limit)
            else:
                return self._query_with_sqlite3(start_time, end_time, session_id, limit)
        
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, _query_sync)
    
    def _query_with_sqlite_utils(self, start_time, end_time, session_id, limit) -> List[FocusMetrics]:
        """Query menggunakan sqlite-utils"""
        import sqlite_utils
        
        db = sqlite_utils.Database(self.db_path)
        
        # Build query
        where_conditions = []
        params = {}
        
        if start_time:
            where_conditions.append("timestamp >= :start_time")
            params['start_time'] = start_time
            
        if end_time:
            where_conditions.append("timestamp <= :end_time")
            params['end_time'] = end_time
            
        if session_id:
            where_conditions.append("session_id = :session_id")
            params['session_id'] = session_id
        
        where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"
        
        query = f"SELECT * FROM focus_metrics WHERE {where_clause} ORDER BY timestamp DESC"
        if limit:
            query += f" LIMIT {limit}"
        
        rows = db.execute(query, params).fetchall()
        
        # Konversi ke FocusMetrics
        metrics_list = []
        for row in rows:
            row_dict = dict(row)
            if row_dict.get('metadata'):
                row_dict['metadata'] = json.loads(row_dict['metadata'])
            # Hapus field yang tidak ada di FocusMetrics
            row_dict.pop('id', None)
            row_dict.pop('created_at', None)
            metrics_list.append(FocusMetrics.from_dict(row_dict))
        
        return metrics_list
    
    def _query_with_sqlite3(self, start_time, end_time, session_id, limit) -> List[FocusMetrics]:
        """Query menggunakan sqlite3 standar"""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            
            # Build query
            where_conditions = []
            params = []
            
            if start_time:
                where_conditions.append("timestamp >= ?")
                params.append(start_time)
                
            if end_time:
                where_conditions.append("timestamp <= ?")
                params.append(end_time)
                
            if session_id:
                where_conditions.append("session_id = ?")
                params.append(session_id)
            
            where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"
            
            query = f"SELECT * FROM focus_metrics WHERE {where_clause} ORDER BY timestamp DESC"
            if limit:
                query += f" LIMIT {limit}"
            
            cursor = conn.execute(query, params)
            rows = cursor.fetchall()
            
            # Konversi ke FocusMetrics
            metrics_list = []
            for row in rows:
                row_dict = dict(row)
                if row_dict.get('metadata'):
                    row_dict['metadata'] = json.loads(row_dict['metadata'])
                # Hapus field yang tidak ada di FocusMetrics
                row_dict.pop('id', None)
                row_dict.pop('created_at', None)
                metrics_list.append(FocusMetrics.from_dict(row_dict))
            
            return metrics_list
    
    async def export_to_csv(self, 
                           output_path: Path,
                           start_time: Optional[float] = None,
                           end_time: Optional[float] = None,
                           session_id: Optional[str] = None) -> int:
        """
        Export metrik ke file CSV
        
        Args:
            output_path: Path file output CSV
            start_time: Timestamp mulai (optional)
            end_time: Timestamp akhir (optional)
            session_id: ID sesi (optional)
            
        Returns:
            int: Jumlah record yang diekspor
        """
        metrics_list = await self.query_metrics(start_time, end_time, session_id)
        
        def _export_sync():
            with open(output_path, 'w', newline='', encoding='utf-8') as csvfile:
                if not metrics_list:
                    return 0
                    
                fieldnames = ['timestamp', 'blink_rate', 'head_yaw_variance', 'focus_score', 
                             'session_id', 'metadata', 'datetime']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                
                for metrics in metrics_list:
                    row = metrics.to_dict()
                    row['datetime'] = datetime.fromtimestamp(metrics.timestamp).isoformat()
                    writer.writerow(row)
                    
            return len(metrics_list)
        
        loop = asyncio.get_event_loop()
        count = await loop.run_in_executor(None, _export_sync)
        
        self.logger.info(f"📤 {count} metrik berhasil diekspor ke {output_path}")
        return count
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Mendapatkan statistik logger
        
        Returns:
            Dict: Statistik logger
        """
        return {
            'db_path': str(self.db_path),
            'csv_fallback_path': str(self.csv_fallback_path),
            'flush_interval': self.flush_interval,
            'buffer_size': len(self._buffer),
            'running': self._running,
            'sqlite_utils_available': SQLITE_UTILS_AVAILABLE
        }


# Instance global logger
logger = FocusLogger(
    flush_interval=getattr(config, 'LOG_FLUSH_SEC', 30)
)