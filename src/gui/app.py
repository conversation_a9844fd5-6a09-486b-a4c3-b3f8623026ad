"""
Aplikasi GUI utama menggunakan Flet framework
Real-Time Focus Monitor dengan live video feed dan skor fokus

(c) 2025 Radhitya Guntoro Adhi
"""

import flet as ft
import cv2
import numpy as np
import threading
import time
import queue
import base64
from io import BytesIO
from PIL import Image

# Import dengan absolute path untuk menghindari relative import error
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.capture.stream import WebcamStream
from src.vision.face_tracker import FaceTracker
from src.analytics.focus_metric import FocusMetricCalculator
from src.analytics.logger_plugin import analytics_logger_plugin
from src.intervention.tts_engine import tts_engine, is_tts_speaking
from src.intervention.rules import evaluate_focus, reset_rule_engine
from src.config import config, REPORT_DIR
from src.gui.components.dashboard import DashboardComponent
from src.gui.components.markdown_viewer import ReportListViewer

class FocusMonitorApp:
    """
    Kelas utama untuk aplikasi Real-Time Focus Monitor
    Mengintegrasikan webcam, face tracking, dan analytics
    """
    
    def __init__(self, page: ft.Page):
        """
        Inisialisasi aplikasi
        
        Args:
            page: Instance halaman Flet
        """
        self.page = page
        self.setup_page()
        
        # Komponen utama
        self.webcam_stream = None
        self.face_tracker = FaceTracker()
        self.focus_calculator = FocusMetricCalculator(
            blink_window_sec=config.analytics.blink_window_sec,
            head_var_window=config.analytics.head_var_window,
            normal_blink_range=config.analytics.blink_rate_normal_range
        )
        
        # Threading dan queues
        self.frame_queue = queue.Queue(maxsize=10)
        self.analytics_queue = queue.Queue(maxsize=100)
        self.is_running = False
        self.processing_thread = None
        
        # TTS state
        self.tts_enabled = getattr(config, 'TTS_ENABLED', True)
        
        # Dashboard component
        self.dashboard = DashboardComponent(page)
        
        # Report viewer component
        self.report_viewer = ReportListViewer(page, REPORT_DIR)
        
        # UI Components - dengan placeholder image
        self.video_image = ft.Image(
            width=640,
            height=480,
            fit=ft.ImageFit.CONTAIN,
            border_radius=10,
            src_base64=""  # Empty base64 to prevent error
        )
        
        # Placeholder untuk video feed
        self.video_placeholder = ft.Container(
            content=ft.Column([
                ft.Icon(
                    ft.icons.VIDEOCAM_OFF,
                    size=80,
                    color=ft.colors.GREY_400
                ),
                ft.Text(
                    "Video akan ditampilkan ketika monitoring dimulai",
                    size=16,
                    color=ft.colors.GREY_600,
                    text_align=ft.TextAlign.CENTER
                )
            ],
            horizontal_alignment=ft.CrossAxisAlignment.CENTER,
            alignment=ft.MainAxisAlignment.CENTER),
            width=640,
            height=480,
            bgcolor=ft.colors.GREY_100,
            border_radius=10,
            alignment=ft.alignment.center
        )
        
        self.focus_score_text = ft.Text(
            "Skor Fokus: --",
            size=24,
            weight=ft.FontWeight.BOLD,
            color=ft.colors.BLUE_700
        )
        
        self.blink_rate_text = ft.Text(
            "Blink Rate: -- per menit",
            size=16,
            color=ft.colors.GREEN_700
        )
        
        self.head_pose_text = ft.Text(
            "Head Pose: Yaw: --, Pitch: --",
            size=16,
            color=ft.colors.ORANGE_700
        )
        
        self.expression_text = ft.Text(
            "Ekspresi: --",
            size=16,
            color=ft.colors.PURPLE_700
        )
        
        # Behavior analysis texts
        self.activity_text = ft.Text(
            "Aktivitas: --",
            size=16,
            color=ft.colors.INDIGO_700
        )
        
        self.posture_text = ft.Text(
            "Postur: --",
            size=16,
            color=ft.colors.TEAL_700
        )
        
        self.cognitive_load_text = ft.Text(
            "Beban Kognitif: --",
            size=16,
            color=ft.colors.DEEP_ORANGE_700
        )
        
        # Enhanced system status texts
        self.status_text = ft.Text(
            "Status: Siap",
            size=14,
            color=ft.colors.GREY_600
        )
        
        self.fps_text = ft.Text(
            "FPS: --",
            size=12,
            color=ft.colors.GREY_500
        )
        
        # Continuous monitoring status
        self.monitoring_mode_text = ft.Text(
            "Mode: Standby",
            size=12,
            color=ft.colors.BLUE_600
        )
        
        self.fallback_status_text = ft.Text(
            "Fallback: Tidak Aktif",
            size=12,
            color=ft.colors.GREEN_600
        )
        
        # TTS Engine status
        self.tts_backend_text = ft.Text(
            "TTS Backend: --",
            size=12,
            color=ft.colors.PURPLE_600
        )
        
        self.tts_queue_text = ft.Text(
            "TTS Queue: 0",
            size=12,
            color=ft.colors.PURPLE_600
        )
        
        # Detection components status
        self.face_detection_text = ft.Text(
            "Face Detection: Standby",
            size=12,
            color=ft.colors.ORANGE_600
        )
        
        self.micro_expression_text = ft.Text(
            "Micro Expression: Standby",
            size=12,
            color=ft.colors.PINK_600
        )
        
        self.behavior_detection_text = ft.Text(
            "Behavior Analysis: Standby",
            size=12,
            color=ft.colors.CYAN_600
        )
        
        # System performance metrics
        self.data_points_text = ft.Text(
            "Data Points: 0",
            size=12,
            color=ft.colors.GREY_500
        )
        
        self.calculations_text = ft.Text(
            "Calculations: 0",
            size=12,
            color=ft.colors.GREY_500
        )
        
        # Control buttons
        self.start_button = ft.ElevatedButton(
            "Mulai Monitoring",
            icon=ft.icons.PLAY_ARROW,
            on_click=self.start_monitoring,
            color=ft.colors.WHITE,
            bgcolor=ft.colors.GREEN_600
        )
        
        self.stop_button = ft.ElevatedButton(
            "Berhenti",
            icon=ft.icons.STOP,
            on_click=self.stop_monitoring,
            color=ft.colors.WHITE,
            bgcolor=ft.colors.RED_600,
            disabled=True
        )
        
        # TTS toggle switch
        self.tts_switch = ft.Switch(
            label="Speech Feedback",
            value=self.tts_enabled,
            on_change=self.toggle_tts
        )
        
        # Progress bar untuk focus score
        self.focus_progress = ft.ProgressBar(
            width=300,
            height=20,
            value=0.5,
            color=ft.colors.BLUE_600,
            bgcolor=ft.colors.GREY_300
        )
        
        # Focus level indicator
        self.focus_indicator = ft.Container(
            content=ft.Text(
                "Fokus Sedang ⚠️",
                size=16,
                weight=ft.FontWeight.W_500,
                color=ft.colors.ORANGE_600,
                text_align=ft.TextAlign.CENTER
            ),
            width=300,
            padding=15,
            bgcolor=ft.colors.ORANGE_50,
            border_radius=10,
            border=ft.border.all(2, ft.colors.ORANGE_200)
        )
        
        self.setup_ui()
        
    def setup_page(self):
        """Setup konfigurasi halaman"""
        self.page.title = "ICikiwir - Real-Time Focus Monitor"
        self.page.theme_mode = ft.ThemeMode.LIGHT
        self.page.window_width = 1200
        self.page.window_height = 800
        self.page.window_resizable = True
        self.page.padding = 20
        
    def setup_ui(self):
        """Setup antarmuka pengguna dengan sistem tab"""
        # Tab untuk Monitor
        monitor_tab = ft.Tab(
            text="Monitor",
            icon=ft.icons.VISIBILITY,
            content=self.build_monitor_content()
        )
        
        # Tab untuk Dashboard
        dashboard_tab = ft.Tab(
            text="Dashboard",
            icon=ft.icons.DASHBOARD,
            content=self.dashboard.get_dashboard_content()
        )
        
        # Tab untuk Reports
        reports_tab = ft.Tab(
            text="Reports",
            icon=ft.icons.DESCRIPTION,
            content=self.report_viewer
        )
        
        # Tabs container
        tabs = ft.Tabs(
            selected_index=0,
            animation_duration=300,
            tabs=[monitor_tab, dashboard_tab, reports_tab],
            expand=True
        )
        
        # Add to page
        self.page.add(tabs)
        self.page.update()
    
    def build_monitor_content(self):
        """Membangun konten tab Monitor"""
        # Header
        header = ft.Container(
            content=ft.Row([
                ft.Column([
                    ft.Text(
                        "🧠 ICikiwir Focus Monitor",
                        size=28,
                        weight=ft.FontWeight.BOLD,
                        color=ft.colors.BLUE_700
                    ),
                    ft.Text(
                        "Real-Time Cognitive Awareness System",
                        size=14,
                        color=ft.colors.GREY_600
                    )
                ], expand=True),
                ft.Column([
                    ft.Text(
                        "(c) 2025 Radhitya Guntoro Adhi",
                        size=12,
                        color=ft.colors.GREY_500,
                        text_align=ft.TextAlign.RIGHT
                    ),
                    self.fps_text
                ], horizontal_alignment=ft.CrossAxisAlignment.END)
            ]),
            padding=ft.padding.only(bottom=20)
        )
        
        # Video dan kontrol panel
        video_panel = ft.Container(
            content=ft.Column([
                ft.Text(
                    "Live Video Feed",
                    size=18,
                    weight=ft.FontWeight.W_500,
                    color=ft.colors.BLUE_600
                ),
                # Stack untuk video dan placeholder - perbaiki struktur
                ft.Stack([
                    self.video_placeholder,
                    self.video_image  # Direct reference tanpa Container wrapper
                ], width=640, height=480),
                ft.Row([
                    self.start_button,
                    self.stop_button
                ], alignment=ft.MainAxisAlignment.CENTER, spacing=10),
                ft.Container(
                    content=self.tts_switch,
                    padding=ft.padding.only(top=10),
                    alignment=ft.alignment.center
                )
            ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
            width=680,
            padding=20,
            bgcolor=ft.colors.WHITE,
            border_radius=15,
            shadow=ft.BoxShadow(
                spread_radius=1,
                blur_radius=10,
                color=ft.colors.with_opacity(0.1, ft.colors.BLACK)
            )
        )
        
        # Metrics panel
        metrics_panel = ft.Container(
            content=ft.Column([
                ft.Text(
                    "Metrik Fokus Real-Time",
                    size=18,
                    weight=ft.FontWeight.W_500,
                    color=ft.colors.GREEN_600
                ),
                ft.Divider(),
                
                # Focus Score
                ft.Container(
                    content=ft.Column([
                        self.focus_score_text,
                        self.focus_progress
                    ]),
                    padding=ft.padding.symmetric(vertical=10)
                ),
                
                ft.Divider(),
                
                # Detailed metrics
                ft.Column([
                    self.blink_rate_text,
                    self.head_pose_text,
                    self.expression_text,
                    ft.Divider(height=5, color=ft.colors.TRANSPARENT),
                    # Behavior analysis section
                    ft.Text(
                        "Analisis Perilaku",
                        size=14,
                        weight=ft.FontWeight.W_500,
                        color=ft.colors.GREY_700
                    ),
                    self.activity_text,
                    self.posture_text,
                    self.cognitive_load_text,
                    ft.Divider(height=10, color=ft.colors.TRANSPARENT),
                    
                    # Enhanced System Status Section
                    ft.Text(
                        "Status Sistem",
                        size=14,
                        weight=ft.FontWeight.W_500,
                        color=ft.colors.BLUE_700
                    ),
                    self.status_text,
                    self.monitoring_mode_text,
                    self.fallback_status_text,
                    ft.Divider(height=5, color=ft.colors.TRANSPARENT),
                    
                    # Detection Components Status
                    ft.Text(
                        "Komponen Deteksi",
                        size=12,
                        weight=ft.FontWeight.W_500,
                        color=ft.colors.ORANGE_700
                    ),
                    self.face_detection_text,
                    self.micro_expression_text,
                    self.behavior_detection_text,
                    ft.Divider(height=5, color=ft.colors.TRANSPARENT),
                    
                    # TTS Engine Status
                    ft.Text(
                        "TTS Engine",
                        size=12,
                        weight=ft.FontWeight.W_500,
                        color=ft.colors.PURPLE_700
                    ),
                    self.tts_backend_text,
                    self.tts_queue_text,
                    ft.Divider(height=5, color=ft.colors.TRANSPARENT),
                    
                    # Performance Metrics
                    ft.Text(
                        "Performa Sistem",
                        size=12,
                        weight=ft.FontWeight.W_500,
                        color=ft.colors.GREY_700
                    ),
                    self.data_points_text,
                    self.calculations_text
                ], spacing=8),
                
                ft.Divider(),
                
                # Focus level indicator
                self.focus_indicator
            ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
            width=350,
            padding=20,
            bgcolor=ft.colors.WHITE,
            border_radius=15,
            shadow=ft.BoxShadow(
                spread_radius=1,
                blur_radius=10,
                color=ft.colors.with_opacity(0.1, ft.colors.BLACK)
            )
        )
        
        # Main layout
        main_content = ft.Row([
            video_panel,
            metrics_panel
        ], alignment=ft.MainAxisAlignment.CENTER, spacing=20)
        
        return ft.Container(
            content=ft.Column([
                header,
                main_content
            ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
            padding=20,
            expand=True
        )
    
    def start_monitoring(self, e):
        """Memulai monitoring fokus"""
        if self.is_running:
            return
            
        try:
            # Inisialisasi webcam
            self.webcam_stream = WebcamStream(
                src=config.camera.default_camera_index,
                resolution=(config.camera.resolution_width, config.camera.resolution_height)
            )
            
            if not self.webcam_stream.start():
                self.show_error("Gagal memulai kamera. Pastikan kamera tersedia.")
                return
            
            # Reset calculator dan rule engine
            self.focus_calculator.reset_data()
            reset_rule_engine()
            
            # Start processing thread
            self.is_running = True
            self.processing_thread = threading.Thread(target=self.processing_loop, daemon=True)
            self.processing_thread.start()
            
            # Update UI
            self.start_button.disabled = True
            self.stop_button.disabled = False
            self.status_text.value = "Status: Monitoring aktif 🟢"
            self.status_text.color = ft.colors.GREEN_600
            
            # Show video, hide placeholder - perbaiki Stack visibility
            self.video_placeholder.visible = False
            self.video_image.visible = True
            
            self.page.update()
            
            print("✅ Focus monitoring dimulai")
            
        except Exception as ex:
            self.show_error(f"Error memulai monitoring: {ex}")
    
    def stop_monitoring(self, e):
        """Menghentikan monitoring fokus"""
        if not self.is_running:
            return
            
        try:
            # Stop processing
            self.is_running = False
            
            # Stop webcam
            if self.webcam_stream:
                self.webcam_stream.stop()
                self.webcam_stream = None
            
            # Wait for thread to finish
            if self.processing_thread and self.processing_thread.is_alive():
                self.processing_thread.join(timeout=2.0)
            
            # Clear queues
            while not self.frame_queue.empty():
                try:
                    self.frame_queue.get_nowait()
                except queue.Empty:
                    break
                    
            while not self.analytics_queue.empty():
                try:
                    self.analytics_queue.get_nowait()
                except queue.Empty:
                    break
            
            # Update UI
            self.start_button.disabled = False
            self.stop_button.disabled = True
            self.status_text.value = "Status: Monitoring dihentikan 🔴"
            self.status_text.color = ft.colors.RED_600
            
            # Reset video display - show placeholder, hide video
            self.video_image.src_base64 = ""
            self.video_image.visible = False
            self.video_placeholder.visible = True
            
            self.page.update()
            
            print("🛑 Focus monitoring dihentikan")
            
        except Exception as ex:
            self.show_error(f"Error menghentikan monitoring: {ex}")
    
    def processing_loop(self):
        """Loop utama untuk memproses frame dan analytics"""
        print("🔄 Memulai processing loop...")
        
        last_update_time = time.time()
        frame_count = 0
        
        while self.is_running:
            try:
                if not self.webcam_stream or not self.webcam_stream.is_active():
                    time.sleep(0.1)
                    continue
                
                # Ambil frame dari webcam
                frame = self.webcam_stream.get_frame()
                if frame is None:
                    time.sleep(0.033)  # ~30 FPS
                    continue
                
                # Process dengan face tracker
                analysis_result = self.face_tracker.process_frame(frame)
                
                # Ambil data untuk continuous monitoring
                face_detected = analysis_result['face_detected']
                micro_expressions = analysis_result.get('micro_expressions', {})
                expression_focus_score = analysis_result.get('expression_focus_score', None)
                behavior_focus_impact = analysis_result.get('behavior_focus_impact', 0.0)
                behavior_data = analysis_result.get('behavior_data', {})
                
                # Update focus calculator dengan continuous monitoring support
                self.focus_calculator.add_frame_data(
                    analysis_result.get('ear_average'),
                    analysis_result.get('head_pose'),
                    micro_expressions=micro_expressions,
                    expression_focus_score=expression_focus_score,
                    behavior_focus_impact=behavior_focus_impact,
                    face_detected=face_detected,
                    behavior_data=behavior_data
                )
                
                # Update UI setiap detik
                current_time = time.time()
                if current_time - last_update_time >= 1.0:
                    self.update_ui_metrics(analysis_result)
                    last_update_time = current_time
                
                # Update video feed (setiap frame)
                self.update_video_display(analysis_result['frame_with_annotations'])
                
                frame_count += 1
                
                # Control frame rate
                time.sleep(0.033)  # ~30 FPS
                
            except Exception as ex:
                print(f"⚠️ Error dalam processing loop: {ex}")
                time.sleep(0.1)
        
        print("🏁 Processing loop selesai")
    
    def update_video_display(self, frame):
        """Update tampilan video dengan frame terbaru"""
        try:
            # Resize frame untuk display
            display_frame = cv2.resize(frame, (640, 480))
            
            # Convert BGR to RGB
            rgb_frame = cv2.cvtColor(display_frame, cv2.COLOR_BGR2RGB)
            
            # Convert to PIL Image
            pil_image = Image.fromarray(rgb_frame)
            
            # Convert to base64 untuk Flet
            buffer = BytesIO()
            pil_image.save(buffer, format='JPEG', quality=85)
            img_base64 = base64.b64encode(buffer.getvalue()).decode()
            
            # Update UI (thread-safe)
            def update_ui():
                self.video_image.src_base64 = img_base64
                self.page.update()
            
            update_ui()
            
        except Exception as ex:
            print(f"⚠️ Error update video display: {ex}")
    
    def update_ui_metrics(self, analysis_result):
        """Update metrik UI dengan data terbaru dan enhanced overlay system"""
        try:
            # Get current metrics dengan fallback support
            face_detected = analysis_result.get('face_detected', False)
            behavior_data = analysis_result.get('behavior_data', {})
            
            # Gunakan compute_focus_with_fallback untuk continuous monitoring
            focus_result = self.focus_calculator.compute_focus_with_fallback(
                face_detected=face_detected,
                behavior_data=behavior_data
            )
            
            # Get regular metrics
            metrics = self.focus_calculator.get_current_metrics()
            
            # Get TTS backend info
            try:
                from src.intervention.tts_engine import get_tts_backend_info
                tts_backend_info = get_tts_backend_info()
            except:
                tts_backend_info = {"name": "Unknown", "queue_size": 0}
            
            # Get system statistics
            stats = self.focus_calculator.get_statistics()
            
            # Update FPS
            fps = self.webcam_stream.get_fps() if self.webcam_stream else 0
            
            def update_ui():
                # Focus score dengan fallback info
                focus_score = focus_result['focus_score']
                confidence = focus_result.get('confidence', 1.0)
                method = focus_result.get('method', 'face_based')
                fallback_used = focus_result.get('fallback_used', False)
                
                confidence_indicator = f" (Confidence: {confidence:.1%})" if confidence < 1.0 else ""
                self.focus_score_text.value = f"Skor Fokus: {focus_score:.1f}{confidence_indicator}"
                
                # TTS Feedback - evaluasi dan berikan feedback jika perlu
                if self.tts_enabled and tts_engine.is_available():
                    feedback_message = evaluate_focus(focus_score)
                    if feedback_message and not is_tts_speaking():
                        tts_engine.speak(feedback_message)
                
                # Progress bar
                self.focus_progress.value = focus_score / 100.0
                
                # Color coding untuk focus score
                if focus_score >= 80:
                    self.focus_score_text.color = ft.colors.GREEN_700
                    self.focus_progress.color = ft.colors.GREEN_600
                elif focus_score >= 60:
                    self.focus_score_text.color = ft.colors.BLUE_700
                    self.focus_progress.color = ft.colors.BLUE_600
                elif focus_score >= 40:
                    self.focus_score_text.color = ft.colors.ORANGE_700
                    self.focus_progress.color = ft.colors.ORANGE_600
                else:
                    self.focus_score_text.color = ft.colors.RED_700
                    self.focus_progress.color = ft.colors.RED_600
                
                # Blink rate
                blink_rate = metrics['blink_rate']
                self.blink_rate_text.value = f"Blink Rate: {blink_rate:.1f} per menit"
                
                # Enhanced System Status
                monitoring_mode = "Face-Based" if face_detected else "Behavior-Based"
                self.monitoring_mode_text.value = f"Mode: {monitoring_mode}"
                self.monitoring_mode_text.color = ft.colors.GREEN_600 if face_detected else ft.colors.ORANGE_600
                
                fallback_status = "Aktif" if fallback_used else "Tidak Aktif"
                self.fallback_status_text.value = f"Fallback: {fallback_status}"
                self.fallback_status_text.color = ft.colors.ORANGE_600 if fallback_used else ft.colors.GREEN_600
                
                # Detection Components Status
                face_status = "Aktif ✅" if face_detected else "Standby ⏸️"
                self.face_detection_text.value = f"Face Detection: {face_status}"
                self.face_detection_text.color = ft.colors.GREEN_600 if face_detected else ft.colors.ORANGE_600
                
                micro_expr_active = face_detected and analysis_result.get('micro_expressions')
                micro_expr_status = "Aktif ✅" if micro_expr_active else "Standby ⏸️"
                self.micro_expression_text.value = f"Micro Expression: {micro_expr_status}"
                self.micro_expression_text.color = ft.colors.GREEN_600 if micro_expr_active else ft.colors.ORANGE_600
                
                behavior_active = bool(behavior_data)
                behavior_status = "Aktif ✅" if behavior_active else "Standby ⏸️"
                self.behavior_detection_text.value = f"Behavior Analysis: {behavior_status}"
                self.behavior_detection_text.color = ft.colors.GREEN_600 if behavior_active else ft.colors.ORANGE_600
                
                # TTS Engine Status
                backend_name = tts_backend_info.get("name", "Unknown")
                queue_size = tts_backend_info.get("queue_size", 0)
                self.tts_backend_text.value = f"TTS Backend: {backend_name}"
                self.tts_queue_text.value = f"TTS Queue: {queue_size}"
                
                # Performance Metrics
                data_points = stats.get('head_pose_data_points', 0)
                calculations = stats.get('total_calculations', 0)
                self.data_points_text.value = f"Data Points: {data_points}"
                self.calculations_text.value = f"Calculations: {calculations}"
                
                # Head pose
                if face_detected:
                    head_pose = analysis_result['head_pose']
                    self.head_pose_text.value = f"Head Pose: Yaw: {head_pose['yaw']:.1f}°, Pitch: {head_pose['pitch']:.1f}°"
                    
                    # Mikro ekspresi
                    dominant_expression = analysis_result.get('dominant_expression', 'neutral')
                    expression_focus_score = analysis_result.get('expression_focus_score', 50.0)
                    
                    # Emoji mapping untuk ekspresi
                    expression_emoji = {
                        'neutral': '😐',
                        'happy': '😊',
                        'sad': '😢',
                        'surprised': '😲',
                        'focused': '🧐',
                        'confused': '😕',
                        'tired': '😴'
                    }
                    
                    # Pastikan dominant_expression adalah string sebelum memanggil .title()
                    if isinstance(dominant_expression, str):
                        expression_display = dominant_expression.title()
                    else:
                        expression_display = str(dominant_expression).title()
                    
                    emoji = expression_emoji.get(dominant_expression, '😐')
                    self.expression_text.value = f"Ekspresi: {expression_display} {emoji} (Skor: {expression_focus_score:.1f})"
                    
                    # Behavior analysis
                    current_activity = analysis_result.get('current_activity', 'tidak_diketahui')
                    posture_quality = analysis_result.get('posture_quality', 'tidak_diketahui')
                    cognitive_load = analysis_result.get('cognitive_load', 'tidak_diketahui')
                    behavior_focus_impact = analysis_result.get('behavior_focus_impact', 0.0)
                    
                    # Activity mapping dengan emoji
                    activity_emoji = {
                        'menulis': '✍️',
                        'mengetik': '⌨️',
                        'minum': '🥤',
                        'merokok': '🚬',
                        'menggunakan_ponsel': '📱',
                        'normal': '👤',
                        'tidak_diketahui': '❓'
                    }
                    
                    # Posture quality mapping
                    posture_emoji = {
                        'baik': '🟢',
                        'sedang': '🟡',
                        'buruk': '🔴',
                        'tidak_diketahui': '⚪'
                    }
                    
                    # Cognitive load mapping
                    cognitive_emoji = {
                        'rendah': '🟢',
                        'sedang': '🟡',
                        'tinggi': '🔴',
                        'tidak_diketahui': '⚪'
                    }
                    
                    activity_display = current_activity.replace('_', ' ').title()
                    posture_display = posture_quality.replace('_', ' ').title()
                    cognitive_display = cognitive_load.replace('_', ' ').title()
                    
                    self.activity_text.value = f"Aktivitas: {activity_display} {activity_emoji.get(current_activity, '❓')}"
                    self.posture_text.value = f"Postur: {posture_display} {posture_emoji.get(posture_quality, '⚪')}"
                    self.cognitive_load_text.value = f"Beban Kognitif: {cognitive_display} {cognitive_emoji.get(cognitive_load, '⚪')} (Dampak: {behavior_focus_impact:+.1f})"
                else:
                    # Fallback mode - show behavior-based info
                    self.head_pose_text.value = "Head Pose: Menggunakan Behavior Analysis"
                    self.expression_text.value = "Ekspresi: Estimasi dari Behavior"
                    
                    # Show behavior data if available
                    if behavior_data:
                        activity = behavior_data.get('activity', {})
                        dominant_activity = activity.get('dominant', 'tidak_diketahui')
                        confidence = activity.get('confidence', 0.0)
                        
                        activity_emoji = {
                            'writing': '✍️',
                            'typing': '⌨️',
                            'drinking': '🥤',
                            'smoking': '🚬',
                            'phone_use': '📱',
                            'neutral': '👤',
                            'tidak_diketahui': '❓'
                        }
                        
                        emoji = activity_emoji.get(dominant_activity, '❓')
                        self.activity_text.value = f"Aktivitas: {dominant_activity.title()} {emoji} (Conf: {confidence:.1%})"
                        
                        posture = behavior_data.get('posture', {})
                        posture_quality = posture.get('posture_quality', 'unknown')
                        self.posture_text.value = f"Postur: {posture_quality.title()}"
                        
                        cognitive = behavior_data.get('cognitive_load', {})
                        load_level = cognitive.get('overall_load', 'normal')
                        self.cognitive_load_text.value = f"Beban Kognitif: {load_level.title()}"
                    else:
                        self.activity_text.value = "Aktivitas: Menunggu Data Behavior"
                        self.posture_text.value = "Postur: Menunggu Data Behavior"
                        self.cognitive_load_text.value = "Beban Kognitif: Menunggu Data Behavior"
                
                # FPS
                self.fps_text.value = f"FPS: {fps:.1f}"
                
                # Focus level description dengan method info
                focus_desc = self.focus_calculator.get_focus_level_description(focus_score)
                if fallback_used:
                    focus_desc += " (Fallback Mode)"
                self.focus_indicator.content.value = focus_desc
                
                # Color coding untuk focus indicator
                if focus_score >= 80:
                    self.focus_indicator.bgcolor = ft.colors.GREEN_50
                    self.focus_indicator.border = ft.border.all(2, ft.colors.GREEN_200)
                    self.focus_indicator.content.color = ft.colors.GREEN_700
                elif focus_score >= 60:
                    self.focus_indicator.bgcolor = ft.colors.BLUE_50
                    self.focus_indicator.border = ft.border.all(2, ft.colors.BLUE_200)
                    self.focus_indicator.content.color = ft.colors.BLUE_700
                elif focus_score >= 40:
                    self.focus_indicator.bgcolor = ft.colors.ORANGE_50
                    self.focus_indicator.border = ft.border.all(2, ft.colors.ORANGE_200)
                    self.focus_indicator.content.color = ft.colors.ORANGE_700
                else:
                    self.focus_indicator.bgcolor = ft.colors.RED_50
                    self.focus_indicator.border = ft.border.all(2, ft.colors.RED_200)
                    self.focus_indicator.content.color = ft.colors.RED_700
                
                self.page.update()
            
            update_ui()
            
        except Exception as ex:
            print(f"⚠️ Error update UI metrics: {ex}")
            import traceback
            traceback.print_exc()
    
    def toggle_tts(self, e):
        """Toggle TTS feedback on/off"""
        self.tts_enabled = e.control.value
        status = "diaktifkan" if self.tts_enabled else "dinonaktifkan"
        print(f"🔊 TTS feedback {status}")
        
        # Update status text jika monitoring aktif
        if self.is_running:
            def update_status():
                current_status = self.status_text.value
                if "🔊" not in current_status and self.tts_enabled:
                    self.status_text.value = current_status + " 🔊"
                elif "🔊" in current_status and not self.tts_enabled:
                    self.status_text.value = current_status.replace(" 🔊", "")
                self.page.update()
            
            update_status()
    
    def show_error(self, message: str):
        """Menampilkan pesan error"""
        def show_dialog():
            dialog = ft.AlertDialog(
                title=ft.Text("Error"),
                content=ft.Text(message),
                actions=[
                    ft.TextButton("OK", on_click=lambda e: self.page.close_dialog())
                ]
            )
            self.page.dialog = dialog
            dialog.open = True
            self.page.update()
        
        show_dialog()
    
    def cleanup(self):
        """Cleanup resources saat aplikasi ditutup"""
        if self.is_running:
            self.stop_monitoring(None)


def run_app():
    """
    Fungsi utama untuk menjalankan aplikasi Focus Monitor
    """
    
    def main(page: ft.Page):
        """
        Fungsi utama untuk setup halaman Flet
        
        Args:
            page: Instance halaman Flet
        """
        app = FocusMonitorApp(page)
        
        # Handle page close
        def on_window_event(e):
            if e.data == "close":
                app.cleanup()
        
        page.on_window_event = on_window_event
    
    # Jalankan aplikasi Flet
    ft.app(target=main)