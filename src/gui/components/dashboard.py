"""
Dashboard Component untuk ICikiwir
Komponen dashboard dengan chart realtime dan statistik fokus
Menggunakan Flet untuk UI dan data dari storage logger

(c) 2025 Radhitya Guntoro Adhi
"""

import flet as ft
import asyncio
import time
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import json

from ...storage.logger import logger, FocusMetrics
from ...config import config


class DashboardComponent:
    """
    Komponen Dashboard untuk menampilkan metrik fokus realtime
    """
    
    def __init__(self, page: ft.Page):
        """
        Inisialisasi Dashboard Component
        
        Args:
            page: Instance halaman Flet
        """
        self.page = page
        self.is_active = False
        self.refresh_task: Optional[asyncio.Task] = None
        
        # Data untuk chart
        self.focus_data: List[Dict[str, Any]] = []
        self.blink_data: List[Dict[str, Any]] = []
        
        # UI Components
        self._create_components()
        
    def _create_components(self):
        """Buat komponen UI dashboard"""
        
        # Header dashboard
        self.dashboard_title = ft.Text(
            "📊 Dashboard Fokus Harian",
            size=24,
            weight=ft.FontWeight.BOLD,
            color=ft.colors.BLUE_700
        )
        
        # Statistik cards
        self.stats_cards = self._create_stats_cards()
        
        # Chart containers (placeholder untuk chart)
        self.focus_chart_container = ft.Container(
            content=ft.Column([
                ft.Text(
                    "📈 Skor Fokus (15 Menit Terakhir)",
                    size=16,
                    weight=ft.FontWeight.W_500,
                    color=ft.colors.BLUE_600
                ),
                ft.Container(
                    content=self._create_focus_chart_placeholder(),
                    height=200,
                    bgcolor=ft.colors.GREY_50,
                    border_radius=10,
                    padding=10
                )
            ]),
            padding=10
        )
        
        self.blink_chart_container = ft.Container(
            content=ft.Column([
                ft.Text(
                    "👁️ Blink Rate per Menit",
                    size=16,
                    weight=ft.FontWeight.W_500,
                    color=ft.colors.GREEN_600
                ),
                ft.Container(
                    content=self._create_blink_chart_placeholder(),
                    height=200,
                    bgcolor=ft.colors.GREY_50,
                    border_radius=10,
                    padding=10
                )
            ]),
            padding=10
        )
        
        # Export button
        self.export_button = ft.ElevatedButton(
            "📤 Export CSV",
            icon=ft.icons.DOWNLOAD,
            on_click=self._export_csv,
            color=ft.colors.WHITE,
            bgcolor=ft.colors.BLUE_600
        )
        
        # Refresh info
        self.last_refresh_text = ft.Text(
            "Terakhir diperbarui: --",
            size=12,
            color=ft.colors.GREY_500
        )
        
    def _create_stats_cards(self) -> ft.Row:
        """Buat kartu statistik"""
        
        # Card untuk rata-rata fokus hari ini
        self.avg_focus_card = ft.Container(
            content=ft.Column([
                ft.Text("Rata-rata Fokus", size=14, color=ft.colors.GREY_600),
                ft.Text("--", size=28, weight=ft.FontWeight.BOLD, color=ft.colors.BLUE_700),
                ft.Text("Hari ini", size=12, color=ft.colors.GREY_500)
            ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
            width=150,
            height=100,
            bgcolor=ft.colors.BLUE_50,
            border_radius=10,
            padding=10,
            border=ft.border.all(1, ft.colors.BLUE_200)
        )
        
        # Card untuk total sesi
        self.session_count_card = ft.Container(
            content=ft.Column([
                ft.Text("Total Sesi", size=14, color=ft.colors.GREY_600),
                ft.Text("--", size=28, weight=ft.FontWeight.BOLD, color=ft.colors.GREEN_700),
                ft.Text("Hari ini", size=12, color=ft.colors.GREY_500)
            ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
            width=150,
            height=100,
            bgcolor=ft.colors.GREEN_50,
            border_radius=10,
            padding=10,
            border=ft.border.all(1, ft.colors.GREEN_200)
        )
        
        # Card untuk waktu monitoring
        self.monitoring_time_card = ft.Container(
            content=ft.Column([
                ft.Text("Waktu Monitor", size=14, color=ft.colors.GREY_600),
                ft.Text("--", size=28, weight=ft.FontWeight.BOLD, color=ft.colors.ORANGE_700),
                ft.Text("Menit", size=12, color=ft.colors.GREY_500)
            ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
            width=150,
            height=100,
            bgcolor=ft.colors.ORANGE_50,
            border_radius=10,
            padding=10,
            border=ft.border.all(1, ft.colors.ORANGE_200)
        )
        
        return ft.Row([
            self.avg_focus_card,
            self.session_count_card,
            self.monitoring_time_card
        ], alignment=ft.MainAxisAlignment.SPACE_AROUND)
    
    def _create_focus_chart_placeholder(self) -> ft.Column:
        """Buat placeholder untuk chart fokus"""
        return ft.Column([
            ft.Text(
                "Chart akan menampilkan data fokus realtime",
                size=14,
                color=ft.colors.GREY_600,
                text_align=ft.TextAlign.CENTER
            ),
            ft.Container(
                content=ft.Row([
                    ft.Container(width=20, height=60, bgcolor=ft.colors.BLUE_300, border_radius=2),
                    ft.Container(width=20, height=80, bgcolor=ft.colors.BLUE_400, border_radius=2),
                    ft.Container(width=20, height=40, bgcolor=ft.colors.BLUE_200, border_radius=2),
                    ft.Container(width=20, height=90, bgcolor=ft.colors.BLUE_500, border_radius=2),
                    ft.Container(width=20, height=70, bgcolor=ft.colors.BLUE_400, border_radius=2),
                ], alignment=ft.MainAxisAlignment.CENTER, spacing=5),
                padding=20
            )
        ], horizontal_alignment=ft.CrossAxisAlignment.CENTER)
    
    def _create_blink_chart_placeholder(self) -> ft.Column:
        """Buat placeholder untuk chart blink rate"""
        return ft.Column([
            ft.Text(
                "Chart akan menampilkan blink rate per menit",
                size=14,
                color=ft.colors.GREY_600,
                text_align=ft.TextAlign.CENTER
            ),
            ft.Container(
                content=ft.Row([
                    ft.Container(width=15, height=30, bgcolor=ft.colors.GREEN_300, border_radius=2),
                    ft.Container(width=15, height=45, bgcolor=ft.colors.GREEN_400, border_radius=2),
                    ft.Container(width=15, height=25, bgcolor=ft.colors.GREEN_200, border_radius=2),
                    ft.Container(width=15, height=50, bgcolor=ft.colors.GREEN_500, border_radius=2),
                    ft.Container(width=15, height=35, bgcolor=ft.colors.GREEN_400, border_radius=2),
                    ft.Container(width=15, height=40, bgcolor=ft.colors.GREEN_400, border_radius=2),
                ], alignment=ft.MainAxisAlignment.CENTER, spacing=3),
                padding=20
            )
        ], horizontal_alignment=ft.CrossAxisAlignment.CENTER)
    
    def get_dashboard_content(self) -> ft.Column:
        """
        Dapatkan konten dashboard lengkap
        
        Returns:
            ft.Column: Konten dashboard
        """
        return ft.Column([
            # Header
            ft.Container(
                content=ft.Row([
                    self.dashboard_title,
                    ft.Row([
                        self.export_button,
                        ft.IconButton(
                            icon=ft.icons.REFRESH,
                            tooltip="Refresh Data",
                            on_click=self._manual_refresh
                        )
                    ])
                ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
                padding=ft.padding.only(bottom=20)
            ),
            
            # Stats cards
            self.stats_cards,
            
            ft.Divider(height=20, color=ft.colors.TRANSPARENT),
            
            # Charts
            ft.Row([
                ft.Container(
                    content=self.focus_chart_container,
                    expand=True
                ),
                ft.Container(
                    content=self.blink_chart_container,
                    expand=True
                )
            ]),
            
            ft.Divider(height=10, color=ft.colors.TRANSPARENT),
            
            # Footer info
            ft.Container(
                content=self.last_refresh_text,
                alignment=ft.alignment.center
            )
        ], scroll=ft.ScrollMode.AUTO)
    
    async def start_dashboard(self):
        """Mulai dashboard dengan auto refresh"""
        if self.is_active:
            return
            
        self.is_active = True
        
        # Load data awal
        await self._refresh_data()
        
        # Start auto refresh task
        self.refresh_task = asyncio.create_task(self._auto_refresh_loop())
        
        print("📊 Dashboard dimulai dengan auto refresh")
    
    async def stop_dashboard(self):
        """Hentikan dashboard"""
        if not self.is_active:
            return
            
        self.is_active = False
        
        # Cancel refresh task
        if self.refresh_task:
            self.refresh_task.cancel()
            try:
                await self.refresh_task
            except asyncio.CancelledError:
                pass
        
        print("📊 Dashboard dihentikan")
    
    async def _auto_refresh_loop(self):
        """Loop auto refresh dashboard"""
        while self.is_active:
            try:
                await asyncio.sleep(config.gui.dashboard_refresh_sec)
                await self._refresh_data()
            except asyncio.CancelledError:
                break
            except Exception as e:
                print(f"❌ Error dalam auto refresh dashboard: {e}")
    
    async def _refresh_data(self):
        """Refresh data dashboard dari storage"""
        try:
            # Ambil data 15 menit terakhir untuk chart
            end_time = time.time()
            start_time = end_time - (config.gui.chart_history_minutes * 60)
            
            metrics_list = await logger.query_metrics(
                start_time=start_time,
                end_time=end_time,
                limit=100
            )
            
            # Update data untuk chart
            self._update_chart_data(metrics_list)
            
            # Update statistik harian
            await self._update_daily_stats()
            
            # Update UI
            self._update_ui()
            
        except Exception as e:
            print(f"❌ Error refresh data dashboard: {e}")
    
    def _update_chart_data(self, metrics_list: List[FocusMetrics]):
        """Update data untuk chart"""
        self.focus_data.clear()
        self.blink_data.clear()
        
        for metrics in reversed(metrics_list):  # Reverse untuk urutan chronological
            timestamp = datetime.fromtimestamp(metrics.timestamp)
            
            self.focus_data.append({
                'time': timestamp.strftime('%H:%M'),
                'value': metrics.focus_score,
                'timestamp': metrics.timestamp
            })
            
            self.blink_data.append({
                'time': timestamp.strftime('%H:%M'),
                'value': metrics.blink_rate,
                'timestamp': metrics.timestamp
            })
        
        # Batasi data untuk performa
        if len(self.focus_data) > 50:
            self.focus_data = self.focus_data[-50:]
        if len(self.blink_data) > 50:
            self.blink_data = self.blink_data[-50:]
    
    async def _update_daily_stats(self):
        """Update statistik harian"""
        try:
            # Ambil data hari ini
            today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            today_start_timestamp = today_start.timestamp()
            
            daily_metrics = await logger.query_metrics(
                start_time=today_start_timestamp,
                end_time=time.time()
            )
            
            if daily_metrics:
                # Hitung rata-rata fokus
                avg_focus = sum(m.focus_score for m in daily_metrics) / len(daily_metrics)
                
                # Hitung jumlah sesi unik
                sessions = set(m.session_id for m in daily_metrics if m.session_id)
                session_count = len(sessions)
                
                # Hitung total waktu monitoring (estimasi)
                if len(daily_metrics) > 1:
                    time_span = daily_metrics[0].timestamp - daily_metrics[-1].timestamp
                    monitoring_minutes = int(time_span / 60)
                else:
                    monitoring_minutes = 0
                
                # Update card values
                self.avg_focus_value = f"{avg_focus:.1f}"
                self.session_count_value = str(session_count)
                self.monitoring_time_value = str(monitoring_minutes)
            else:
                self.avg_focus_value = "0.0"
                self.session_count_value = "0"
                self.monitoring_time_value = "0"
                
        except Exception as e:
            print(f"❌ Error update daily stats: {e}")
            self.avg_focus_value = "--"
            self.session_count_value = "--"
            self.monitoring_time_value = "--"
    
    def _update_ui(self):
        """Update UI dengan data terbaru"""
        try:
            # Update stats cards
            self.avg_focus_card.content.controls[1].value = self.avg_focus_value
            self.session_count_card.content.controls[1].value = self.session_count_value
            self.monitoring_time_card.content.controls[1].value = self.monitoring_time_value
            
            # Update last refresh time
            self.last_refresh_text.value = f"Terakhir diperbarui: {datetime.now().strftime('%H:%M:%S')}"
            
            # Update chart placeholders dengan data terbaru
            if self.focus_data:
                latest_focus = self.focus_data[-1]['value']
                focus_color = self._get_focus_color(latest_focus)
                
                # Update focus chart placeholder dengan warna berdasarkan skor terbaru
                self.focus_chart_container.content.controls[1].bgcolor = focus_color
            
            # Trigger page update
            def update_page():
                self.page.update()
            
            update_page()
            
        except Exception as e:
            print(f"❌ Error update UI: {e}")
    
    def _get_focus_color(self, focus_score: float) -> str:
        """Dapatkan warna berdasarkan skor fokus"""
        if focus_score >= 80:
            return ft.colors.GREEN_50
        elif focus_score >= 60:
            return ft.colors.BLUE_50
        elif focus_score >= 40:
            return ft.colors.ORANGE_50
        else:
            return ft.colors.RED_50
    
    async def _manual_refresh(self, e):
        """Handler untuk refresh manual"""
        await self._refresh_data()
        print("🔄 Dashboard di-refresh manual")
    
    async def _export_csv(self, e):
        """Handler untuk export CSV"""
        try:
            # Generate filename dengan timestamp
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"focus_metrics_{timestamp}.csv"
            
            # Export data hari ini
            today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            today_start_timestamp = today_start.timestamp()
            
            from pathlib import Path
            output_path = Path.home() / "Downloads" / filename
            
            count = await logger.export_to_csv(
                output_path=output_path,
                start_time=today_start_timestamp,
                end_time=time.time()
            )
            
            # Show success dialog
            def show_success():
                dialog = ft.AlertDialog(
                    title=ft.Text("Export Berhasil"),
                    content=ft.Text(f"Data berhasil diekspor ke:\n{output_path}\n\nTotal record: {count}"),
                    actions=[
                        ft.TextButton("OK", on_click=lambda e: self.page.close_dialog())
                    ]
                )
                self.page.dialog = dialog
                dialog.open = True
                self.page.update()
            
            show_success()
            
        except Exception as ex:
            # Show error dialog
            def show_error():
                dialog = ft.AlertDialog(
                    title=ft.Text("Export Gagal"),
                    content=ft.Text(f"Gagal mengekspor data:\n{str(ex)}"),
                    actions=[
                        ft.TextButton("OK", on_click=lambda e: self.page.close_dialog())
                    ]
                )
                self.page.dialog = dialog
                dialog.open = True
                self.page.update()
            
            show_error()