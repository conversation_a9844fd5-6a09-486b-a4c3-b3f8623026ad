"""
Komponen Markdown Viewer untuk menampilkan laporan ICikiwir
Menggunakan Flet untuk rendering markdown dengan dukungan gambar base64

(c) 2025 Radhi<PERSON><PERSON>
"""

import flet as ft
from pathlib import Path
from typing import Optional
import re


class ReportViewer(ft.Column):
    """
    Komponen untuk menampilkan laporan markdown
    Extends flet.Column untuk layout vertikal
    """
    
    def __init__(self, page: ft.Page):
        """
        Inisialisasi ReportViewer
        
        Args:
            page: Instance halaman Flet
        """
        super().__init__()
        self.page = page
        self.current_report_path: Optional[Path] = None
        
        # UI Components
        self.markdown_content = ft.Markdown(
            value="Pilih laporan untuk ditampilkan...",
            expand=True
        )
        
        # Scroll container untuk markdown
        self.scroll_container = ft.Container(
            content=self.markdown_content,
            expand=True,
            padding=20,
            bgcolor=ft.colors.WHITE,
            border_radius=10,
            border=ft.border.all(1, ft.colors.GREY_300)
        )
        
        # Header dengan info laporan
        self.report_header = ft.Container(
            content=ft.Row([
                ft.Icon(ft.icons.DESCRIPTION, color=ft.colors.BLUE_600),
                ft.Text(
                    "Laporan ICikiwir",
                    size=18,
                    weight=ft.FontWeight.W_500,
                    color=ft.colors.BLUE_700
                ),
                ft.Container(expand=True),  # Spacer
                ft.TextButton(
                    "Refresh",
                    icon=ft.icons.REFRESH,
                    on_click=self.refresh_report
                )
            ]),
            padding=ft.padding.symmetric(horizontal=20, vertical=10),
            bgcolor=ft.colors.BLUE_50,
            border_radius=ft.border_radius.only(top_left=10, top_right=10)
        )
        
        # Build layout
        self.controls = [
            self.report_header,
            self.scroll_container
        ]
        
        self.expand = True
        self.spacing = 0
    
    def load_report(self, report_path: Path) -> bool:
        """
        Memuat dan menampilkan laporan markdown
        
        Args:
            report_path: Path ke file laporan .md
            
        Returns:
            bool: True jika berhasil, False jika gagal
        """
        try:
            if not report_path.exists():
                self.show_error(f"File laporan tidak ditemukan: {report_path}")
                return False
            
            if not report_path.suffix.lower() == '.md':
                self.show_error("File harus berformat .md (Markdown)")
                return False
            
            # Baca konten file
            with open(report_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Proses konten untuk Flet Markdown
            processed_content = self.process_markdown_content(content)
            
            # Update markdown viewer
            self.markdown_content.value = processed_content
            self.current_report_path = report_path
            
            # Update header
            self.update_header(report_path)
            
            # Update UI
            self.page.update()
            
            print(f"✅ Laporan berhasil dimuat: {report_path.name}")
            return True
            
        except Exception as e:
            self.show_error(f"Error memuat laporan: {e}")
            return False
    
    def process_markdown_content(self, content: str) -> str:
        """
        Memproses konten markdown untuk kompatibilitas dengan Flet
        
        Args:
            content: Konten markdown mentah
            
        Returns:
            str: Konten markdown yang sudah diproses
        """
        # Flet Markdown belum sepenuhnya mendukung base64 images
        # Untuk sementara, kita ganti dengan placeholder
        
        # Pattern untuk mendeteksi base64 images
        base64_pattern = r'!\[([^\]]*)\]\(data:image/[^;]+;base64,[^)]+\)'
        
        # Ganti dengan placeholder
        def replace_base64_image(match):
            alt_text = match.group(1) or "Grafik"
            return f"📊 **{alt_text}** (Grafik tersedia di file laporan)"
        
        processed_content = re.sub(base64_pattern, replace_base64_image, content)
        
        # Tambahkan informasi tambahan di akhir
        processed_content += "\n\n---\n\n"
        processed_content += "💡 **Catatan:** Grafik dan chart dapat dilihat dengan membuka file laporan menggunakan aplikasi markdown viewer yang mendukung base64 images, seperti Typora atau VS Code.\n\n"
        processed_content += f"📁 **Lokasi File:** `{self.current_report_path}`" if self.current_report_path else ""
        
        return processed_content
    
    def update_header(self, report_path: Path):
        """
        Update header dengan informasi laporan
        
        Args:
            report_path: Path ke file laporan
        """
        # Extract tanggal dari nama file (format: YYYY-MM-DD.md)
        date_str = report_path.stem
        try:
            from datetime import datetime
            date_obj = datetime.strptime(date_str, '%Y-%m-%d')
            formatted_date = date_obj.strftime('%d %B %Y')
        except:
            formatted_date = date_str
        
        # Update header text
        header_row = self.report_header.content
        header_row.controls[1].value = f"Laporan Harian - {formatted_date}"
    
    def refresh_report(self, e):
        """
        Refresh laporan yang sedang ditampilkan
        
        Args:
            e: Event dari button click
        """
        if self.current_report_path:
            self.load_report(self.current_report_path)
        else:
            self.show_info("Tidak ada laporan yang dimuat")
    
    def show_error(self, message: str):
        """
        Menampilkan pesan error
        
        Args:
            message: Pesan error
        """
        self.markdown_content.value = f"""# ❌ Error

{message}

---

Pastikan file laporan tersedia dan dapat dibaca.
"""
        self.page.update()
        print(f"❌ Error: {message}")
    
    def show_info(self, message: str):
        """
        Menampilkan pesan informasi
        
        Args:
            message: Pesan informasi
        """
        self.markdown_content.value = f"""# ℹ️ Informasi

{message}

---

Pilih laporan dari daftar untuk menampilkan konten.
"""
        self.page.update()
    
    def clear_content(self):
        """
        Bersihkan konten viewer
        """
        self.markdown_content.value = "Pilih laporan untuk ditampilkan..."
        self.current_report_path = None
        
        # Reset header
        header_row = self.report_header.content
        header_row.controls[1].value = "Laporan ICikiwir"
        
        self.page.update()


class ReportListViewer(ft.Column):
    """
    Komponen untuk menampilkan daftar laporan dan viewer
    """
    
    def __init__(self, page: ft.Page, report_dir: Path):
        """
        Inisialisasi ReportListViewer
        
        Args:
            page: Instance halaman Flet
            report_dir: Direktori yang berisi laporan
        """
        super().__init__()
        self.page = page
        self.report_dir = report_dir
        
        # Komponen viewer
        self.report_viewer = ReportViewer(page)
        
        # List untuk menampilkan file laporan
        self.report_list = ft.ListView(
            expand=True,
            spacing=5,
            padding=10
        )
        
        # Container untuk list
        self.list_container = ft.Container(
            content=ft.Column([
                ft.Container(
                    content=ft.Row([
                        ft.Icon(ft.icons.LIST, color=ft.colors.GREEN_600),
                        ft.Text(
                            "Daftar Laporan",
                            size=16,
                            weight=ft.FontWeight.W_500,
                            color=ft.colors.GREEN_700
                        ),
                        ft.Container(expand=True),
                        ft.IconButton(
                            icon=ft.icons.REFRESH,
                            tooltip="Refresh daftar",
                            on_click=self.refresh_list
                        )
                    ]),
                    padding=10,
                    bgcolor=ft.colors.GREEN_50,
                    border_radius=ft.border_radius.only(top_left=10, top_right=10)
                ),
                ft.Container(
                    content=self.report_list,
                    expand=True,
                    padding=5,
                    bgcolor=ft.colors.WHITE,
                    border_radius=ft.border_radius.only(bottom_left=10, bottom_right=10),
                    border=ft.border.all(1, ft.colors.GREY_300)
                )
            ], spacing=0),
            width=300,
            expand=True
        )
        
        # Layout utama
        self.controls = [
            ft.Row([
                self.list_container,
                ft.VerticalDivider(width=1),
                ft.Container(
                    content=self.report_viewer,
                    expand=True
                )
            ], expand=True, spacing=10)
        ]
        
        self.expand = True
        
        # Load daftar laporan
        self.refresh_list(None)
    
    def refresh_list(self, e):
        """
        Refresh daftar laporan
        
        Args:
            e: Event dari button click
        """
        try:
            # Pastikan direktori ada
            if not self.report_dir.exists():
                self.report_dir.mkdir(parents=True, exist_ok=True)
            
            # Cari file .md di direktori
            report_files = list(self.report_dir.glob("*.md"))
            report_files.sort(reverse=True)  # Urutkan terbaru dulu
            
            # Clear list
            self.report_list.controls.clear()
            
            if not report_files:
                # Tidak ada laporan
                self.report_list.controls.append(
                    ft.Container(
                        content=ft.Text(
                            "Belum ada laporan tersedia",
                            color=ft.colors.GREY_600,
                            italic=True
                        ),
                        padding=20,
                        alignment=ft.alignment.center
                    )
                )
            else:
                # Tambahkan setiap file ke list
                for report_file in report_files:
                    self.add_report_item(report_file)
            
            self.page.update()
            print(f"✅ Daftar laporan diperbarui: {len(report_files)} file")
            
        except Exception as ex:
            print(f"❌ Error refresh list: {ex}")
    
    def add_report_item(self, report_file: Path):
        """
        Tambahkan item laporan ke list
        
        Args:
            report_file: Path ke file laporan
        """
        # Extract info dari nama file
        date_str = report_file.stem
        try:
            from datetime import datetime
            date_obj = datetime.strptime(date_str, '%Y-%m-%d')
            formatted_date = date_obj.strftime('%d %B %Y')
            day_name = date_obj.strftime('%A')
        except:
            formatted_date = date_str
            day_name = ""
        
        # File size
        try:
            file_size = report_file.stat().st_size
            size_str = f"{file_size / 1024:.1f} KB" if file_size > 1024 else f"{file_size} B"
        except:
            size_str = "N/A"
        
        # Create list item
        list_item = ft.Container(
            content=ft.Column([
                ft.Row([
                    ft.Icon(ft.icons.DESCRIPTION, size=20, color=ft.colors.BLUE_600),
                    ft.Column([
                        ft.Text(
                            formatted_date,
                            size=14,
                            weight=ft.FontWeight.W_500,
                            color=ft.colors.BLACK87
                        ),
                        ft.Text(
                            f"{day_name} • {size_str}",
                            size=12,
                            color=ft.colors.GREY_600
                        )
                    ], spacing=2, expand=True),
                    ft.IconButton(
                        icon=ft.icons.OPEN_IN_NEW,
                        icon_size=16,
                        tooltip="Buka laporan",
                        on_click=lambda e, path=report_file: self.open_report(path)
                    )
                ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN)
            ], spacing=5),
            padding=10,
            margin=2,
            bgcolor=ft.colors.GREY_50,
            border_radius=8,
            border=ft.border.all(1, ft.colors.GREY_200),
            ink=True,
            on_click=lambda e, path=report_file: self.open_report(path)
        )
        
        self.report_list.controls.append(list_item)
    
    def open_report(self, report_path: Path):
        """
        Buka laporan di viewer
        
        Args:
            report_path: Path ke file laporan
        """
        success = self.report_viewer.load_report(report_path)
        if success:
            print(f"📖 Membuka laporan: {report_path.name}")