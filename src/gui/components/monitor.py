"""
Monitor Component - Komponen untuk monitoring real-time focus
Menampilkan webcam stream, metrics, dan kontrol monitoring

(c) 2025 Radhi<PERSON>a Guntoro Adhi
"""

import flet as ft
import threading
import time
import base64
import io
from PIL import Image
import numpy as np
from typing import Optional

from src.capture.stream import WebcamStream
from src.vision.face_tracker import FaceTracker
from src.analytics.focus_calculator import FocusCalculator
from src.intervention.rules import evaluate_focus
from src.intervention.tts_engine import tts_engine


class MonitorComponent(ft.Column):
    """
    Komponen monitoring real-time untuk ICikiwir
    Menampilkan video stream, metrics, dan kontrol
    """
    
    def __init__(self):
        super().__init__()
        
        # State monitoring
        self.is_monitoring = False
        self.webcam_stream: Optional[WebcamStream] = None
        self.face_tracker: Optional[FaceTracker] = None
        self.focus_calculator: Optional[FocusCalculator] = None
        
        # UI Components
        self.video_image = ft.Image(
            width=640,
            height=480,
            fit=ft.ImageFit.CONTAIN,
            border_radius=10,
        )
        
        self.start_button = ft.ElevatedButton(
            "Mulai Monitoring",
            icon=ft.icons.PLAY_ARROW,
            on_click=self.start_monitoring,
            color=ft.colors.WHITE,
            bgcolor=ft.colors.GREEN,
        )
        
        self.stop_button = ft.ElevatedButton(
            "Hentikan Monitoring",
            icon=ft.icons.STOP,
            on_click=self.stop_monitoring,
            color=ft.colors.WHITE,
            bgcolor=ft.colors.RED,
            disabled=True,
        )
        
        # Metrics display
        self.focus_score_text = ft.Text("Skor Fokus: --", size=20, weight=ft.FontWeight.BOLD)
        self.blink_rate_text = ft.Text("Tingkat Kedip: --", size=16)
        self.head_pose_text = ft.Text("Posisi Kepala: --", size=16)
        self.fps_text = ft.Text("FPS: --", size=14, color=ft.colors.GREY)
        
        # Status indicator
        self.status_text = ft.Text(
            "Status: Siap",
            size=16,
            color=ft.colors.BLUE,
            weight=ft.FontWeight.BOLD
        )
        
        # Build layout
        self._build_layout()
        
    def _build_layout(self):
        """Membangun layout komponen"""
        
        # Video container
        video_container = ft.Container(
            content=self.video_image,
            bgcolor=ft.colors.BLACK12,
            border_radius=10,
            padding=10,
            alignment=ft.alignment.center,
        )
        
        # Control buttons
        control_row = ft.Row([
            self.start_button,
            self.stop_button,
        ], alignment=ft.MainAxisAlignment.CENTER)
        
        # Metrics panel
        metrics_panel = ft.Container(
            content=ft.Column([
                ft.Text("📊 Metrics Real-time", size=18, weight=ft.FontWeight.BOLD),
                ft.Divider(),
                self.focus_score_text,
                self.blink_rate_text,
                self.head_pose_text,
                ft.Divider(),
                self.fps_text,
                self.status_text,
            ], spacing=10),
            bgcolor=ft.colors.SURFACE_VARIANT,
            border_radius=10,
            padding=20,
            width=300,
        )
        
        # Main layout
        main_row = ft.Row([
            ft.Column([
                video_container,
                control_row,
            ], spacing=20, expand=True),
            metrics_panel,
        ], spacing=20, alignment=ft.MainAxisAlignment.START)
        
        self.controls = [
            ft.Text("🎥 Monitor Fokus Real-time", size=24, weight=ft.FontWeight.BOLD),
            ft.Divider(),
            main_row,
        ]
        
    def start_monitoring(self, e):
        """Memulai monitoring"""
        try:
            self.status_text.value = "Status: Memulai..."
            self.status_text.color = ft.colors.ORANGE
            self.page.update()
            
            # Initialize components
            self.webcam_stream = WebcamStream()
            self.face_tracker = FaceTracker()
            self.focus_calculator = FocusCalculator()
            
            # Start webcam
            if not self.webcam_stream.start():
                raise Exception("Gagal memulai webcam")
            
            # Update UI state
            self.is_monitoring = True
            self.start_button.disabled = True
            self.stop_button.disabled = False
            self.status_text.value = "Status: Monitoring Aktif"
            self.status_text.color = ft.colors.GREEN
            
            # Start monitoring thread
            self.monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
            self.monitoring_thread.start()
            
            self.page.update()
            
        except Exception as ex:
            self.status_text.value = f"Error: {str(ex)}"
            self.status_text.color = ft.colors.RED
            self.page.update()
            
    def stop_monitoring(self, e):
        """Menghentikan monitoring"""
        try:
            self.is_monitoring = False
            
            # Stop webcam
            if self.webcam_stream:
                self.webcam_stream.stop()
                self.webcam_stream = None
            
            # Update UI state
            self.start_button.disabled = False
            self.stop_button.disabled = True
            self.status_text.value = "Status: Dihentikan"
            self.status_text.color = ft.colors.BLUE
            
            # Reset video display
            self.video_image.src = None
            self.video_image.src_base64 = None
            
            # Reset metrics
            self.focus_score_text.value = "Skor Fokus: --"
            self.blink_rate_text.value = "Tingkat Kedip: --"
            self.head_pose_text.value = "Posisi Kepala: --"
            self.fps_text.value = "FPS: --"
            
            self.page.update()
            
        except Exception as ex:
            self.status_text.value = f"Error: {str(ex)}"
            self.status_text.color = ft.colors.RED
            self.page.update()
            
    def _monitoring_loop(self):
        """Loop monitoring utama (dijalankan di thread terpisah)"""
        try:
            while self.is_monitoring:
                if not self.webcam_stream or not self.webcam_stream.is_active():
                    break
                    
                # Get frame from webcam
                frame = self.webcam_stream.get_frame()
                if frame is None:
                    time.sleep(0.033)  # ~30 FPS
                    continue
                
                # Process frame with face tracker
                results = self.face_tracker.process_frame(frame)
                
                # Calculate focus score
                focus_score = self.focus_calculator.calculate_focus_score(results)
                
                # Update video display
                self._update_video_display(frame)
                
                # Update metrics
                self._update_metrics(results, focus_score)
                
                # Evaluate focus and trigger intervention if needed
                feedback = evaluate_focus(focus_score)
                if feedback and tts_engine.is_available():
                    tts_engine.speak(feedback)
                
                # Control frame rate
                time.sleep(0.033)  # ~30 FPS
                
        except Exception as ex:
            self.status_text.value = f"Error monitoring: {str(ex)}"
            self.status_text.color = ft.colors.RED
            self.page.update()
            
    def _update_video_display(self, frame):
        """Update tampilan video dengan frame terbaru"""
        try:
            # Convert frame to PIL Image
            frame_rgb = frame[:, :, ::-1]  # BGR to RGB
            pil_image = Image.fromarray(frame_rgb)
            
            # Resize for display
            pil_image = pil_image.resize((640, 480), Image.Resampling.LANCZOS)
            
            # Convert to base64
            buffer = io.BytesIO()
            pil_image.save(buffer, format='JPEG', quality=85)
            img_base64 = base64.b64encode(buffer.getvalue()).decode()
            
            # Update UI (thread-safe)
            self.video_image.src_base64 = img_base64
            self.page.update()
            
        except Exception as ex:
            print(f"Error updating video display: {ex}")
            
    def _update_metrics(self, results, focus_score):
        """Update tampilan metrics"""
        try:
            # Update focus score
            self.focus_score_text.value = f"Skor Fokus: {focus_score:.1f}%"
            
            # Update blink rate
            if 'blink_rate' in results:
                self.blink_rate_text.value = f"Tingkat Kedip: {results['blink_rate']:.1f}/min"
            
            # Update head pose
            if 'head_pose' in results:
                pose = results['head_pose']
                yaw = pose.get('yaw', 0)
                pitch = pose.get('pitch', 0)
                self.head_pose_text.value = f"Posisi Kepala: Yaw={yaw:.1f}°, Pitch={pitch:.1f}°"
            
            # Update FPS
            if self.webcam_stream:
                fps = self.webcam_stream.get_fps()
                self.fps_text.value = f"FPS: {fps:.1f}"
            
            # Update UI
            self.page.update()
            
        except Exception as ex:
            print(f"Error updating metrics: {ex}")