"""
ICikiwir - Cognitive-Aware Study Companion
Sistem monitoring fokus dan kesehatan mental saat belajar

(c) 2025 Radhitya Guntoro Adhi
"""

import logging
from icikiwir.core import registry, event_bus

__version__ = "0.1.0"
__author__ = "Radhitya Guntoro Adhi"

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def initialize_icikiwir():
    """
    Inisialisasi sistem ICikiwir
    - Discover dan register plugins
    - Start event bus
    """
    try:
        # Discover plugins dari entry points
        plugin_count = registry.discover()
        logger.info(f"ICikiwir berhasil dimuat dengan {plugin_count} plugin")
        
        # Start event bus (akan dijalankan saat aplikasi dimulai)
        logger.info("ICikiwir core berhasil diinisialisasi")
        
    except Exception as e:
        logger.error(f"Error inisialisasi ICikiwir: {e}")
        raise

# Auto-initialize saat import
initialize_icikiwir()