"""
Generator untuk membuat digest paper dalam format Markdown
Mengkonversi metadata paper menjadi format yang mudah dibaca

(c) 2025 Radhitya Guntoro Adhi
"""

import re
from typing import Dict, Any, Optional, List
from datetime import datetime
from pathlib import Path
import yaml

from .mcp_client import PaperMetadata

class DigestGenerator:
    """
    Generator untuk membuat digest paper dalam format Markdown
    Mendukung template bahasa Indonesia dan Inggris
    """
    
    def __init__(self):
        self._config = {
            "language": "id",  # id atau en
            "output_dir": "references",
            "template_style": "academic",  # academic, simple, detailed
            "include_metadata": True,
            "include_summary": True,
            "include_keywords": True
        }
        
        # Template bahasa Indonesia
        self._templates_id = {
            "header": "# {judul}\n\n",
            "metadata_section": "## 📋 Metadata\n\n",
            "metadata_fields": {
                "penulis": "**Penulis:** {penulis}\n",
                "tahun": "**Tahun:** {tahun}\n", 
                "venue": "**Venue:** {venue}\n",
                "doi": "**DOI:** {doi}\n",
                "url": "**URL:** [{url}]({url})\n",
                "citations": "**Sitasi:** {citations}\n"
            },
            "abstract_section": "## 📝 Abstrak\n\n{abstrak}\n\n",
            "keywords_section": "## 🏷️ Kata Kunci\n\n{keywords}\n\n",
            "summary_section": "## 💡 Ringkasan\n\n{summary}\n\n",
            "notes_section": "## 📓 Catatan\n\n*Tambahkan catatan pribadi di sini...*\n\n",
            "footer": "---\n\n*Digest dibuat otomatis oleh ICikiwir pada {timestamp}*\n"
        }
        
        # Template bahasa Inggris
        self._templates_en = {
            "header": "# {judul}\n\n",
            "metadata_section": "## 📋 Metadata\n\n",
            "metadata_fields": {
                "penulis": "**Authors: <AUTHORS>
                "tahun": "**Year:** {tahun}\n",
                "venue": "**Venue:** {venue}\n", 
                "doi": "**DOI:** {doi}\n",
                "url": "**URL:** [{url}]({url})\n",
                "citations": "**Citations:** {citations}\n"
            },
            "abstract_section": "## 📝 Abstract\n\n{abstrak}\n\n",
            "keywords_section": "## 🏷️ Keywords\n\n{keywords}\n\n",
            "summary_section": "## 💡 Summary\n\n{summary}\n\n",
            "notes_section": "## 📓 Notes\n\n*Add your personal notes here...*\n\n",
            "footer": "---\n\n*Digest automatically generated by ICikiwir on {timestamp}*\n"
        }
    
    def configure(self, config: Dict[str, Any]) -> None:
        """
        Konfigurasi generator
        
        Args:
            config: Dictionary konfigurasi
        """
        self._config.update(config)
    
    def generate_digest(self, paper: PaperMetadata) -> str:
        """
        Generate digest markdown dari metadata paper
        
        Args:
            paper: Metadata paper
            
        Returns:
            String konten markdown
        """
        # Pilih template berdasarkan bahasa
        templates = self._templates_id if self._config["language"] == "id" else self._templates_en
        
        # Mulai dengan YAML front matter
        content = self._generate_frontmatter(paper)
        content += "\n"
        
        # Header dengan judul
        content += templates["header"].format(judul=paper.judul)
        
        # Metadata section
        if self._config["include_metadata"]:
            content += self._generate_metadata_section(paper, templates)
        
        # Abstract section
        content += self._generate_abstract_section(paper, templates)
        
        # Keywords section
        if self._config["include_keywords"] and paper.keywords:
            content += self._generate_keywords_section(paper, templates)
        
        # Summary section (auto-generated)
        if self._config["include_summary"]:
            content += self._generate_summary_section(paper, templates)
        
        # Notes section
        content += templates["notes_section"]
        
        # Footer
        content += templates["footer"].format(
            timestamp=datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        )
        
        return content
    
    def _generate_frontmatter(self, paper: PaperMetadata) -> str:
        """
        Generate YAML front matter untuk metadata
        
        Args:
            paper: Metadata paper
            
        Returns:
            String YAML front matter
        """
        frontmatter = {
            "title": paper.judul,
            "authors": paper.penulis,
            "year": paper.tahun,
            "url": paper.url,
            "type": "paper_digest",
            "created": datetime.now().strftime("%Y-%m-%d"),
            "language": self._config["language"]
        }
        
        # Tambah field optional jika ada
        if paper.doi:
            frontmatter["doi"] = paper.doi
        if paper.venue:
            frontmatter["venue"] = paper.venue
        if paper.citations:
            frontmatter["citations"] = paper.citations
        if paper.keywords:
            frontmatter["keywords"] = paper.keywords
        
        return "---\n" + yaml.dump(frontmatter, default_flow_style=False, allow_unicode=True) + "---"
    
    def _generate_metadata_section(self, paper: PaperMetadata, templates: Dict[str, Any]) -> str:
        """
        Generate section metadata
        
        Args:
            paper: Metadata paper
            templates: Template yang digunakan
            
        Returns:
            String section metadata
        """
        content = templates["metadata_section"]
        
        # Penulis
        if paper.penulis:
            penulis_str = ", ".join(paper.penulis)
            content += templates["metadata_fields"]["penulis"].format(penulis=penulis_str)
        
        # Tahun
        content += templates["metadata_fields"]["tahun"].format(tahun=paper.tahun)
        
        # Venue jika ada
        if paper.venue:
            content += templates["metadata_fields"]["venue"].format(venue=paper.venue)
        
        # DOI jika ada
        if paper.doi:
            content += templates["metadata_fields"]["doi"].format(doi=paper.doi)
        
        # URL
        content += templates["metadata_fields"]["url"].format(url=paper.url)
        
        # Citations jika ada
        if paper.citations:
            content += templates["metadata_fields"]["citations"].format(citations=paper.citations)
        
        content += "\n"
        return content
    
    def _generate_abstract_section(self, paper: PaperMetadata, templates: Dict[str, Any]) -> str:
        """
        Generate section abstrak
        
        Args:
            paper: Metadata paper
            templates: Template yang digunakan
            
        Returns:
            String section abstrak
        """
        # Clean up abstract text
        abstract = self._clean_text(paper.abstrak)
        return templates["abstract_section"].format(abstrak=abstract)
    
    def _generate_keywords_section(self, paper: PaperMetadata, templates: Dict[str, Any]) -> str:
        """
        Generate section kata kunci
        
        Args:
            paper: Metadata paper
            templates: Template yang digunakan
            
        Returns:
            String section keywords
        """
        if not paper.keywords:
            return ""
        
        # Format keywords sebagai list atau tags
        if isinstance(paper.keywords, list):
            keywords_str = ", ".join([f"`{kw}`" for kw in paper.keywords])
        else:
            keywords_str = f"`{paper.keywords}`"
        
        return templates["keywords_section"].format(keywords=keywords_str)
    
    def _generate_summary_section(self, paper: PaperMetadata, templates: Dict[str, Any]) -> str:
        """
        Generate section ringkasan otomatis
        
        Args:
            paper: Metadata paper
            templates: Template yang digunakan
            
        Returns:
            String section summary
        """
        # Generate summary sederhana dari abstrak
        summary = self._generate_auto_summary(paper)
        return templates["summary_section"].format(summary=summary)
    
    def _generate_auto_summary(self, paper: PaperMetadata) -> str:
        """
        Generate ringkasan otomatis dari metadata paper
        
        Args:
            paper: Metadata paper
            
        Returns:
            String ringkasan
        """
        if self._config["language"] == "id":
            summary_template = """Paper ini ditulis oleh {authors} pada tahun {year}. 

**Poin Utama:**
- {main_points}

**Relevansi:**
- {relevance}

**Metodologi:**
- {methodology}"""
        else:
            summary_template = """This paper was written by {authors} in {year}.

**Key Points:**
- {main_points}

**Relevance:**
- {relevance}

**Methodology:**
- {methodology}"""
        
        # Extract key information
        authors = ", ".join(paper.penulis[:3])  # Maksimal 3 penulis pertama
        if len(paper.penulis) > 3:
            authors += " et al."
        
        # Extract main points dari abstrak
        main_points = self._extract_main_points(paper.abstrak)
        relevance = self._extract_relevance(paper.abstrak)
        methodology = self._extract_methodology(paper.abstrak)
        
        return summary_template.format(
            authors=authors,
            year=paper.tahun,
            main_points=main_points,
            relevance=relevance,
            methodology=methodology
        )
    
    def _extract_main_points(self, abstract: str) -> str:
        """Extract poin utama dari abstrak"""
        # Sederhana: ambil kalimat pertama dan terakhir
        sentences = self._split_sentences(abstract)
        if len(sentences) >= 2:
            return f"{sentences[0]}\n- {sentences[-1]}"
        elif sentences:
            return sentences[0]
        else:
            return "Tidak dapat mengekstrak poin utama dari abstrak"
    
    def _extract_relevance(self, abstract: str) -> str:
        """Extract relevansi dari abstrak"""
        # Cari kata kunci yang menunjukkan relevansi
        relevance_keywords = [
            "important", "significant", "novel", "innovative", "breakthrough",
            "penting", "signifikan", "baru", "inovatif", "terobosan"
        ]
        
        sentences = self._split_sentences(abstract)
        relevant_sentences = []
        
        for sentence in sentences:
            if any(keyword in sentence.lower() for keyword in relevance_keywords):
                relevant_sentences.append(sentence)
        
        if relevant_sentences:
            return relevant_sentences[0]
        else:
            return "Relevansi akan dianalisis lebih lanjut"
    
    def _extract_methodology(self, abstract: str) -> str:
        """Extract metodologi dari abstrak"""
        # Cari kata kunci metodologi
        method_keywords = [
            "method", "approach", "algorithm", "technique", "framework",
            "metode", "pendekatan", "algoritma", "teknik", "kerangka"
        ]
        
        sentences = self._split_sentences(abstract)
        method_sentences = []
        
        for sentence in sentences:
            if any(keyword in sentence.lower() for keyword in method_keywords):
                method_sentences.append(sentence)
        
        if method_sentences:
            return method_sentences[0]
        else:
            return "Metodologi akan dianalisis dari paper lengkap"
    
    def _split_sentences(self, text: str) -> List[str]:
        """Split teks menjadi kalimat"""
        # Sederhana: split berdasarkan titik
        sentences = re.split(r'[.!?]+', text)
        return [s.strip() for s in sentences if s.strip()]
    
    def _clean_text(self, text: str) -> str:
        """
        Bersihkan teks dari karakter yang tidak diinginkan
        
        Args:
            text: Teks yang akan dibersihkan
            
        Returns:
            Teks yang sudah dibersihkan
        """
        if not text:
            return "Abstrak tidak tersedia"
        
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove HTML tags jika ada
        text = re.sub(r'<[^>]+>', '', text)
        
        # Remove special characters yang mengganggu
        text = re.sub(r'[^\w\s\.,;:!?()-]', '', text)
        
        return text.strip()
    
    def generate_digest_file(self, paper: PaperMetadata, output_path: Optional[Path] = None) -> Path:
        """
        Generate digest dan simpan ke file
        
        Args:
            paper: Metadata paper
            output_path: Path output (optional)
            
        Returns:
            Path file yang dibuat
        """
        if output_path is None:
            # Generate filename dari metadata
            safe_title = self._sanitize_filename(paper.judul)
            filename = f"{safe_title}-{paper.tahun}.md"
            output_path = Path(self._config["output_dir"]) / filename
        
        # Pastikan direktori ada
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Generate dan simpan digest
        digest_content = self.generate_digest(paper)
        output_path.write_text(digest_content, encoding='utf-8')
        
        return output_path
    
    def _sanitize_filename(self, title: str) -> str:
        """
        Sanitize judul untuk dijadikan nama file
        
        Args:
            title: Judul paper
            
        Returns:
            String yang aman untuk nama file
        """
        # Remove special characters
        safe_title = re.sub(r'[^\w\s-]', '', title)
        
        # Replace spaces with hyphens
        safe_title = re.sub(r'\s+', '-', safe_title)
        
        # Lowercase dan limit panjang
        safe_title = safe_title.lower()[:50]
        
        # Remove trailing hyphens
        safe_title = safe_title.strip('-')
        
        return safe_title if safe_title else "untitled"
    
    def batch_generate(self, papers: List[PaperMetadata], output_dir: Optional[Path] = None) -> List[Path]:
        """
        Generate digest untuk multiple papers
        
        Args:
            papers: List metadata paper
            output_dir: Direktori output (optional)
            
        Returns:
            List path file yang dibuat
        """
        if output_dir:
            original_dir = self._config["output_dir"]
            self._config["output_dir"] = str(output_dir)
        
        try:
            generated_files = []
            for paper in papers:
                try:
                    file_path = self.generate_digest_file(paper)
                    generated_files.append(file_path)
                except Exception as e:
                    print(f"Error generating digest for '{paper.judul}': {e}")
            
            return generated_files
            
        finally:
            if output_dir:
                self._config["output_dir"] = original_dir

# Convenience functions
def generate_digest_simple(paper: PaperMetadata, language: str = "id") -> str:
    """
    Fungsi sederhana untuk generate digest
    
    Args:
        paper: Metadata paper
        language: Bahasa (id/en)
        
    Returns:
        String konten markdown
    """
    generator = DigestGenerator()
    generator.configure({"language": language})
    return generator.generate_digest(paper)

def generate_digest_file_simple(paper: PaperMetadata, output_path: Path, language: str = "id") -> Path:
    """
    Fungsi sederhana untuk generate dan simpan digest
    
    Args:
        paper: Metadata paper
        output_path: Path output
        language: Bahasa (id/en)
        
    Returns:
        Path file yang dibuat
    """
    generator = DigestGenerator()
    generator.configure({"language": language})
    return generator.generate_digest_file(paper, output_path)