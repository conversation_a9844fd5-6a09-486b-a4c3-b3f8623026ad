"""
Plugin wrapper untuk integrasi MCP dengan ICikiwir plugin system

(c) 2025 Radhitya Guntoro Adhi
"""

import asyncio
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime
from pathlib import Path

from icikiwir.core.protocols import AnalyticsPlugin
from .mcp_client import MCPWebResearchClient, PaperMetadata, SearchResult
from .digest_generator import DigestGenerator

logger = logging.getLogger(__name__)

class MCPResearchPlugin(AnalyticsPlugin):
    """
    Plugin untuk integrasi MCP WebResearch dengan sistem ICikiwir
    Menyediakan kemampuan pencarian dan ekstraksi paper ilmiah
    """
    
    def __init__(self):
        self.client: Optional[MCPWebResearchClient] = None
        self.digest_generator = DigestGenerator()
        self._config = {
            "server_url": "http://localhost:8080",
            "timeout": 30,
            "max_results": 10,
            "auto_digest": True,
            "digest_language": "id",
            "output_dir": "references"
        }
        self._metrics = {
            "papers_searched": 0,
            "papers_extracted": 0,
            "digests_generated": 0,
            "errors": 0,
            "last_search_time": None
        }
        
    @property
    def name(self) -> str:
        return "mcp_research"
    
    @property
    def version(self) -> str:
        return "1.0.0"
    
    @property
    def description(self) -> str:
        return "Integrasi dengan MCP WebResearch untuk science backup dan pencarian paper ilmiah"
    
    def get_config_schema(self) -> Dict[str, Any]:
        """Return JSON Schema untuk validasi konfigurasi"""
        return {
            "type": "object",
            "properties": {
                "server_url": {
                    "type": "string",
                    "default": "http://localhost:8080",
                    "description": "URL MCP WebResearch server"
                },
                "timeout": {
                    "type": "integer",
                    "default": 30,
                    "description": "Timeout dalam detik untuk request"
                },
                "max_results": {
                    "type": "integer",
                    "default": 10,
                    "description": "Maksimal hasil pencarian"
                },
                "auto_digest": {
                    "type": "boolean",
                    "default": True,
                    "description": "Otomatis generate digest untuk paper"
                },
                "digest_language": {
                    "type": "string",
                    "default": "id",
                    "description": "Bahasa untuk digest (id/en)"
                },
                "output_dir": {
                    "type": "string",
                    "default": "references",
                    "description": "Direktori output untuk digest"
                }
            }
        }
    
    def configure(self, config: Dict[str, Any]) -> None:
        """Apply konfigurasi ke plugin"""
        self._config.update(config)
        
        # Reinitialize client dengan config baru
        if self.client:
            asyncio.create_task(self.client.disconnect())
        
        self.client = MCPWebResearchClient(
            base_url=self._config["server_url"],
            timeout=self._config["timeout"]
        )
        
        # Configure digest generator
        self.digest_generator.configure({
            "language": self._config["digest_language"],
            "output_dir": self._config["output_dir"]
        })
        
        logger.info(f"MCP Research Plugin dikonfigurasi dengan server: {self._config['server_url']}")
    
    def analyze_metrics(self, metrics: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze metrics dan trigger paper search jika diperlukan
        
        Args:
            metrics: Raw metrics dari vision/analytics plugins
            
        Returns:
            Dict dengan hasil analisis dan rekomendasi
        """
        try:
            # Analisis konteks belajar untuk menentukan kebutuhan referensi
            focus_score = metrics.get("focus_score", 0)
            session_duration = metrics.get("session_duration", 0)
            topic_keywords = metrics.get("topic_keywords", [])
            
            recommendations = []
            
            # Trigger pencarian jika fokus rendah dan ada keywords
            if focus_score < 60 and topic_keywords:
                recommendations.append({
                    "type": "paper_search",
                    "reason": "Fokus rendah, mungkin perlu referensi tambahan",
                    "keywords": topic_keywords,
                    "priority": "medium"
                })
            
            # Trigger pencarian jika sesi panjang tanpa referensi baru
            if session_duration > 60 and not self._metrics["last_search_time"]:
                recommendations.append({
                    "type": "paper_search", 
                    "reason": "Sesi belajar panjang, saatnya cari referensi baru",
                    "priority": "low"
                })
            
            return {
                "plugin": self.name,
                "timestamp": datetime.now().isoformat(),
                "recommendations": recommendations,
                "metrics": self._metrics.copy()
            }
            
        except Exception as e:
            logger.error(f"Error dalam analyze_metrics: {e}")
            self._metrics["errors"] += 1
            return {
                "plugin": self.name,
                "error": str(e),
                "metrics": self._metrics.copy()
            }
    
    async def search_papers_async(self, query: str, max_results: Optional[int] = None) -> SearchResult:
        """
        Cari paper secara asynchronous
        
        Args:
            query: Query pencarian
            max_results: Maksimal hasil (default dari config)
            
        Returns:
            SearchResult dengan daftar paper
        """
        if not self.client:
            self.configure({})  # Use default config
        
        if max_results is None:
            max_results = self._config["max_results"]
        
        try:
            result = await self.client.search_papers(query, max_results)
            self._metrics["papers_searched"] += len(result.papers)
            self._metrics["last_search_time"] = datetime.now().isoformat()
            
            logger.info(f"Pencarian '{query}' menemukan {len(result.papers)} paper")
            return result
            
        except Exception as e:
            logger.error(f"Error saat pencarian paper: {e}")
            self._metrics["errors"] += 1
            raise
    
    def search_papers(self, query: str, max_results: Optional[int] = None) -> SearchResult:
        """
        Cari paper (synchronous wrapper)
        
        Args:
            query: Query pencarian
            max_results: Maksimal hasil
            
        Returns:
            SearchResult dengan daftar paper
        """
        try:
            # Jalankan async function dalam event loop
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # Jika sudah ada event loop, buat task
                task = asyncio.create_task(self.search_papers_async(query, max_results))
                return asyncio.run_coroutine_threadsafe(task, loop).result()
            else:
                # Jika belum ada event loop, jalankan langsung
                return asyncio.run(self.search_papers_async(query, max_results))
        except Exception as e:
            logger.error(f"Error dalam search_papers: {e}")
            # Return empty result jika error
            return SearchResult(
                query=query,
                total_results=0,
                papers=[],
                search_time=0.0,
                timestamp=datetime.now().isoformat()
            )
    
    async def extract_paper_async(self, url: str) -> Optional[PaperMetadata]:
        """
        Extract metadata paper secara asynchronous
        
        Args:
            url: URL paper
            
        Returns:
            PaperMetadata jika berhasil
        """
        if not self.client:
            self.configure({})
        
        try:
            metadata = await self.client.extract_paper_metadata(url)
            if metadata:
                self._metrics["papers_extracted"] += 1
                logger.info(f"Berhasil extract metadata dari {url}")
            return metadata
            
        except Exception as e:
            logger.error(f"Error saat extract paper: {e}")
            self._metrics["errors"] += 1
            return None
    
    def extract_paper(self, url: str) -> Optional[PaperMetadata]:
        """
        Extract metadata paper (synchronous wrapper)
        
        Args:
            url: URL paper
            
        Returns:
            PaperMetadata jika berhasil
        """
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                task = asyncio.create_task(self.extract_paper_async(url))
                return asyncio.run_coroutine_threadsafe(task, loop).result()
            else:
                return asyncio.run(self.extract_paper_async(url))
        except Exception as e:
            logger.error(f"Error dalam extract_paper: {e}")
            return None
    
    def generate_digest(self, paper: PaperMetadata, output_path: Optional[Path] = None) -> Optional[Path]:
        """
        Generate digest markdown untuk paper
        
        Args:
            paper: Metadata paper
            output_path: Path output (optional)
            
        Returns:
            Path file digest jika berhasil
        """
        try:
            if output_path is None:
                # Generate filename dari metadata paper
                safe_title = "".join(c for c in paper.judul if c.isalnum() or c in (' ', '-', '_')).rstrip()
                safe_title = safe_title.replace(' ', '-').lower()[:50]
                filename = f"{safe_title}-{paper.tahun}.md"
                output_path = Path(self._config["output_dir"]) / filename
            
            # Pastikan direktori ada
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Generate digest
            digest_content = self.digest_generator.generate_digest(paper)
            output_path.write_text(digest_content, encoding='utf-8')
            
            self._metrics["digests_generated"] += 1
            logger.info(f"Digest disimpan ke {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"Error saat generate digest: {e}")
            self._metrics["errors"] += 1
            return None
    
    def search_and_digest(self, query: str, max_results: Optional[int] = None) -> List[Path]:
        """
        Cari paper dan generate digest otomatis
        
        Args:
            query: Query pencarian
            max_results: Maksimal hasil
            
        Returns:
            List path file digest yang berhasil dibuat
        """
        try:
            # Cari paper
            search_result = self.search_papers(query, max_results)
            
            digest_paths = []
            
            # Generate digest untuk setiap paper jika auto_digest enabled
            if self._config["auto_digest"]:
                for paper in search_result.papers:
                    digest_path = self.generate_digest(paper)
                    if digest_path:
                        digest_paths.append(digest_path)
            
            logger.info(f"Berhasil generate {len(digest_paths)} digest dari query '{query}'")
            return digest_paths
            
        except Exception as e:
            logger.error(f"Error dalam search_and_digest: {e}")
            self._metrics["errors"] += 1
            return []
    
    def get_metrics(self) -> Dict[str, Any]:
        """Dapatkan metrics plugin"""
        metrics = self._metrics.copy()
        
        # Tambah metrics dari client jika ada
        if self.client:
            client_metrics = self.client.get_metrics()
            metrics.update({f"client_{k}": v for k, v in client_metrics.items()})
        
        return metrics
    
    def cleanup(self) -> None:
        """Cleanup resources plugin"""
        try:
            if self.client:
                # Disconnect client secara asynchronous
                asyncio.create_task(self.client.disconnect())
                self.client = None
            
            logger.info("MCP Research Plugin berhasil di-cleanup")
            
        except Exception as e:
            logger.error(f"Error saat cleanup: {e}")

# Convenience functions untuk penggunaan langsung
def search_papers_simple(query: str, max_results: int = 5) -> List[PaperMetadata]:
    """
    Fungsi sederhana untuk pencarian paper
    
    Args:
        query: Query pencarian
        max_results: Maksimal hasil
        
    Returns:
        List PaperMetadata
    """
    plugin = MCPResearchPlugin()
    plugin.configure({})
    
    try:
        result = plugin.search_papers(query, max_results)
        return result.papers
    finally:
        plugin.cleanup()

def search_and_digest_simple(query: str, max_results: int = 3) -> List[Path]:
    """
    Fungsi sederhana untuk pencarian dan generate digest
    
    Args:
        query: Query pencarian
        max_results: Maksimal hasil
        
    Returns:
        List path file digest
    """
    plugin = MCPResearchPlugin()
    plugin.configure({"auto_digest": True})
    
    try:
        return plugin.search_and_digest(query, max_results)
    finally:
        plugin.cleanup()