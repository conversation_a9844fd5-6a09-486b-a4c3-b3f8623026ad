"""
Analytics Logger Plugin untuk ICikiwir
Plugin yang subscribe ke focus_metric events dan menyimpan ke storage
Mengimplementasikan AnalyticsPlugin interface

(c) 2025 Radhitya Guntoro Adhi
"""

import asyncio
import time
import uuid
from typing import Dict, Any, Optional
from datetime import datetime

from icikiwir.core.protocols import AnalyticsPlugin
from icikiwir.core.event_bus import event_bus, AnalyticsEvent
from ..storage.logger import logger, FocusMetrics


class AnalyticsLogger:
    """
    Plugin Analytics Logger yang subscribe ke focus_metric events
    dan menyimpan data ke storage layer
    """
    
    def __init__(self):
        """Inisialisasi AnalyticsLogger plugin"""
        self._name = "analytics_logger"
        self._version = "1.0.0"
        self._description = "Plugin untuk logging metrik fokus ke database secara realtime"
        
        # Session tracking
        self._current_session_id = None
        self._session_start_time = None
        
        # Event subscription
        self._subscription_id = None
        self._running = False
        
        # Statistics
        self._events_processed = 0
        self._last_event_time = None
        
    @property
    def name(self) -> str:
        """Nama unik plugin"""
        return self._name
    
    @property
    def version(self) -> str:
        """Versi plugin"""
        return self._version
    
    @property
    def description(self) -> str:
        """Deskripsi plugin"""
        return self._description
    
    async def start(self):
        """Mulai plugin dan subscribe ke events"""
        if self._running:
            return
            
        # Start session baru
        self._start_new_session()
        
        # Subscribe ke analytics events (focus_metric)
        self._subscription_id = event_bus.subscribe("analytics", self._handle_focus_metric_event)
        
        # Start storage logger
        await logger.start()
        
        self._running = True
        print(f"✅ {self.name} plugin dimulai (session: {self._current_session_id})")
    
    async def stop(self):
        """Hentikan plugin dan cleanup"""
        if not self._running:
            return
            
        # Unsubscribe dari events
        if self._subscription_id:
            event_bus.unsubscribe("analytics", handler=self._handle_focus_metric_event)
            self._subscription_id = None
        
        # Stop storage logger
        await logger.stop()
        
        self._running = False
        print(f"🛑 {self.name} plugin dihentikan")
    
    def _start_new_session(self):
        """Mulai session logging baru"""
        self._current_session_id = f"session_{int(time.time())}_{str(uuid.uuid4())[:8]}"
        self._session_start_time = time.time()
        print(f"🆕 Session baru dimulai: {self._current_session_id}")
    
    async def _handle_focus_metric_event(self, event: AnalyticsEvent):
        """
        Handler untuk focus_metric events
        
        Args:
            event: AnalyticsEvent yang berisi data focus metrics
        """
        try:
            # Validasi event data
            if not self._validate_event_data(event.data):
                print(f"⚠️ Event data tidak valid: {event.data}")
                return
            
            # Extract metrics dari event data
            metrics = self._extract_metrics_from_event(event)
            
            # Log ke storage
            await logger.log_focus(metrics)
            
            # Update statistics
            self._events_processed += 1
            self._last_event_time = time.time()
            
            # Debug log
            print(f"📝 Metrik logged: focus_score={metrics.focus_score:.1f}, "
                  f"blink_rate={metrics.blink_rate:.1f}, "
                  f"head_var={metrics.head_yaw_variance:.2f}")
            
        except Exception as e:
            print(f"❌ Error handling focus metric event: {e}")
    
    def _validate_event_data(self, data: Dict[str, Any]) -> bool:
        """
        Validasi data event focus_metric
        
        Args:
            data: Data dari event
            
        Returns:
            bool: True jika valid
        """
        required_fields = ['focus_score', 'blink_rate', 'head_yaw_variance']
        
        for field in required_fields:
            if field not in data:
                return False
            
            # Validasi tipe data
            if not isinstance(data[field], (int, float)):
                return False
        
        # Validasi range nilai
        if not (0 <= data['focus_score'] <= 100):
            return False
            
        if data['blink_rate'] < 0:
            return False
            
        if data['head_yaw_variance'] < 0:
            return False
        
        return True
    
    def _extract_metrics_from_event(self, event: AnalyticsEvent) -> FocusMetrics:
        """
        Extract FocusMetrics dari AnalyticsEvent
        
        Args:
            event: AnalyticsEvent
            
        Returns:
            FocusMetrics: Instance FocusMetrics
        """
        data = event.data
        
        # Buat metadata dari event
        metadata = {
            'event_id': event.event_id,
            'source': event.source,
            'event_timestamp': event.timestamp.isoformat(),
        }
        
        # Tambahkan data tambahan jika ada
        for key, value in data.items():
            if key not in ['focus_score', 'blink_rate', 'head_yaw_variance']:
                metadata[key] = value
        
        return FocusMetrics(
            timestamp=event.timestamp.timestamp(),
            blink_rate=float(data['blink_rate']),
            head_yaw_variance=float(data['head_yaw_variance']),
            focus_score=float(data['focus_score']),
            session_id=self._current_session_id,
            metadata=metadata
        )
    
    def analyze_metrics(self, metrics: Dict[str, Any]) -> Dict[str, Any]:
        """
        Implementasi AnalyticsPlugin interface
        Menganalisis raw metrics dan menghasilkan insights
        
        Args:
            metrics: Raw metrics dari vision plugins
            
        Returns:
            Dict: Hasil analisis dengan insights
        """
        # Extract focus metrics
        focus_score = metrics.get('focus_score', 50.0)
        blink_rate = metrics.get('blink_rate', 15.0)
        head_variance = metrics.get('head_yaw_variance', 50.0)
        
        # Tentukan attention level
        if focus_score >= 80:
            attention_level = 'high'
            level_desc = 'Sangat Fokus'
        elif focus_score >= 60:
            attention_level = 'medium'
            level_desc = 'Fokus Baik'
        elif focus_score >= 40:
            attention_level = 'low'
            level_desc = 'Kurang Fokus'
        else:
            attention_level = 'very_low'
            level_desc = 'Tidak Fokus'
        
        # Generate insights
        insights = []
        recommendations = []
        
        if focus_score < 40:
            insights.append(f"Skor fokus rendah ({focus_score:.1f})")
            recommendations.append("Pertimbangkan untuk istirahat sejenak")
            
        if blink_rate > 25:
            insights.append(f"Blink rate tinggi ({blink_rate:.1f}/menit)")
            recommendations.append("Mungkin mata lelah, coba istirahat mata")
            
        if blink_rate < 8:
            insights.append(f"Blink rate rendah ({blink_rate:.1f}/menit)")
            recommendations.append("Ingat untuk berkedip lebih sering")
            
        if head_variance > 100:
            insights.append(f"Gerakan kepala tidak stabil (variance: {head_variance:.1f})")
            recommendations.append("Coba pertahankan posisi kepala yang stabil")
        
        # Publish analytics event untuk logging
        if self._running:
            analytics_event = AnalyticsEvent(
                source=self.name,
                data={
                    'focus_score': focus_score,
                    'blink_rate': blink_rate,
                    'head_yaw_variance': head_variance,
                    'attention_level': attention_level,
                    'insights': insights,
                    'recommendations': recommendations
                }
            )
            
            # Publish secara async (non-blocking)
            asyncio.create_task(event_bus.publish(analytics_event))
        
        return {
            'focus_score': focus_score / 100.0,  # Normalize ke 0-1
            'attention_level': attention_level,
            'attention_description': level_desc,
            'insights': insights,
            'recommendations': recommendations,
            'metadata': {
                'session_id': self._current_session_id,
                'events_processed': self._events_processed,
                'last_event_time': self._last_event_time,
                'session_duration': time.time() - self._session_start_time if self._session_start_time else 0
            }
        }
    
    def get_config_schema(self) -> Dict[str, Any]:
        """
        Schema konfigurasi plugin
        
        Returns:
            Dict: JSON Schema
        """
        return {
            "type": "object",
            "properties": {
                "flush_interval": {
                    "type": "integer",
                    "minimum": 5,
                    "maximum": 300,
                    "default": 30,
                    "description": "Interval flush buffer ke database (detik)"
                },
                "session_auto_restart": {
                    "type": "boolean",
                    "default": True,
                    "description": "Otomatis restart session setiap hari"
                },
                "enable_debug_logging": {
                    "type": "boolean",
                    "default": False,
                    "description": "Aktifkan debug logging"
                }
            }
        }
    
    def configure(self, config: Dict[str, Any]) -> None:
        """
        Terapkan konfigurasi ke plugin
        
        Args:
            config: Dictionary konfigurasi
        """
        if 'flush_interval' in config:
            logger.flush_interval = config['flush_interval']
            print(f"🔧 Flush interval diubah ke {config['flush_interval']} detik")
        
        if 'session_auto_restart' in config:
            # TODO: Implementasi auto restart session
            print(f"🔧 Session auto restart: {config['session_auto_restart']}")
        
        if 'enable_debug_logging' in config:
            # TODO: Implementasi debug logging
            print(f"🔧 Debug logging: {config['enable_debug_logging']}")
    
    def cleanup(self) -> None:
        """Cleanup resources"""
        if self._running:
            # Jalankan stop secara sync
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    asyncio.create_task(self.stop())
                else:
                    loop.run_until_complete(self.stop())
            except RuntimeError:
                asyncio.run(self.stop())
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        Mendapatkan statistik plugin
        
        Returns:
            Dict: Statistik plugin
        """
        return {
            'name': self.name,
            'version': self.version,
            'running': self._running,
            'current_session_id': self._current_session_id,
            'session_start_time': self._session_start_time,
            'events_processed': self._events_processed,
            'last_event_time': self._last_event_time,
            'session_duration': time.time() - self._session_start_time if self._session_start_time else 0,
            'storage_stats': logger.get_stats()
        }


# Instance global plugin
analytics_logger_plugin = AnalyticsLogger()


# Convenience functions
async def start_analytics_logging():
    """Mulai analytics logging"""
    await analytics_logger_plugin.start()


async def stop_analytics_logging():
    """Hentikan analytics logging"""
    await analytics_logger_plugin.stop()


def get_logging_statistics() -> Dict[str, Any]:
    """Dapatkan statistik logging"""
    return analytics_logger_plugin.get_statistics()