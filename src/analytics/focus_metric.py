"""
Focus Metric Calculator
Menghitung skor fokus berdasarkan blink rate dan head pose variance
Menggunakan algoritma scoring untuk menghasilkan nilai 0-100

(c) 2025 Radhitya Guntoro Adhi
"""

import numpy as np
from typing import List, Dict, Optional, Tuple
from collections import deque
import time
import math
import asyncio

class FocusMetricCalculator:
    """
    Kelas untuk menghitung metrik fokus real-time
    Menganalisis blink rate dan head pose variance untuk scoring
    """
    
    def __init__(self,
                 blink_window_sec: int = 60,
                 head_var_window: int = 60,
                 normal_blink_range: Tuple[float, float] = (12.0, 20.0)):
        """
        Inisialisasi FocusMetricCalculator
        
        Args:
            blink_window_sec: Window waktu untuk menghitung blink rate (detik)
            head_var_window: Window waktu untuk menghitung head pose variance (detik)
            normal_blink_range: Range normal blink rate per menit (min, max)
        """
        self.blink_window_sec = blink_window_sec
        self.head_var_window = head_var_window
        self.normal_blink_range = normal_blink_range
        
        # Buffer untuk menyimpan data historis
        self.blink_history = deque(maxlen=blink_window_sec * 2)  # Buffer 2x window size
        self.head_pose_history = deque(maxlen=head_var_window * 2)
        
        # Tracking blink events
        self.last_ear_values = deque(maxlen=5)  # Buffer untuk deteksi blink
        self.ear_threshold = 0.25  # Threshold untuk deteksi blink
        self.blink_count = 0
        self.last_blink_time = 0
        
        # Statistik
        self.total_calculations = 0
        self.last_focus_score = 50.0  # Default score
        
        # Event publishing
        self.enable_events = True
        
    def add_frame_data(self, ear_average: Optional[float], head_pose: Optional[Dict[str, float]],
                      micro_expressions: Optional[Dict[str, float]] = None,
                      expression_focus_score: Optional[float] = None,
                      behavior_focus_impact: Optional[float] = None,
                      face_detected: bool = True,
                      behavior_data: Optional[Dict] = None,
                      timestamp: Optional[float] = None):
        """
        Menambahkan data frame baru untuk analisis dengan dukungan continuous monitoring
        
        Args:
            ear_average: Eye Aspect Ratio rata-rata (None jika wajah tidak terdeteksi)
            head_pose: Dictionary dengan yaw, pitch, roll (None jika wajah tidak terdeteksi)
            micro_expressions: Dictionary skor mikro ekspresi
            expression_focus_score: Skor fokus dari analisis ekspresi
            behavior_focus_impact: Dampak fokus dari behavior analysis
            face_detected: Apakah wajah terdeteksi pada frame ini
            behavior_data: Data behavior analysis untuk fallback
            timestamp: Timestamp frame (default: waktu saat ini)
        """
        if timestamp is None:
            timestamp = time.time()
        
        # Jika wajah terdeteksi, proses data normal
        if face_detected and ear_average is not None and head_pose is not None:
            # Deteksi blink berdasarkan EAR
            self._detect_blink(ear_average, timestamp)
            
            # Simpan data head pose dengan timestamp
            head_pose_data = {
                'timestamp': timestamp,
                'yaw': head_pose.get('yaw', 0.0),
                'pitch': head_pose.get('pitch', 0.0),
                'roll': head_pose.get('roll', 0.0),
                'micro_expressions': micro_expressions or {},
                'expression_focus_score': expression_focus_score or 50.0,
                'behavior_focus_impact': behavior_focus_impact or 0.0,
                'face_detected': True,
                'behavior_data': behavior_data or {}
            }
            self.head_pose_history.append(head_pose_data)
        else:
            # Fallback: gunakan behavior analysis untuk continuous monitoring
            fallback_data = {
                'timestamp': timestamp,
                'yaw': 0.0,  # Default values
                'pitch': 0.0,
                'roll': 0.0,
                'micro_expressions': {},
                'expression_focus_score': self._estimate_focus_from_behavior(behavior_data),
                'behavior_focus_impact': behavior_focus_impact or 0.0,
                'face_detected': False,
                'behavior_data': behavior_data or {},
                'is_fallback': True
            }
            self.head_pose_history.append(fallback_data)
            
            # Estimasi blink rate dari behavior patterns
            self._estimate_blink_from_behavior(behavior_data, timestamp)
        
        # Bersihkan data lama
        self._cleanup_old_data(timestamp)
    
    def _detect_blink(self, ear: float, timestamp: float):
        """
        Mendeteksi blink berdasarkan EAR threshold
        
        Args:
            ear: Eye Aspect Ratio saat ini
            timestamp: Timestamp frame
        """
        self.last_ear_values.append(ear)
        
        # Deteksi blink: EAR turun di bawah threshold lalu naik kembali
        if len(self.last_ear_values) >= 3:
            # Cek pola: tinggi -> rendah -> tinggi
            if (self.last_ear_values[-3] > self.ear_threshold and 
                self.last_ear_values[-2] <= self.ear_threshold and 
                self.last_ear_values[-1] > self.ear_threshold):
                
                # Hindari double counting (minimal 200ms antar blink)
                if timestamp - self.last_blink_time > 0.2:
                    self.blink_count += 1
                    self.last_blink_time = timestamp
                    
                    # Simpan blink event
                    blink_data = {
                        'timestamp': timestamp,
                        'ear_value': self.last_ear_values[-2]
                    }
                    self.blink_history.append(blink_data)
    
    def _cleanup_old_data(self, current_timestamp: float):
        """
        Membersihkan data yang sudah terlalu lama
        
        Args:
            current_timestamp: Timestamp saat ini
        """
        # Bersihkan blink history
        while (self.blink_history and 
               current_timestamp - self.blink_history[0]['timestamp'] > self.blink_window_sec):
            self.blink_history.popleft()
            
        # Bersihkan head pose history
        while (self.head_pose_history and 
               current_timestamp - self.head_pose_history[0]['timestamp'] > self.head_var_window):
            self.head_pose_history.popleft()
    
    def calculate_blink_rate(self, timestamp: Optional[float] = None) -> float:
        """
        Menghitung blink rate per menit dalam window waktu
        
        Args:
            timestamp: Timestamp referensi (default: waktu saat ini)
            
        Returns:
            float: Blink rate per menit
        """
        if timestamp is None:
            timestamp = time.time()
            
        # Hitung blink dalam window
        recent_blinks = [b for b in self.blink_history 
                        if timestamp - b['timestamp'] <= self.blink_window_sec]
        
        if not recent_blinks:
            return 0.0
            
        # Konversi ke blink per menit
        window_minutes = self.blink_window_sec / 60.0
        blink_rate = len(recent_blinks) / window_minutes
        
        return blink_rate
    
    def calculate_head_pose_variance(self, timestamp: Optional[float] = None) -> Dict[str, float]:
        """
        Menghitung variance head pose dalam window waktu
        
        Args:
            timestamp: Timestamp referensi (default: waktu saat ini)
            
        Returns:
            Dict: Variance untuk yaw, pitch, roll dan total variance
        """
        if timestamp is None:
            timestamp = time.time()
            
        # Ambil data head pose dalam window
        recent_poses = [p for p in self.head_pose_history 
                       if timestamp - p['timestamp'] <= self.head_var_window]
        
        if len(recent_poses) < 2:
            return {'yaw_var': 0.0, 'pitch_var': 0.0, 'roll_var': 0.0, 'total_var': 0.0}
        
        # Ekstrak nilai untuk setiap axis
        yaw_values = [p['yaw'] for p in recent_poses]
        pitch_values = [p['pitch'] for p in recent_poses]
        roll_values = [p['roll'] for p in recent_poses]
        
        # Hitung variance
        yaw_var = np.var(yaw_values) if len(yaw_values) > 1 else 0.0
        pitch_var = np.var(pitch_values) if len(pitch_values) > 1 else 0.0
        roll_var = np.var(roll_values) if len(roll_values) > 1 else 0.0
        
        # Total variance (weighted sum)
        total_var = (yaw_var * 0.5) + (pitch_var * 0.3) + (roll_var * 0.2)
        
        return {
            'yaw_var': yaw_var,
            'pitch_var': pitch_var,
            'roll_var': roll_var,
            'total_var': total_var
        }
    
    def compute_focus(self, blink_rate: Optional[float] = None,
                     head_variance: Optional[float] = None,
                     expression_focus_score: Optional[float] = None,
                     behavior_focus_impact: Optional[float] = None,
                     timestamp: Optional[float] = None) -> float:
        """
        Menghitung skor fokus berdasarkan blink rate, head pose variance, dan mikro ekspresi
        
        Args:
            blink_rate: Blink rate per menit (jika None, akan dihitung otomatis)
            head_variance: Head pose variance (jika None, akan dihitung otomatis)
            expression_focus_score: Skor fokus dari mikro ekspresi (jika None, akan dihitung otomatis)
            timestamp: Timestamp referensi
            
        Returns:
            float: Skor fokus 0-100 (semakin tinggi = lebih fokus)
        """
        if timestamp is None:
            timestamp = time.time()
            
        # Hitung blink rate jika tidak disediakan
        if blink_rate is None:
            blink_rate = self.calculate_blink_rate(timestamp)
            
        # Hitung head variance jika tidak disediakan
        if head_variance is None:
            head_var_data = self.calculate_head_pose_variance(timestamp)
            head_variance = head_var_data['total_var']
        
        # Hitung expression focus score jika tidak disediakan
        if expression_focus_score is None:
            expression_focus_score = self._get_recent_expression_score(timestamp)
        
        # Hitung behavior focus impact jika tidak disediakan
        if behavior_focus_impact is None:
            behavior_focus_impact = self._get_recent_behavior_impact(timestamp)
        
        # Scoring berdasarkan blink rate
        blink_score = self._score_blink_rate(blink_rate)
        
        # Scoring berdasarkan head pose variance
        head_score = self._score_head_variance(head_variance)
        
        # Kombinasi weighted score dengan mikro ekspresi dan behavior analysis
        # Bobot: blink_rate (30%) + head_pose (20%) + facial_expression (35%) + behavior (15%)
        base_score = (
            (blink_score * 0.30) +      # Blink rate (EAR)
            (head_score * 0.20) +       # Head pose stability
            (expression_focus_score * 0.35)  # Facial expression (mikro ekspresi)
        )
        
        # Tambahkan behavior impact (dapat positif atau negatif)
        # Behavior impact dalam range -20 hingga +20, dikonversi ke kontribusi 15%
        behavior_contribution = behavior_focus_impact * 0.15
        focus_score = base_score + behavior_contribution
        
        # Clamp ke range 0-100
        focus_score = max(0.0, min(100.0, focus_score))
        
        # Update statistik
        self.total_calculations += 1
        self.last_focus_score = focus_score
        
        # Publish focus_metric event jika enabled
        if self.enable_events:
            self._publish_focus_metric_event(focus_score, blink_rate, head_variance, expression_focus_score, behavior_focus_impact, timestamp)
        
        return focus_score
    
    def _score_blink_rate(self, blink_rate: float) -> float:
        """
        Menghitung skor berdasarkan blink rate
        
        Args:
            blink_rate: Blink rate per menit
            
        Returns:
            float: Skor 0-100
        """
        min_normal, max_normal = self.normal_blink_range
        
        if min_normal <= blink_rate <= max_normal:
            # Dalam range normal = skor tinggi
            return 85.0
        elif blink_rate < min_normal:
            # Terlalu sedikit blink = mungkin sangat fokus atau mengantuk
            if blink_rate < min_normal * 0.5:
                return 40.0  # Kemungkinan mengantuk
            else:
                return 75.0  # Sangat fokus
        else:
            # Terlalu banyak blink = stress atau tidak fokus
            excess_ratio = blink_rate / max_normal
            if excess_ratio > 2.0:
                return 20.0  # Sangat tidak fokus
            else:
                return max(30.0, 70.0 - (excess_ratio - 1.0) * 40.0)
    
    def _score_head_variance(self, head_variance: float) -> float:
        """
        Menghitung skor berdasarkan head pose variance
        
        Args:
            head_variance: Total head pose variance
            
        Returns:
            float: Skor 0-100
        """
        # Threshold variance (dalam derajat kuadrat)
        low_variance_threshold = 25.0    # Sangat stabil
        medium_variance_threshold = 100.0  # Cukup stabil
        high_variance_threshold = 400.0   # Tidak stabil
        
        if head_variance <= low_variance_threshold:
            # Kepala sangat stabil = fokus tinggi
            return 90.0
        elif head_variance <= medium_variance_threshold:
            # Kepala cukup stabil = fokus sedang
            ratio = head_variance / medium_variance_threshold
            return 90.0 - (ratio * 30.0)  # 90 -> 60
        elif head_variance <= high_variance_threshold:
            # Kepala tidak stabil = fokus rendah
            ratio = (head_variance - medium_variance_threshold) / (high_variance_threshold - medium_variance_threshold)
            return 60.0 - (ratio * 40.0)  # 60 -> 20
        else:
            # Kepala sangat tidak stabil = fokus sangat rendah
            return 20.0
    
    def _get_recent_expression_score(self, timestamp: Optional[float] = None) -> float:
        """
        Mendapatkan skor fokus dari mikro ekspresi terbaru
        
        Args:
            timestamp: Timestamp referensi
            
        Returns:
            float: Skor fokus dari ekspresi (0-100)
        """
        if timestamp is None:
            timestamp = time.time()
        
        # Ambil data head pose terbaru yang mengandung expression data
        recent_poses = [p for p in self.head_pose_history
                       if timestamp - p['timestamp'] <= 10.0]  # 10 detik terakhir
        
        if not recent_poses:
            return 50.0  # Default neutral
        
        # Ambil expression scores dari data terbaru
        expression_scores = []
        for pose_data in recent_poses:
            expr_score = pose_data.get('expression_focus_score', 50.0)
            if expr_score > 0:  # Valid score
                expression_scores.append(expr_score)
        
        if expression_scores:
            # Return weighted average (lebih berat ke data terbaru)
            weights = np.linspace(0.5, 1.0, len(expression_scores))
            weighted_avg = np.average(expression_scores, weights=weights)
            return weighted_avg
        else:
            return 50.0  # Default neutral
    
    def _get_recent_behavior_impact(self, timestamp: Optional[float] = None) -> float:
        """
        Mendapatkan dampak fokus dari behavior analysis terbaru
        
        Args:
            timestamp: Timestamp referensi
            
        Returns:
            float: Dampak fokus dari behavior (-20 hingga +20)
        """
        if timestamp is None:
            timestamp = time.time()
        
        # Ambil data head pose terbaru yang mengandung behavior data
        recent_poses = [p for p in self.head_pose_history
                       if timestamp - p['timestamp'] <= 5.0]  # 5 detik terakhir
        
        if not recent_poses:
            return 0.0  # Default neutral
        
        # Ambil behavior impacts dari data terbaru
        behavior_impacts = []
        for pose_data in recent_poses:
            behavior_impact = pose_data.get('behavior_focus_impact', 0.0)
            behavior_impacts.append(behavior_impact)
        
        if behavior_impacts:
            # Return weighted average (lebih berat ke data terbaru)
            weights = np.linspace(0.3, 1.0, len(behavior_impacts))
            weighted_avg = np.average(behavior_impacts, weights=weights)
            return weighted_avg
        else:
            return 0.0  # Default neutral
    
    def _publish_focus_metric_event(self, focus_score: float, blink_rate: float,
                                   head_variance: float, expression_focus_score: float,
                                   behavior_focus_impact: float, timestamp: float):
        """
        Publish focus_metric event ke event bus
        
        Args:
            focus_score: Skor fokus yang dihitung
            blink_rate: Blink rate per menit
            head_variance: Head pose variance
            expression_focus_score: Skor fokus dari mikro ekspresi
            timestamp: Timestamp event
        """
        try:
            # Import event bus secara lazy untuk menghindari circular import
            from icikiwir.core.event_bus import publish_focus_metric_event
            
            # Buat data event
            event_data = {
                'focus_score': focus_score,
                'blink_rate': blink_rate,
                'head_variance': head_variance,
                'expression_focus_score': expression_focus_score,
                'behavior_focus_impact': behavior_focus_impact,
                'timestamp': timestamp,
                'focus_level': self.get_focus_level_description(focus_score),
                'blink_score': self._score_blink_rate(blink_rate),
                'head_score': self._score_head_variance(head_variance),
                'total_calculations': self.total_calculations
            }
            
            # Publish event secara async (non-blocking)
            def publish_async():
                try:
                    loop = asyncio.get_event_loop()
                    if loop.is_running():
                        # Jika event loop sudah berjalan, schedule task
                        asyncio.create_task(
                            publish_focus_metric_event('focus_calculator', event_data)
                        )
                    else:
                        # Jika tidak ada event loop, jalankan secara sync
                        asyncio.run(
                            publish_focus_metric_event('focus_calculator', event_data)
                        )
                except Exception as e:
                    # Silent fail untuk event publishing
                    pass
            
            # Jalankan publishing di thread terpisah untuk non-blocking
            import threading
            threading.Thread(target=publish_async, daemon=True).start()
            
        except ImportError:
            # Event bus tidak tersedia, skip publishing
            pass
        except Exception as e:
            # Silent fail untuk event publishing
            pass
    
    def get_current_metrics(self, timestamp: Optional[float] = None) -> Dict[str, float]:
        """
        Mendapatkan semua metrik saat ini
        
        Args:
            timestamp: Timestamp referensi
            
        Returns:
            Dict: Metrik lengkap
        """
        if timestamp is None:
            timestamp = time.time()
            
        blink_rate = self.calculate_blink_rate(timestamp)
        head_var_data = self.calculate_head_pose_variance(timestamp)
        focus_score = self.compute_focus(blink_rate, head_var_data['total_var'], timestamp)
        
        expression_focus_score = self._get_recent_expression_score(timestamp)
        
        return {
            'focus_score': focus_score,
            'blink_rate': blink_rate,
            'head_variance': head_var_data['total_var'],
            'head_yaw_var': head_var_data['yaw_var'],
            'head_pitch_var': head_var_data['pitch_var'],
            'head_roll_var': head_var_data['roll_var'],
            'expression_focus_score': expression_focus_score,
            'total_blinks': len(self.blink_history),
            'data_points': len(self.head_pose_history)
        }
    
    def get_focus_level_description(self, focus_score: float) -> str:
        """
        Mendapatkan deskripsi level fokus berdasarkan skor
        
        Args:
            focus_score: Skor fokus 0-100
            
        Returns:
            str: Deskripsi level fokus
        """
        if focus_score >= 80:
            return "Sangat Fokus 🎯"
        elif focus_score >= 60:
            return "Fokus Baik ✅"
        elif focus_score >= 40:
            return "Fokus Sedang ⚠️"
        elif focus_score >= 20:
            return "Kurang Fokus ⚡"
        else:
            return "Tidak Fokus ❌"
    
    def reset_data(self):
        """
        Reset semua data historis
        """
        self.blink_history.clear()
        self.head_pose_history.clear()
        self.last_ear_values.clear()
        self.blink_count = 0
        self.last_blink_time = 0
        self.total_calculations = 0
        self.last_focus_score = 50.0
        
        print("🔄 Data focus metric direset")
    
    def _estimate_focus_from_behavior(self, behavior_data: Optional[Dict]) -> float:
        """
        Estimasi skor fokus dari behavior analysis ketika wajah tidak terdeteksi
        
        Args:
            behavior_data: Data dari AdvancedBehaviorDetector
            
        Returns:
            float: Estimasi skor fokus berdasarkan behavior
        """
        if not behavior_data:
            return 50.0  # Default neutral
        
        # Ambil aktivitas dominan
        activity = behavior_data.get('activity', {})
        dominant_activity = activity.get('dominant', 'neutral')
        activity_confidence = activity.get('confidence', 0.0)
        
        # Skor dasar berdasarkan aktivitas
        activity_scores = {
            'writing': 75.0,     # Produktif, fokus tinggi
            'typing': 70.0,      # Produktif, fokus baik
            'neutral': 50.0,     # Netral
            'drinking': 45.0,    # Sedikit terganggu
            'phone_use': 25.0,   # Sangat terganggu
            'smoking': 30.0      # Terganggu
        }
        
        base_score = activity_scores.get(dominant_activity, 50.0)
        
        # Adjust berdasarkan confidence
        confidence_adjusted = base_score * activity_confidence + 50.0 * (1 - activity_confidence)
        
        # Faktor postur
        posture = behavior_data.get('posture', {})
        posture_quality = posture.get('posture_quality', 'unknown')
        posture_adjustments = {
            'excellent': 10.0,
            'good': 5.0,
            'fair': 0.0,
            'poor': -10.0,
            'unknown': 0.0
        }
        
        posture_bonus = posture_adjustments.get(posture_quality, 0.0)
        
        # Faktor cognitive load
        cognitive_load = behavior_data.get('cognitive_load', {})
        load_level = cognitive_load.get('overall_load', 'normal')
        load_adjustments = {
            'low': 5.0,
            'normal': 0.0,
            'high': -5.0,
            'very_high': -15.0
        }
        
        load_penalty = load_adjustments.get(load_level, 0.0)
        
        # Kombinasi final
        final_score = confidence_adjusted + posture_bonus + load_penalty
        
        # Clamp ke range 0-100
        return max(0.0, min(100.0, final_score))
    
    def _estimate_blink_from_behavior(self, behavior_data: Optional[Dict], timestamp: float):
        """
        Estimasi blink rate dari behavior patterns ketika EAR tidak tersedia
        
        Args:
            behavior_data: Data dari AdvancedBehaviorDetector
            timestamp: Timestamp saat ini
        """
        if not behavior_data:
            return
        
        # Estimasi blink berdasarkan aktivitas dan cognitive load
        activity = behavior_data.get('activity', {})
        dominant_activity = activity.get('dominant', 'neutral')
        
        cognitive_load = behavior_data.get('cognitive_load', {})
        load_level = cognitive_load.get('overall_load', 'normal')
        
        # Estimasi blink rate berdasarkan aktivitas
        activity_blink_rates = {
            'writing': 14.0,     # Fokus, blink rate normal-rendah
            'typing': 16.0,      # Fokus pada layar, blink rate sedikit tinggi
            'neutral': 15.0,     # Normal
            'drinking': 12.0,    # Sementara tidak blink saat minum
            'phone_use': 18.0,   # Menatap layar, blink rate tinggi
            'smoking': 13.0      # Relaks, blink rate rendah
        }
        
        base_blink_rate = activity_blink_rates.get(dominant_activity, 15.0)
        
        # Adjust berdasarkan cognitive load
        load_multipliers = {
            'low': 0.9,      # Relaks, blink rate sedikit rendah
            'normal': 1.0,   # Normal
            'high': 1.2,     # Stress, blink rate tinggi
            'very_high': 1.4 # Sangat stress, blink rate sangat tinggi
        }
        
        multiplier = load_multipliers.get(load_level, 1.0)
        estimated_blink_rate = base_blink_rate * multiplier
        
        # Simulasi blink events berdasarkan estimated rate
        # Konversi dari per-menit ke probabilitas per frame (asumsi 30 FPS)
        blink_probability_per_frame = estimated_blink_rate / (60.0 * 30.0)
        
        # Simulasi blink dengan probabilitas
        import random
        if random.random() < blink_probability_per_frame:
            # Simulasi blink event
            blink_data = {
                'timestamp': timestamp,
                'ear_value': 0.2,  # Simulated low EAR
                'estimated': True
            }
            self.blink_history.append(blink_data)
    
    def compute_focus_with_fallback(self, face_detected: bool = True,
                                   blink_rate: Optional[float] = None,
                                   head_variance: Optional[float] = None,
                                   expression_focus_score: Optional[float] = None,
                                   behavior_focus_impact: Optional[float] = None,
                                   behavior_data: Optional[Dict] = None,
                                   timestamp: Optional[float] = None) -> Dict[str, float]:
        """
        Menghitung skor fokus dengan dukungan fallback mechanism
        
        Args:
            face_detected: Apakah wajah terdeteksi
            blink_rate: Blink rate per menit
            head_variance: Head pose variance
            expression_focus_score: Skor fokus dari mikro ekspresi
            behavior_focus_impact: Dampak fokus dari behavior
            behavior_data: Data behavior untuk fallback
            timestamp: Timestamp referensi
            
        Returns:
            Dict dengan skor fokus dan metadata
        """
        if timestamp is None:
            timestamp = time.time()
        
        # Jika wajah terdeteksi, gunakan metode normal
        if face_detected:
            focus_score = self.compute_focus(
                blink_rate, head_variance, expression_focus_score,
                behavior_focus_impact, timestamp
            )
            
            return {
                'focus_score': focus_score,
                'method': 'face_based',
                'confidence': 1.0,
                'fallback_used': False,
                'components': {
                    'blink_score': self._score_blink_rate(blink_rate or self.calculate_blink_rate(timestamp)),
                    'head_score': self._score_head_variance(head_variance or self.calculate_head_pose_variance(timestamp)['total_var']),
                    'expression_score': expression_focus_score or self._get_recent_expression_score(timestamp),
                    'behavior_impact': behavior_focus_impact or self._get_recent_behavior_impact(timestamp)
                }
            }
        else:
            # Fallback: gunakan behavior-based scoring
            behavior_focus_score = self._estimate_focus_from_behavior(behavior_data)
            
            # Ambil historical data untuk stabilitas
            recent_scores = self._get_recent_focus_scores(timestamp, window_sec=30)
            
            if recent_scores:
                # Weighted average: 70% behavior estimation + 30% historical trend
                historical_avg = np.mean(recent_scores)
                final_score = (behavior_focus_score * 0.7) + (historical_avg * 0.3)
                confidence = 0.6  # Lower confidence untuk fallback
            else:
                final_score = behavior_focus_score
                confidence = 0.4  # Very low confidence tanpa historical data
            
            # Clamp score
            final_score = max(0.0, min(100.0, final_score))
            
            # Update last focus score
            self.last_focus_score = final_score
            self.total_calculations += 1
            
            # Publish event jika enabled
            if self.enable_events:
                self._publish_focus_metric_event(
                    final_score, 0.0, 0.0, behavior_focus_score,
                    behavior_focus_impact or 0.0, timestamp
                )
            
            return {
                'focus_score': final_score,
                'method': 'behavior_based',
                'confidence': confidence,
                'fallback_used': True,
                'components': {
                    'behavior_score': behavior_focus_score,
                    'historical_influence': np.mean(recent_scores) if recent_scores else 50.0,
                    'behavior_impact': behavior_focus_impact or 0.0
                }
            }
    
    def _get_recent_focus_scores(self, timestamp: float, window_sec: int = 30) -> List[float]:
        """
        Mendapatkan skor fokus dari history dalam window waktu tertentu
        
        Args:
            timestamp: Timestamp referensi
            window_sec: Window waktu dalam detik
            
        Returns:
            List skor fokus terbaru
        """
        # Ambil data head pose yang mengandung focus scores
        recent_data = [
            data for data in self.head_pose_history
            if timestamp - data['timestamp'] <= window_sec
        ]
        
        # Extract focus scores (jika ada)
        focus_scores = []
        for data in recent_data:
            expr_score = data.get('expression_focus_score', 0)
            if expr_score > 0:
                focus_scores.append(expr_score)
        
        return focus_scores
    
    def get_statistics(self) -> Dict[str, any]:
        """
        Mendapatkan statistik calculator
        
        Returns:
            Dict: Statistik lengkap
        """
        # Hitung fallback usage statistics
        total_data_points = len(self.head_pose_history)
        fallback_points = len([
            data for data in self.head_pose_history
            if data.get('is_fallback', False)
        ])
        
        fallback_percentage = (fallback_points / total_data_points * 100) if total_data_points > 0 else 0.0
        
        return {
            'total_calculations': self.total_calculations,
            'total_blinks_detected': len(self.blink_history),
            'head_pose_data_points': len(self.head_pose_history),
            'last_focus_score': self.last_focus_score,
            'blink_window_sec': self.blink_window_sec,
            'head_var_window': self.head_var_window,
            'normal_blink_range': self.normal_blink_range,
            'fallback_usage_percentage': fallback_percentage,
            'continuous_monitoring_enabled': True
        }


def compute_focus(blink_rate: float, head_variance: float) -> float:
    """
    Fungsi utility untuk menghitung skor fokus sederhana
    Untuk penggunaan standalone tanpa class
    
    Args:
        blink_rate: Blink rate per menit
        head_variance: Head pose variance
        
    Returns:
        float: Skor fokus 0-100
    """
    # Buat instance temporary calculator
    calculator = FocusMetricCalculator()
    return calculator.compute_focus(blink_rate, head_variance)