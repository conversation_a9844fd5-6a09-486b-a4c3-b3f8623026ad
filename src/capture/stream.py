"""
WebcamStream - Kelas untuk menangani streaming webcam dengan threading
Menyediakan interface untuk start/stop capture video real-time

(c) 2025 Radhi<PERSON>a Guntoro Adhi
"""

import threading
import time
from typing import Optional, Tuple
import cv2
import numpy as np

class WebcamStream:
    """
    Kelas untuk menangani streaming webcam dengan threading
    Memungkinkan capture video non-blocking untuk performa optimal
    """
    
    def __init__(self, src: int = 0, resolution: Tuple[int, int] = (640, 480)):
        """
        Inisialisasi WebcamStream
        
        Args:
            src: Index kamera (default: 0 untuk kamera utama)
            resolution: Resolusi video (width, height)
        """
        self.src = src
        self.resolution = resolution
        self.cap: Optional[cv2.VideoCapture] = None
        self.frame: Optional[np.ndarray] = None
        self.is_running = False
        self.thread: Optional[threading.Thread] = None
        self.lock = threading.Lock()
        
        # Statistik performa
        self.fps_counter = 0
        self.fps_start_time = time.time()
        self.current_fps = 0.0
        
    def start(self) -> bool:
        """
        Memulai streaming webcam
        
        Returns:
            bool: True jika berhasil, False jika gagal
        """
        if self.is_running:
            print("⚠️ WebcamStream sudah berjalan")
            return True
            
        # Inisialisasi kamera
        self.cap = cv2.VideoCapture(self.src)
        if not self.cap.isOpened():
            print(f"❌ Gagal membuka kamera dengan index {self.src}")
            return False
            
        # Set resolusi
        self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, self.resolution[0])
        self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, self.resolution[1])
        self.cap.set(cv2.CAP_PROP_FPS, 30)  # Target 30 FPS
        
        # Baca frame pertama untuk validasi
        ret, frame = self.cap.read()
        if not ret:
            print("❌ Gagal membaca frame dari kamera")
            self.cap.release()
            return False
            
        with self.lock:
            self.frame = frame
            self.is_running = True
            
        # Mulai thread untuk capture
        self.thread = threading.Thread(target=self._capture_loop, daemon=True)
        self.thread.start()
        
        print(f"✅ WebcamStream dimulai dengan resolusi {self.resolution}")
        return True
        
    def stop(self):
        """
        Menghentikan streaming webcam
        """
        if not self.is_running:
            print("⚠️ WebcamStream sudah berhenti")
            return
            
        self.is_running = False
        
        # Tunggu thread selesai
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=2.0)
            
        # Release kamera
        if self.cap:
            self.cap.release()
            self.cap = None
            
        print("🛑 WebcamStream dihentikan")
        
    def get_frame(self) -> Optional[np.ndarray]:
        """
        Mendapatkan frame terbaru dari webcam
        
        Returns:
            np.ndarray: Frame terbaru atau None jika tidak ada
        """
        with self.lock:
            if self.frame is not None:
                return self.frame.copy()
            return None
            
    def get_fps(self) -> float:
        """
        Mendapatkan FPS saat ini
        
        Returns:
            float: FPS saat ini
        """
        return self.current_fps
        
    def is_active(self) -> bool:
        """
        Mengecek apakah stream masih aktif
        
        Returns:
            bool: True jika aktif, False jika tidak
        """
        return self.is_running
        
    def _capture_loop(self):
        """
        Loop utama untuk capture frame (dijalankan di thread terpisah)
        """
        print("🎥 Memulai capture loop...")
        
        while self.is_running:
            if not self.cap or not self.cap.isOpened():
                print("❌ Kamera tidak tersedia, menghentikan capture")
                break
                
            ret, frame = self.cap.read()
            if not ret:
                print("⚠️ Gagal membaca frame, mencoba lagi...")
                time.sleep(0.1)
                continue
                
            # Update frame dengan thread safety
            with self.lock:
                self.frame = frame
                
            # Update FPS counter
            self._update_fps()
            
            # Kontrol frame rate (target ~30 FPS)
            time.sleep(0.033)  # ~30ms delay
            
        print("🏁 Capture loop selesai")
        
    def _update_fps(self):
        """
        Update perhitungan FPS
        """
        self.fps_counter += 1
        current_time = time.time()
        
        # Hitung FPS setiap detik
        if current_time - self.fps_start_time >= 1.0:
            self.current_fps = self.fps_counter / (current_time - self.fps_start_time)
            self.fps_counter = 0
            self.fps_start_time = current_time
            
    def __del__(self):
        """
        Destructor untuk memastikan resource dibersihkan
        """
        self.stop()


# TODO: Implementasi fitur-fitur berikut di versi mendatang:
# - Frame buffer circular untuk menyimpan beberapa frame terakhir
# - Auto-detection resolusi optimal berdasarkan hardware
# - Support multiple camera sources
# - Frame preprocessing (resize, color correction, noise reduction)
# - Recording capability untuk debugging
# - Error recovery mechanism yang lebih robust
# - Konfigurasi codec dan compression settings
# - Integration dengan MediaPipe untuk real-time processing