"""
Generator laporan harian untuk ICikiwir
<PERSON><PERSON><PERSON><PERSON>an laporan markdown dengan grafik dan metrik fokus

(c) 2025 Radhitya Guntoro Adhi
"""

import os
import base64
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional
from io import BytesIO

import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from jinja2 import Template

from ..config import config


def ensure_report_dir() -> Path:
    """
    Memastikan direktori laporan tersedia
    
    Returns:
        Path: Path ke direktori laporan
    """
    report_dir = Path("data/reports")
    report_dir.mkdir(parents=True, exist_ok=True)
    return report_dir


def generate_focus_chart(metrics_df: pd.DataFrame) -> str:
    """
    Menghasilkan grafik line chart untuk skor fokus
    
    Args:
        metrics_df: DataFrame dengan kolom timestamp dan focus_score
        
    Returns:
        str: Base64 encoded PNG image
    """
    plt.figure(figsize=(12, 6))
    
    # Konversi timestamp ke datetime jika belum
    if 'timestamp' in metrics_df.columns:
        metrics_df['datetime'] = pd.to_datetime(metrics_df['timestamp'])
    else:
        metrics_df['datetime'] = pd.to_datetime(metrics_df.index)
    
    # Plot line chart
    plt.plot(metrics_df['datetime'], metrics_df['focus_score'], 
             linewidth=2, color='#2196F3', marker='o', markersize=4)
    
    # Formatting
    plt.title('Skor Fokus Sepanjang Hari', fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('Waktu', fontsize=12)
    plt.ylabel('Skor Fokus (%)', fontsize=12)
    plt.grid(True, alpha=0.3)
    
    # Format x-axis
    plt.gca().xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
    plt.gca().xaxis.set_major_locator(mdates.HourLocator(interval=2))
    plt.xticks(rotation=45)
    
    # Set y-axis limits
    plt.ylim(0, 100)
    
    # Add threshold lines
    plt.axhline(y=80, color='green', linestyle='--', alpha=0.7, label='Fokus Tinggi')
    plt.axhline(y=60, color='orange', linestyle='--', alpha=0.7, label='Fokus Sedang')
    plt.axhline(y=40, color='red', linestyle='--', alpha=0.7, label='Fokus Rendah')
    
    plt.legend()
    plt.tight_layout()
    
    # Convert to base64
    buffer = BytesIO()
    plt.savefig(buffer, format='png', dpi=150, bbox_inches='tight')
    buffer.seek(0)
    image_base64 = base64.b64encode(buffer.getvalue()).decode()
    plt.close()
    
    return image_base64


def generate_blink_chart(metrics_df: pd.DataFrame) -> str:
    """
    Menghasilkan bar chart untuk blink rate per jam
    
    Args:
        metrics_df: DataFrame dengan kolom timestamp dan blink_rate
        
    Returns:
        str: Base64 encoded PNG image
    """
    plt.figure(figsize=(12, 6))
    
    # Konversi timestamp dan group by hour
    if 'timestamp' in metrics_df.columns:
        metrics_df['datetime'] = pd.to_datetime(metrics_df['timestamp'])
    else:
        metrics_df['datetime'] = pd.to_datetime(metrics_df.index)
    
    # Group by hour dan hitung rata-rata blink rate
    hourly_blink = metrics_df.groupby(metrics_df['datetime'].dt.hour)['blink_rate'].mean()
    
    # Create bar chart
    bars = plt.bar(hourly_blink.index, hourly_blink.values, 
                   color='#4CAF50', alpha=0.8, edgecolor='darkgreen')
    
    # Formatting
    plt.title('Rata-rata Blink Rate per Jam', fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('Jam', fontsize=12)
    plt.ylabel('Blink Rate (per menit)', fontsize=12)
    plt.grid(True, alpha=0.3, axis='y')
    
    # Add normal range indicators
    plt.axhline(y=12, color='orange', linestyle='--', alpha=0.7, label='Batas Bawah Normal')
    plt.axhline(y=20, color='orange', linestyle='--', alpha=0.7, label='Batas Atas Normal')
    
    # Add value labels on bars
    for bar in bars:
        height = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                f'{height:.1f}', ha='center', va='bottom', fontsize=10)
    
    plt.legend()
    plt.tight_layout()
    
    # Convert to base64
    buffer = BytesIO()
    plt.savefig(buffer, format='png', dpi=150, bbox_inches='tight')
    buffer.seek(0)
    image_base64 = base64.b64encode(buffer.getvalue()).decode()
    plt.close()
    
    return image_base64


def generate_daily_report(date: datetime, metrics_df: pd.DataFrame, output_dir: Optional[Path] = None) -> Path:
    """
    Menghasilkan laporan harian dalam format Markdown
    
    Args:
        date: Tanggal laporan
        metrics_df: DataFrame dengan metrik fokus
        output_dir: Direktori output (opsional, default: data/reports)
        
    Returns:
        Path: Path ke file laporan yang dibuat
    """
    try:
        # Pastikan direktori laporan ada
        if output_dir:
            report_dir = Path(output_dir)
            report_dir.mkdir(parents=True, exist_ok=True)
        else:
            report_dir = ensure_report_dir()
        
        # Nama file laporan
        report_filename = f"{date.strftime('%Y-%m-%d')}.md"
        report_path = report_dir / report_filename
        
        # Hitung statistik dasar
        if metrics_df.empty:
            print(f"⚠️ Tidak ada data untuk tanggal {date.strftime('%Y-%m-%d')}")
            return False
        
        total_sessions = len(metrics_df)
        avg_focus = metrics_df['focus_score'].mean()
        blink_rate_mean = metrics_df['blink_rate'].mean()
        max_focus = metrics_df['focus_score'].max()
        min_focus = metrics_df['focus_score'].min()
        
        # Generate charts
        focus_chart_b64 = generate_focus_chart(metrics_df)
        blink_chart_b64 = generate_blink_chart(metrics_df)
        
        # Template Jinja2 untuk laporan
        template_content = """---
date: {{ date }}
total_session: {{ total_session }}
avg_focus: {{ avg_focus_str }}
blink_rate_mean: {{ blink_rate_mean_str }}
---

# Laporan Harian ICikiwir

**Tanggal:** {{ date_formatted }}
**Total Sesi:** {{ total_session }}
**Rata-rata Fokus:** {{ avg_focus_str }}%
**Rata-rata Blink Rate:** {{ blink_rate_mean_str }} per menit

## 📊 Ringkasan Metrik

| Metrik | Nilai |
|--------|-------|
| Skor Fokus Tertinggi | {{ max_focus }}% |
| Skor Fokus Terendah | {{ min_focus }}% |
| Rata-rata Skor Fokus | {{ avg_focus_str }}% |
| Rata-rata Blink Rate | {{ blink_rate_mean_str }} per menit |
| Total Sesi Monitoring | {{ total_session }} |

## 📈 Grafik Skor Fokus

![Grafik Skor Fokus](data:image/png;base64,{{ focus_chart }})

## 📊 Grafik Blink Rate

![Grafik Blink Rate](data:image/png;base64,{{ blink_chart }})

## 🎯 Analisis

{% if avg_focus >= 80 %}
**Performa Excellent!** 🎉
Skor fokus rata-rata Anda sangat baik ({{ avg_focus_str }}%). Pertahankan pola kerja ini!
{% elif avg_focus >= 60 %}
**Performa Baik** ✅
Skor fokus rata-rata Anda cukup baik ({{ avg_focus_str }}%). Ada ruang untuk peningkatan.
{% elif avg_focus >= 40 %}
**Performa Sedang** ⚠️
Skor fokus rata-rata Anda sedang ({{ avg_focus_str }}%). Pertimbangkan untuk mengatur lingkungan kerja yang lebih kondusif.
{% else %}
**Perlu Perbaikan** ❌
Skor fokus rata-rata Anda rendah ({{ avg_focus_str }}%). Disarankan untuk mengevaluasi faktor-faktor yang mengganggu konsentrasi.
{% endif %}

{% if blink_rate_mean < 12 %}
**Blink Rate Rendah** ⚠️
Blink rate Anda ({{ blink_rate_mean_str }} per menit) di bawah normal. Ini bisa menandakan mata kering atau terlalu fokus pada layar.
{% elif blink_rate_mean > 20 %}
**Blink Rate Tinggi** ⚠️
Blink rate Anda ({{ blink_rate_mean_str }} per menit) di atas normal. Ini bisa menandakan kelelahan mata atau stres.
{% else %}
**Blink Rate Normal** ✅
Blink rate Anda ({{ blink_rate_mean_str }} per menit) dalam rentang normal.
{% endif %}

## 💡 Rekomendasi

1. **Istirahat Mata**: Terapkan aturan 20-20-20 (setiap 20 menit, lihat objek sejauh 20 kaki selama 20 detik)
2. **Postur Tubuh**: Pastikan posisi duduk ergonomis dan layar sejajar dengan mata
3. **Pencahayaan**: Atur pencahayaan ruangan yang cukup dan hindari silau pada layar
4. **Hidrasi**: Minum air yang cukup untuk menjaga konsentrasi
5. **Break Reguler**: Ambil istirahat singkat setiap 45-60 menit

---

*Laporan ini dihasilkan secara otomatis oleh ICikiwir - Cognitive-Aware Study Companion*  
*(c) 2025 Radhitya Guntoro Adhi*
"""
        
        # Render template
        template = Template(template_content)
        report_content = template.render(
            date=date.strftime('%Y-%m-%d'),
            date_formatted=date.strftime('%d %B %Y'),
            total_session=total_sessions,
            avg_focus=avg_focus,  # Keep as float for comparison
            avg_focus_str=f"{avg_focus:.1f}",  # String version for display
            blink_rate_mean=blink_rate_mean,  # Keep as float for comparison
            blink_rate_mean_str=f"{blink_rate_mean:.1f}",  # String version for display
            max_focus=f"{max_focus:.1f}",
            min_focus=f"{min_focus:.1f}",
            focus_chart=focus_chart_b64,
            blink_chart=blink_chart_b64
        )
        
        # Tulis file laporan
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        print(f"✅ Laporan berhasil dibuat: {report_path}")
        return report_path
        
    except Exception as e:
        print(f"❌ Error membuat laporan: {e}")
        raise


if __name__ == "__main__":
    # Test dengan data dummy
    import numpy as np
    
    # Generate dummy data
    dates = pd.date_range(start='2025-06-22 09:00', end='2025-06-22 17:00', freq='5min')
    focus_scores = np.random.normal(70, 15, len(dates))
    focus_scores = np.clip(focus_scores, 0, 100)  # Clip to 0-100 range
    blink_rates = np.random.normal(16, 3, len(dates))
    blink_rates = np.clip(blink_rates, 5, 30)  # Clip to reasonable range
    
    dummy_df = pd.DataFrame({
        'timestamp': dates,
        'focus_score': focus_scores,
        'blink_rate': blink_rates
    })
    
    # Generate report
    test_date = datetime(2025, 6, 22)
    try:
        report_path = generate_daily_report(test_date, dummy_df)
        print(f"🎉 Test laporan berhasil! File: {report_path}")
    except Exception as e:
        print(f"❌ Test laporan gagal: {e}")