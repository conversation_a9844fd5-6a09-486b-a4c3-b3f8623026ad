"""
Micro Expression Detector menggunakan MediaPipe Face Mesh
Mendeteksi ekspresi wajah mikro untuk analisis fokus yang lebih akurat
Menganalisis landmark wajah untuk menentukan emosi dan tingkat konsentrasi

(c) 2025 <PERSON><PERSON><PERSON><PERSON>
"""

import cv2
import numpy as np
import mediapipe as mp
from typing import Dict, List, Optional, Tuple
import math

class MicroExpressionDetector:
    """
    Kelas untuk mendeteksi mikro ekspresi wajah menggunakan MediaPipe Face Mesh
    Menganalisis landmark untuk menentukan emosi dan tingkat fokus
    """
    
    def __init__(self):
        """
        Inisialisasi MicroExpressionDetector
        """
        # Landmark indices untuk berbagai bagian wajah
        self.MOUTH_LANDMARKS = [
            # Bibir luar
            61, 84, 17, 314, 405, 320, 307, 375, 321, 308, 324, 318,
            # <PERSON><PERSON><PERSON> dalam  
            78, 95, 88, 178, 87, 14, 317, 402, 318, 324
        ]
        
        self.EYEBROW_LEFT = [70, 63, 105, 66, 107, 55, 65, 52, 53, 46]
        self.EYEBROW_RIGHT = [296, 334, 293, 300, 276, 283, 282, 295, 285, 336]
        
        self.CHEEK_LEFT = [116, 117, 118, 119, 120, 121, 126, 142, 36, 205]
        self.CHEEK_RIGHT = [345, 346, 347, 348, 349, 350, 355, 371, 266, 425]
        
        self.FOREHEAD = [9, 10, 151, 337, 299, 333, 298, 301]
        
        # Baseline measurements untuk normalisasi
        self.baseline_measurements = None
        self.calibration_frames = 0
        self.max_calibration_frames = 30
        
        # History untuk smoothing
        self.expression_history = []
        self.history_size = 5
        
    def calibrate_baseline(self, landmarks_2d: np.ndarray) -> bool:
        """
        Kalibrasi baseline measurements untuk normalisasi
        
        Args:
            landmarks_2d: Array landmark 2D
            
        Returns:
            bool: True jika kalibrasi selesai
        """
        if self.calibration_frames >= self.max_calibration_frames:
            return True
            
        measurements = self._extract_measurements(landmarks_2d)
        
        if self.baseline_measurements is None:
            self.baseline_measurements = measurements.copy()
        else:
            # Running average
            alpha = 0.1
            for key in measurements:
                self.baseline_measurements[key] = (
                    (1 - alpha) * self.baseline_measurements[key] + 
                    alpha * measurements[key]
                )
        
        self.calibration_frames += 1
        return self.calibration_frames >= self.max_calibration_frames
    
    def _extract_measurements(self, landmarks_2d: np.ndarray) -> Dict[str, float]:
        """
        Ekstrak measurements dari landmark wajah
        
        Args:
            landmarks_2d: Array landmark 2D
            
        Returns:
            Dict: Measurements untuk berbagai bagian wajah
        """
        measurements = {}
        
        try:
            # Mouth measurements
            mouth_points = landmarks_2d[self.MOUTH_LANDMARKS]
            mouth_width = self._calculate_distance(mouth_points[0], mouth_points[6])
            mouth_height = self._calculate_distance(mouth_points[3], mouth_points[9])
            measurements['mouth_aspect_ratio'] = mouth_height / (mouth_width + 1e-6)
            measurements['mouth_width'] = mouth_width
            measurements['mouth_height'] = mouth_height
            
            # Eyebrow measurements
            left_brow = landmarks_2d[self.EYEBROW_LEFT]
            right_brow = landmarks_2d[self.EYEBROW_RIGHT]
            
            # Eyebrow height (distance from eye to eyebrow)
            left_eye_center = landmarks_2d[33]  # Left eye landmark
            right_eye_center = landmarks_2d[263]  # Right eye landmark
            
            left_brow_height = self._calculate_distance(left_eye_center, left_brow[4])
            right_brow_height = self._calculate_distance(right_eye_center, right_brow[4])
            
            measurements['left_eyebrow_height'] = left_brow_height
            measurements['right_eyebrow_height'] = right_brow_height
            measurements['eyebrow_symmetry'] = abs(left_brow_height - right_brow_height)
            
            # Cheek measurements (untuk smile detection)
            left_cheek = landmarks_2d[self.CHEEK_LEFT]
            right_cheek = landmarks_2d[self.CHEEK_RIGHT]
            
            # Cheek lift (smile indicator)
            nose_base = landmarks_2d[1]  # Nose base
            left_cheek_lift = nose_base[1] - left_cheek[0][1]  # Y difference
            right_cheek_lift = nose_base[1] - right_cheek[0][1]
            
            measurements['left_cheek_lift'] = left_cheek_lift
            measurements['right_cheek_lift'] = right_cheek_lift
            
            # Forehead tension
            forehead_points = landmarks_2d[self.FOREHEAD]
            forehead_variance = np.var([p[1] for p in forehead_points])  # Y variance
            measurements['forehead_tension'] = forehead_variance
            
        except Exception as e:
            print(f"⚠️ Error extracting measurements: {e}")
            # Return default measurements
            measurements = {
                'mouth_aspect_ratio': 0.0,
                'mouth_width': 0.0,
                'mouth_height': 0.0,
                'left_eyebrow_height': 0.0,
                'right_eyebrow_height': 0.0,
                'eyebrow_symmetry': 0.0,
                'left_cheek_lift': 0.0,
                'right_cheek_lift': 0.0,
                'forehead_tension': 0.0
            }
        
        return measurements
    
    def _calculate_distance(self, p1: np.ndarray, p2: np.ndarray) -> float:
        """
        Menghitung jarak euclidean antara dua titik
        
        Args:
            p1: Titik pertama [x, y]
            p2: Titik kedua [x, y]
            
        Returns:
            float: Jarak euclidean
        """
        return np.sqrt((p1[0] - p2[0])**2 + (p1[1] - p2[1])**2)
    
    def detect_expressions(self, landmarks_2d: np.ndarray) -> Dict[str, float]:
        """
        Mendeteksi mikro ekspresi dari landmark wajah
        
        Args:
            landmarks_2d: Array landmark 2D
            
        Returns:
            Dict: Skor ekspresi untuk berbagai emosi (0-1)
        """
        if self.baseline_measurements is None:
            # Belum dikalibrasi, return neutral
            return {
                'neutral': 1.0,
                'happy': 0.0,
                'sad': 0.0,
                'surprised': 0.0,
                'focused': 0.5,
                'confused': 0.0,
                'tired': 0.0
            }
        
        current_measurements = self._extract_measurements(landmarks_2d)
        
        # Normalisasi terhadap baseline
        normalized = {}
        for key in current_measurements:
            baseline_val = self.baseline_measurements.get(key, 1.0)
            if baseline_val != 0:
                normalized[key] = current_measurements[key] / baseline_val
            else:
                normalized[key] = 1.0
        
        # Deteksi ekspresi berdasarkan normalized measurements
        expressions = self._classify_expressions(normalized)
        
        # Smoothing dengan history
        self.expression_history.append(expressions)
        if len(self.expression_history) > self.history_size:
            self.expression_history.pop(0)
        
        # Average dengan history untuk smoothing
        smoothed_expressions = {}
        for emotion in expressions:
            values = [exp[emotion] for exp in self.expression_history]
            smoothed_expressions[emotion] = np.mean(values)
        
        return smoothed_expressions
    
    def _classify_expressions(self, normalized: Dict[str, float]) -> Dict[str, float]:
        """
        Klasifikasi ekspresi berdasarkan normalized measurements
        
        Args:
            normalized: Normalized measurements
            
        Returns:
            Dict: Skor ekspresi untuk berbagai emosi
        """
        expressions = {
            'neutral': 0.0,
            'happy': 0.0,
            'sad': 0.0,
            'surprised': 0.0,
            'focused': 0.0,
            'confused': 0.0,
            'tired': 0.0
        }
        
        try:
            # Happy detection (smile)
            mouth_ratio = normalized.get('mouth_aspect_ratio', 1.0)
            cheek_lift_avg = (normalized.get('left_cheek_lift', 1.0) + 
                             normalized.get('right_cheek_lift', 1.0)) / 2.0
            
            if mouth_ratio > 1.2 and cheek_lift_avg > 1.1:
                expressions['happy'] = min(1.0, (mouth_ratio - 1.0) * 0.5 + (cheek_lift_avg - 1.0) * 0.3)
            
            # Sad detection (mouth down, eyebrows down)
            if mouth_ratio < 0.8:
                brow_height_avg = (normalized.get('left_eyebrow_height', 1.0) + 
                                  normalized.get('right_eyebrow_height', 1.0)) / 2.0
                if brow_height_avg < 0.9:
                    expressions['sad'] = min(1.0, (1.0 - mouth_ratio) * 0.4 + (1.0 - brow_height_avg) * 0.3)
            
            # Surprised detection (eyebrows up, mouth open)
            brow_height_avg = (normalized.get('left_eyebrow_height', 1.0) + 
                              normalized.get('right_eyebrow_height', 1.0)) / 2.0
            mouth_height = normalized.get('mouth_height', 1.0)
            
            if brow_height_avg > 1.15 and mouth_height > 1.2:
                expressions['surprised'] = min(1.0, (brow_height_avg - 1.0) * 0.4 + (mouth_height - 1.0) * 0.3)
            
            # Focused detection (slight eyebrow tension, neutral mouth, low variance)
            forehead_tension = normalized.get('forehead_tension', 1.0)
            eyebrow_symmetry = normalized.get('eyebrow_symmetry', 1.0)
            
            # Fokus ditandai dengan:
            # - Sedikit tension di dahi (konsentrasi)
            # - Eyebrow simetris (tidak bingung)
            # - Mulut netral
            focus_score = 0.0
            if 1.0 <= forehead_tension <= 1.3:  # Slight tension
                focus_score += 0.3
            if eyebrow_symmetry < 1.2:  # Symmetric eyebrows
                focus_score += 0.3
            if 0.9 <= mouth_ratio <= 1.1:  # Neutral mouth
                focus_score += 0.4
            
            expressions['focused'] = min(1.0, focus_score)
            
            # Confused detection (asymmetric eyebrows, mouth tension)
            if eyebrow_symmetry > 1.3:
                expressions['confused'] = min(1.0, (eyebrow_symmetry - 1.0) * 0.4)
            
            # Tired detection (low eyebrows, droopy features)
            if brow_height_avg < 0.85:
                tired_score = (1.0 - brow_height_avg) * 0.5
                if mouth_ratio < 0.9:  # Droopy mouth
                    tired_score += (1.0 - mouth_ratio) * 0.3
                expressions['tired'] = min(1.0, tired_score)
            
            # Neutral adalah default jika tidak ada ekspresi kuat lainnya
            total_expression = sum(expressions.values())
            if total_expression < 0.3:
                expressions['neutral'] = 1.0 - total_expression
            else:
                expressions['neutral'] = max(0.0, 1.0 - total_expression)
            
        except Exception as e:
            print(f"⚠️ Error classifying expressions: {e}")
            expressions['neutral'] = 1.0
        
        return expressions
    
    def get_focus_contribution(self, expressions: Dict[str, float]) -> float:
        """
        Menghitung kontribusi ekspresi terhadap skor fokus
        
        Args:
            expressions: Dictionary skor ekspresi
            
        Returns:
            float: Kontribusi fokus (0-100)
        """
        # Mapping ekspresi ke kontribusi fokus
        focus_weights = {
            'focused': 1.0,      # Sangat positif untuk fokus
            'neutral': 0.7,      # Cukup baik untuk fokus
            'happy': 0.5,        # Bisa fokus tapi tidak optimal
            'surprised': 0.3,    # Terganggu, kurang fokus
            'confused': 0.1,     # Sangat mengganggu fokus
            'sad': 0.2,          # Emosi negatif mengganggu fokus
            'tired': 0.0         # Sangat buruk untuk fokus
        }
        
        # Weighted sum berdasarkan intensitas ekspresi
        focus_contribution = 0.0
        total_weight = 0.0
        
        for emotion, intensity in expressions.items():
            if emotion in focus_weights:
                weight = focus_weights[emotion] * intensity
                focus_contribution += weight
                total_weight += intensity
        
        # Normalisasi ke 0-100
        if total_weight > 0:
            focus_score = (focus_contribution / total_weight) * 100.0
        else:
            focus_score = 50.0  # Default neutral
        
        return max(0.0, min(100.0, focus_score))
    
    def get_dominant_expression(self, expressions: Dict[str, float]) -> Tuple[str, float]:
        """
        Mendapatkan ekspresi dominan
        
        Args:
            expressions: Dictionary skor ekspresi
            
        Returns:
            Tuple: (nama_ekspresi, intensitas)
        """
        if not expressions:
            return ('neutral', 1.0)
        
        dominant_emotion = max(expressions.items(), key=lambda x: x[1])
        return dominant_emotion
    
    def reset_calibration(self):
        """
        Reset kalibrasi baseline
        """
        self.baseline_measurements = None
        self.calibration_frames = 0
        self.expression_history.clear()
        print("🔄 Kalibrasi mikro ekspresi direset")
    
    def is_calibrated(self) -> bool:
        """
        Cek apakah detector sudah dikalibrasi
        
        Returns:
            bool: True jika sudah dikalibrasi
        """
        return self.baseline_measurements is not None
    
    def get_calibration_progress(self) -> float:
        """
        Mendapatkan progress kalibrasi
        
        Returns:
            float: Progress 0-1
        """
        return min(1.0, self.calibration_frames / self.max_calibration_frames)