"""
Advanced Behavior Detector untuk ICikiwir
Mendeteksi berbagai aktivitas dan perilaku pengguna untuk analisis fokus yang lebih komprehensif

<PERSON>:
- Deteksi aktivitas menulis/mengetik
- Deteksi minum air
- Deteksi merokok
- Deteksi beban kognitif
- Detek<PERSON> postur tubuh (menunduk, tegak)
- Analisis gerakan tangan dan kepala

(c) 2025 Radhitya Guntoro Adhi
"""

import cv2
import numpy as np
import mediapipe as mp
from typing import Dict, List, Optional, Tuple
import time
from collections import deque
import math

class AdvancedBehaviorDetector:
    """
    Detector untuk berbagai perilaku dan aktivitas pengguna
    """
    
    def __init__(self):
        """
        Inisialisasi Advanced Behavior Detector
        """
        # MediaPipe components
        self.mp_hands = mp.solutions.hands
        self.mp_pose = mp.solutions.pose
        self.mp_face_mesh = mp.solutions.face_mesh
        
        # Initialize detectors
        self.hands = self.mp_hands.Hands(
            static_image_mode=False,
            max_num_hands=2,
            min_detection_confidence=0.7,
            min_tracking_confidence=0.5
        )
        
        self.pose = self.mp_pose.Pose(
            static_image_mode=False,
            min_detection_confidence=0.7,
            min_tracking_confidence=0.5
        )
        
        # History buffers untuk analisis temporal
        self.hand_history = deque(maxlen=30)  # 1 detik pada 30 FPS
        self.head_position_history = deque(maxlen=60)  # 2 detik
        self.mouth_activity_history = deque(maxlen=45)  # 1.5 detik
        self.cognitive_load_history = deque(maxlen=90)  # 3 detik
        
        # Thresholds dan parameters
        self.writing_motion_threshold = 0.02
        self.drinking_mouth_threshold = 0.015
        self.smoking_hand_mouth_distance = 0.1
        self.head_down_angle_threshold = 25  # derajat
        
        # State tracking
        self.current_activity = "neutral"
        self.activity_confidence = 0.0
        self.posture_state = "normal"
        self.cognitive_load_level = "normal"
        
        # Calibration data
        self.baseline_hand_position = None
        self.baseline_head_position = None
        self.is_calibrated = False
        
    def process_frame(self, frame: np.ndarray) -> Dict:
        """
        Memproses frame untuk deteksi perilaku
        
        Args:
            frame: Frame input dari kamera
            
        Returns:
            Dict dengan hasil deteksi perilaku
        """
        rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        h, w = frame.shape[:2]
        
        # Deteksi hands dan pose
        hands_results = self.hands.process(rgb_frame)
        pose_results = self.pose.process(rgb_frame)
        
        # Analisis berbagai perilaku
        behavior_analysis = {
            'timestamp': time.time(),
            'activity': self._detect_activity(hands_results, pose_results, w, h),
            'posture': self._analyze_posture(pose_results, w, h),
            'cognitive_load': self._assess_cognitive_load(hands_results, pose_results),
            'hand_activity': self._analyze_hand_activity(hands_results, w, h),
            'head_position': self._analyze_head_position(pose_results, w, h),
            'focus_impact': 0.0
        }
        
        # Hitung dampak terhadap fokus
        behavior_analysis['focus_impact'] = self._calculate_focus_impact(behavior_analysis)
        
        # Update histories
        self._update_histories(behavior_analysis)
        
        # Kalibrasi otomatis jika belum dikalibrasi
        if not self.is_calibrated:
            self._auto_calibrate(behavior_analysis)
        
        return behavior_analysis
    
    def _detect_activity(self, hands_results, pose_results, w: int, h: int) -> Dict:
        """
        Mendeteksi aktivitas utama pengguna
        """
        activities = {
            'writing': 0.0,
            'typing': 0.0,
            'drinking': 0.0,
            'smoking': 0.0,
            'phone_use': 0.0,
            'neutral': 0.0
        }
        
        if hands_results.multi_hand_landmarks and pose_results.pose_landmarks:
            # Deteksi menulis/mengetik
            writing_score = self._detect_writing_typing(hands_results, pose_results, w, h)
            activities['writing'] = writing_score['writing']
            activities['typing'] = writing_score['typing']
            
            # Deteksi minum
            activities['drinking'] = self._detect_drinking(hands_results, pose_results, w, h)
            
            # Deteksi merokok
            activities['smoking'] = self._detect_smoking(hands_results, pose_results, w, h)
            
            # Deteksi penggunaan ponsel
            activities['phone_use'] = self._detect_phone_use(hands_results, pose_results, w, h)
        
        # Tentukan aktivitas dominan
        dominant_activity = max(activities, key=activities.get)
        confidence = activities[dominant_activity]
        
        if confidence < 0.3:
            dominant_activity = 'neutral'
            confidence = 1.0 - max(activities.values())
        
        return {
            'dominant': dominant_activity,
            'confidence': confidence,
            'scores': activities
        }
    
    def _detect_writing_typing(self, hands_results, pose_results, w: int, h: int) -> Dict:
        """
        Mendeteksi aktivitas menulis atau mengetik
        """
        writing_score = 0.0
        typing_score = 0.0
        
        if hands_results.multi_hand_landmarks:
            hand_landmarks = hands_results.multi_hand_landmarks[0]
            
            # Analisis posisi tangan
            wrist = hand_landmarks.landmark[self.mp_hands.HandLandmark.WRIST]
            index_tip = hand_landmarks.landmark[self.mp_hands.HandLandmark.INDEX_FINGER_TIP]
            thumb_tip = hand_landmarks.landmark[self.mp_hands.HandLandmark.THUMB_TIP]
            
            # Konversi ke koordinat pixel
            wrist_pos = np.array([wrist.x * w, wrist.y * h])
            index_pos = np.array([index_tip.x * w, index_tip.y * h])
            thumb_pos = np.array([thumb_tip.x * w, thumb_tip.y * h])
            
            # Deteksi gerakan menulis (gerakan halus, konsisten)
            if len(self.hand_history) > 10:
                recent_positions = list(self.hand_history)[-10:]
                motion_variance = np.var([pos['wrist'] for pos in recent_positions], axis=0)
                motion_smoothness = 1.0 / (1.0 + np.sum(motion_variance))
                
                # Cek apakah tangan dalam posisi menulis
                finger_distance = np.linalg.norm(index_pos - thumb_pos)
                writing_grip = 1.0 - min(finger_distance / 50.0, 1.0)  # Normalized grip strength
                
                writing_score = motion_smoothness * writing_grip * 0.8
            
            # Deteksi mengetik (gerakan lebih cepat, vertikal)
            if len(self.hand_history) > 5:
                recent_positions = list(self.hand_history)[-5:]
                vertical_motion = np.var([pos['index_tip'][1] for pos in recent_positions])
                horizontal_stability = 1.0 - np.var([pos['index_tip'][0] for pos in recent_positions]) / 100.0
                
                typing_score = min(vertical_motion / 20.0, 1.0) * max(horizontal_stability, 0.0)
            
            # Store current hand position
            current_hand_data = {
                'wrist': wrist_pos,
                'index_tip': index_pos,
                'thumb_tip': thumb_pos,
                'timestamp': time.time()
            }
            self.hand_history.append(current_hand_data)
        
        return {
            'writing': min(writing_score, 1.0),
            'typing': min(typing_score, 1.0)
        }
    
    def _detect_drinking(self, hands_results, pose_results, w: int, h: int) -> float:
        """
        Mendeteksi aktivitas minum
        """
        drinking_score = 0.0
        
        if hands_results.multi_hand_landmarks and pose_results.pose_landmarks:
            hand_landmarks = hands_results.multi_hand_landmarks[0]
            pose_landmarks = pose_results.pose_landmarks
            
            # Posisi tangan dan mulut
            wrist = hand_landmarks.landmark[self.mp_hands.HandLandmark.WRIST]
            mouth = pose_landmarks.landmark[self.mp_pose.PoseLandmark.NOSE]  # Approximation
            
            wrist_pos = np.array([wrist.x * w, wrist.y * h])
            mouth_pos = np.array([mouth.x * w, mouth.y * h])
            
            # Jarak tangan ke mulut
            hand_mouth_distance = np.linalg.norm(wrist_pos - mouth_pos)
            
            # Skor berdasarkan kedekatan tangan ke mulut
            if hand_mouth_distance < 100:  # pixels
                proximity_score = 1.0 - (hand_mouth_distance / 100.0)
                
                # Cek apakah tangan dalam posisi memegang gelas
                fingers = [
                    hand_landmarks.landmark[self.mp_hands.HandLandmark.THUMB_TIP],
                    hand_landmarks.landmark[self.mp_hands.HandLandmark.INDEX_FINGER_TIP],
                    hand_landmarks.landmark[self.mp_hands.HandLandmark.MIDDLE_FINGER_TIP],
                    hand_landmarks.landmark[self.mp_hands.HandLandmark.RING_FINGER_TIP],
                    hand_landmarks.landmark[self.mp_hands.HandLandmark.PINKY_TIP]
                ]
                
                # Analisis postur jari (grip pattern)
                finger_positions = np.array([[f.x * w, f.y * h] for f in fingers])
                grip_compactness = 1.0 - (np.std(finger_positions) / 50.0)
                
                drinking_score = proximity_score * max(grip_compactness, 0.0)
        
        return min(drinking_score, 1.0)
    
    def _detect_smoking(self, hands_results, pose_results, w: int, h: int) -> float:
        """
        Mendeteksi aktivitas merokok dengan algoritma yang lebih sensitif dan akurat
        """
        smoking_score = 0.0
        
        if hands_results.multi_hand_landmarks and pose_results.pose_landmarks:
            hand_landmarks = hands_results.multi_hand_landmarks[0]
            pose_landmarks = pose_results.pose_landmarks
            
            # Posisi landmark tangan yang relevan
            index_tip = hand_landmarks.landmark[self.mp_hands.HandLandmark.INDEX_FINGER_TIP]
            middle_tip = hand_landmarks.landmark[self.mp_hands.HandLandmark.MIDDLE_FINGER_TIP]
            thumb_tip = hand_landmarks.landmark[self.mp_hands.HandLandmark.THUMB_TIP]
            wrist = hand_landmarks.landmark[self.mp_hands.HandLandmark.WRIST]
            
            # Posisi mulut yang lebih akurat (gunakan mouth landmarks dari pose)
            mouth_left = pose_landmarks.landmark[self.mp_pose.PoseLandmark.MOUTH_LEFT]
            mouth_right = pose_landmarks.landmark[self.mp_pose.PoseLandmark.MOUTH_RIGHT]
            
            # Konversi ke koordinat pixel
            index_pos = np.array([index_tip.x * w, index_tip.y * h])
            middle_pos = np.array([middle_tip.x * w, middle_tip.y * h])
            thumb_pos = np.array([thumb_tip.x * w, thumb_tip.y * h])
            wrist_pos = np.array([wrist.x * w, wrist.y * h])
            
            # Posisi mulut yang lebih akurat
            mouth_center = np.array([
                (mouth_left.x + mouth_right.x) / 2 * w,
                (mouth_left.y + mouth_right.y) / 2 * h
            ])
            
            # 1. Analisis postur jari untuk memegang rokok
            # Jarak antara telunjuk dan jempol (grip rokok)
            index_thumb_distance = np.linalg.norm(index_pos - thumb_pos)
            # Jarak antara telunjuk dan tengah (support rokok)
            index_middle_distance = np.linalg.norm(index_pos - middle_pos)
            
            # Skor grip: rokok biasanya dipegang antara telunjuk-jempol atau telunjuk-tengah
            grip_score_1 = max(0.0, 1.0 - (index_thumb_distance / 40.0))  # Grip telunjuk-jempol
            grip_score_2 = max(0.0, 1.0 - (index_middle_distance / 35.0))  # Grip telunjuk-tengah
            grip_score = max(grip_score_1, grip_score_2)
            
            # 2. Analisis posisi tangan relatif terhadap mulut
            # Posisi rata-rata jari yang memegang rokok
            cigarette_pos = (index_pos + middle_pos + thumb_pos) / 3
            
            # Jarak ke mulut
            distance_to_mouth = np.linalg.norm(cigarette_pos - mouth_center)
            
            # Skor kedekatan dengan threshold yang lebih besar
            proximity_threshold = 120  # pixels, lebih besar untuk menangkap gerakan mendekati mulut
            proximity_score = max(0.0, 1.0 - (distance_to_mouth / proximity_threshold))
            
            # 3. Analisis sudut tangan (rokok biasanya dipegang dengan sudut tertentu)
            # Vektor dari pergelangan ke posisi rokok
            hand_vector = cigarette_pos - wrist_pos
            hand_angle = math.degrees(math.atan2(hand_vector[1], hand_vector[0]))
            
            # Rokok biasanya dipegang dengan sudut 30-150 derajat (tangan terangkat)
            angle_score = 0.0
            if 30 <= abs(hand_angle) <= 150:
                angle_score = 1.0
            elif 15 <= abs(hand_angle) <= 165:
                angle_score = 0.7
            else:
                angle_score = 0.3
            
            # 4. Analisis gerakan temporal (jika ada history)
            temporal_score = 1.0
            if len(self.hand_history) > 5:
                recent_positions = [data['wrist'] for data in list(self.hand_history)[-5:]]
                # Cek apakah tangan bergerak ke arah mulut (pattern merokok)
                movements_to_mouth = []
                for i in range(1, len(recent_positions)):
                    prev_dist = np.linalg.norm(recent_positions[i-1] - mouth_center)
                    curr_dist = np.linalg.norm(recent_positions[i] - mouth_center)
                    if curr_dist < prev_dist:  # Bergerak mendekati mulut
                        movements_to_mouth.append(1)
                    else:
                        movements_to_mouth.append(0)
                
                if movements_to_mouth:
                    temporal_score = sum(movements_to_mouth) / len(movements_to_mouth)
                    temporal_score = max(0.5, temporal_score)  # Minimum 0.5 untuk tidak terlalu penalti
            
            # 5. Kombinasi semua faktor dengan bobot
            # Bobot: grip (40%) + proximity (30%) + angle (20%) + temporal (10%)
            smoking_score = (
                grip_score * 0.40 +
                proximity_score * 0.30 +
                angle_score * 0.20 +
                temporal_score * 0.10
            )
            
            # 6. Boost score jika multiple indicators kuat
            strong_indicators = sum([
                grip_score > 0.7,
                proximity_score > 0.6,
                angle_score > 0.8,
                temporal_score > 0.7
            ])
            
            if strong_indicators >= 3:
                smoking_score *= 1.2  # Boost 20% jika 3+ indikator kuat
            elif strong_indicators >= 2:
                smoking_score *= 1.1  # Boost 10% jika 2+ indikator kuat
            
            # Debug info untuk troubleshooting
            if smoking_score > 0.3:  # Log hanya jika ada potensi deteksi
                debug_info = {
                    'grip_score': grip_score,
                    'proximity_score': proximity_score,
                    'angle_score': angle_score,
                    'temporal_score': temporal_score,
                    'distance_to_mouth': distance_to_mouth,
                    'hand_angle': hand_angle,
                    'strong_indicators': strong_indicators
                }
                # Simpan untuk debugging (bisa di-comment jika tidak perlu)
                # print(f"🚬 Smoking detection: {smoking_score:.2f} - {debug_info}")
        
        return min(smoking_score, 1.0)
    
    def _detect_phone_use(self, hands_results, pose_results, w: int, h: int) -> float:
        """
        Mendeteksi penggunaan ponsel
        """
        phone_score = 0.0
        
        if hands_results.multi_hand_landmarks and pose_results.pose_landmarks:
            # Analisis posisi kepala (menunduk ke ponsel)
            pose_landmarks = pose_results.pose_landmarks
            nose = pose_landmarks.landmark[self.mp_pose.PoseLandmark.NOSE]
            left_ear = pose_landmarks.landmark[self.mp_pose.PoseLandmark.LEFT_EAR]
            right_ear = pose_landmarks.landmark[self.mp_pose.PoseLandmark.RIGHT_EAR]
            
            # Hitung sudut kepala
            ear_center = np.array([(left_ear.x + right_ear.x) / 2 * w, 
                                  (left_ear.y + right_ear.y) / 2 * h])
            nose_pos = np.array([nose.x * w, nose.y * h])
            
            head_angle = math.degrees(math.atan2(nose_pos[1] - ear_center[1], 
                                               nose_pos[0] - ear_center[0]))
            
            # Skor berdasarkan sudut kepala menunduk
            if head_angle > 10:  # Kepala menunduk
                head_down_score = min(head_angle / 45.0, 1.0)
                
                # Cek posisi tangan (memegang ponsel)
                if len(hands_results.multi_hand_landmarks) >= 1:
                    hand_landmarks = hands_results.multi_hand_landmarks[0]
                    wrist = hand_landmarks.landmark[self.mp_hands.HandLandmark.WRIST]
                    wrist_pos = np.array([wrist.x * w, wrist.y * h])
                    
                    # Jarak tangan ke area dada/perut (posisi memegang ponsel)
                    chest_area = np.array([w/2, h*0.7])  # Estimasi area dada
                    hand_chest_distance = np.linalg.norm(wrist_pos - chest_area)
                    
                    if hand_chest_distance < 150:  # pixels
                        hand_position_score = 1.0 - (hand_chest_distance / 150.0)
                        phone_score = head_down_score * hand_position_score
        
        return min(phone_score, 1.0)
    
    def _analyze_posture(self, pose_results, w: int, h: int) -> Dict:
        """
        Menganalisis postur tubuh pengguna
        """
        posture_analysis = {
            'head_angle': 0.0,
            'shoulder_alignment': 0.0,
            'posture_quality': 'unknown',
            'focus_impact': 0.0
        }
        
        if pose_results.pose_landmarks:
            landmarks = pose_results.pose_landmarks.landmark
            
            # Analisis sudut kepala
            nose = landmarks[self.mp_pose.PoseLandmark.NOSE]
            left_ear = landmarks[self.mp_pose.PoseLandmark.LEFT_EAR]
            right_ear = landmarks[self.mp_pose.PoseLandmark.RIGHT_EAR]
            
            ear_center = np.array([(left_ear.x + right_ear.x) / 2, 
                                  (left_ear.y + right_ear.y) / 2])
            nose_pos = np.array([nose.x, nose.y])
            
            # Hitung sudut kepala (pitch)
            head_vector = nose_pos - ear_center
            head_angle = math.degrees(math.atan2(head_vector[1], head_vector[0]))
            posture_analysis['head_angle'] = head_angle
            
            # Analisis alignment bahu
            left_shoulder = landmarks[self.mp_pose.PoseLandmark.LEFT_SHOULDER]
            right_shoulder = landmarks[self.mp_pose.PoseLandmark.RIGHT_SHOULDER]
            
            shoulder_diff = abs(left_shoulder.y - right_shoulder.y)
            shoulder_alignment = 1.0 - min(shoulder_diff * 10, 1.0)  # Normalized
            posture_analysis['shoulder_alignment'] = shoulder_alignment
            
            # Klasifikasi kualitas postur
            if abs(head_angle) < 15 and shoulder_alignment > 0.8:
                posture_analysis['posture_quality'] = 'excellent'
                posture_analysis['focus_impact'] = 0.1  # Positive impact
            elif abs(head_angle) < 30 and shoulder_alignment > 0.6:
                posture_analysis['posture_quality'] = 'good'
                posture_analysis['focus_impact'] = 0.0  # Neutral
            elif abs(head_angle) < 45:
                posture_analysis['posture_quality'] = 'fair'
                posture_analysis['focus_impact'] = -0.2  # Slight negative
            else:
                posture_analysis['posture_quality'] = 'poor'
                posture_analysis['focus_impact'] = -0.4  # Significant negative
        
        return posture_analysis
    
    def _assess_cognitive_load(self, hands_results, pose_results) -> Dict:
        """
        Menilai beban kognitif berdasarkan gerakan dan perilaku
        """
        cognitive_indicators = {
            'hand_fidgeting': 0.0,
            'head_movement_frequency': 0.0,
            'micro_movement_intensity': 0.0,
            'overall_load': 'normal'
        }
        
        # Analisis gerakan tangan (fidgeting)
        if hands_results.multi_hand_landmarks and len(self.hand_history) > 10:
            recent_hand_data = list(self.hand_history)[-10:]
            hand_movements = []
            
            for i in range(1, len(recent_hand_data)):
                prev_pos = recent_hand_data[i-1]['wrist']
                curr_pos = recent_hand_data[i]['wrist']
                movement = np.linalg.norm(curr_pos - prev_pos)
                hand_movements.append(movement)
            
            if hand_movements:
                fidgeting_score = np.std(hand_movements) / 10.0  # Normalized
                cognitive_indicators['hand_fidgeting'] = min(fidgeting_score, 1.0)
        
        # Analisis frekuensi gerakan kepala
        if len(self.head_position_history) > 20:
            recent_head_data = list(self.head_position_history)[-20:]
            head_movements = []
            
            for i in range(1, len(recent_head_data)):
                prev_pos = recent_head_data[i-1]['position']
                curr_pos = recent_head_data[i]['position']
                movement = np.linalg.norm(np.array(curr_pos) - np.array(prev_pos))
                head_movements.append(movement)
            
            if head_movements:
                movement_frequency = len([m for m in head_movements if m > 5]) / len(head_movements)
                cognitive_indicators['head_movement_frequency'] = movement_frequency
        
        # Hitung intensitas micro-movement
        micro_intensity = (cognitive_indicators['hand_fidgeting'] + 
                          cognitive_indicators['head_movement_frequency']) / 2
        cognitive_indicators['micro_movement_intensity'] = micro_intensity
        
        # Klasifikasi beban kognitif
        if micro_intensity < 0.3:
            cognitive_indicators['overall_load'] = 'low'
        elif micro_intensity < 0.6:
            cognitive_indicators['overall_load'] = 'normal'
        elif micro_intensity < 0.8:
            cognitive_indicators['overall_load'] = 'high'
        else:
            cognitive_indicators['overall_load'] = 'very_high'
        
        return cognitive_indicators
    
    def _analyze_hand_activity(self, hands_results, w: int, h: int) -> Dict:
        """
        Menganalisis aktivitas tangan secara detail
        """
        hand_analysis = {
            'hands_detected': 0,
            'dominant_hand': 'unknown',
            'activity_level': 0.0,
            'gesture_confidence': 0.0
        }
        
        if hands_results.multi_hand_landmarks:
            hand_analysis['hands_detected'] = len(hands_results.multi_hand_landmarks)
            
            # Analisis tangan dominan
            if hands_results.multi_handedness:
                dominant_hand_info = hands_results.multi_handedness[0]
                hand_analysis['dominant_hand'] = dominant_hand_info.classification[0].label.lower()
            
            # Hitung tingkat aktivitas
            if len(self.hand_history) > 5:
                recent_movements = []
                recent_data = list(self.hand_history)[-5:]
                
                for i in range(1, len(recent_data)):
                    prev_wrist = recent_data[i-1]['wrist']
                    curr_wrist = recent_data[i]['wrist']
                    movement = np.linalg.norm(curr_wrist - prev_wrist)
                    recent_movements.append(movement)
                
                if recent_movements:
                    activity_level = np.mean(recent_movements) / 20.0  # Normalized
                    hand_analysis['activity_level'] = min(activity_level, 1.0)
        
        return hand_analysis
    
    def _analyze_head_position(self, pose_results, w: int, h: int) -> Dict:
        """
        Menganalisis posisi dan gerakan kepala
        """
        head_analysis = {
            'position': [0.0, 0.0],
            'stability': 0.0,
            'looking_down': False,
            'attention_direction': 'center'
        }
        
        if pose_results.pose_landmarks:
            nose = pose_results.pose_landmarks.landmark[self.mp_pose.PoseLandmark.NOSE]
            head_pos = [nose.x * w, nose.y * h]
            head_analysis['position'] = head_pos
            
            # Simpan posisi kepala untuk analisis stabilitas
            head_data = {
                'position': head_pos,
                'timestamp': time.time()
            }
            self.head_position_history.append(head_data)
            
            # Analisis stabilitas kepala
            if len(self.head_position_history) > 10:
                recent_positions = [data['position'] for data in list(self.head_position_history)[-10:]]
                position_variance = np.var(recent_positions, axis=0)
                stability = 1.0 / (1.0 + np.sum(position_variance) / 1000.0)
                head_analysis['stability'] = min(stability, 1.0)
            
            # Deteksi apakah sedang menunduk
            if head_pos[1] > h * 0.6:  # Kepala di bagian bawah frame
                head_analysis['looking_down'] = True
            
            # Analisis arah perhatian
            if head_pos[0] < w * 0.4:
                head_analysis['attention_direction'] = 'left'
            elif head_pos[0] > w * 0.6:
                head_analysis['attention_direction'] = 'right'
            else:
                head_analysis['attention_direction'] = 'center'
        
        return head_analysis
    
    def _calculate_focus_impact(self, behavior_analysis: Dict) -> float:
        """
        Menghitung dampak perilaku terhadap fokus
        """
        focus_impact = 0.0
        
        # Dampak aktivitas
        activity = behavior_analysis['activity']
        activity_impacts = {
            'neutral': 0.0,
            'writing': 0.1,      # Slight positive (productive)
            'typing': 0.1,       # Slight positive (productive)
            'drinking': -0.1,    # Brief distraction
            'smoking': -0.3,     # Significant distraction
            'phone_use': -0.5    # Major distraction
        }
        
        activity_impact = activity_impacts.get(activity['dominant'], 0.0)
        focus_impact += activity_impact * activity['confidence']
        
        # Dampak postur
        posture_impact = behavior_analysis['posture']['focus_impact']
        focus_impact += posture_impact
        
        # Dampak beban kognitif
        cognitive_load = behavior_analysis['cognitive_load']['overall_load']
        cognitive_impacts = {
            'low': 0.1,
            'normal': 0.0,
            'high': -0.2,
            'very_high': -0.4
        }
        focus_impact += cognitive_impacts.get(cognitive_load, 0.0)
        
        # Normalisasi ke range [-1, 1]
        return max(-1.0, min(1.0, focus_impact))
    
    def _update_histories(self, behavior_analysis: Dict):
        """
        Update history buffers dengan data terbaru
        """
        # Update cognitive load history
        cognitive_data = {
            'load_level': behavior_analysis['cognitive_load']['overall_load'],
            'micro_intensity': behavior_analysis['cognitive_load']['micro_movement_intensity'],
            'timestamp': behavior_analysis['timestamp']
        }
        self.cognitive_load_history.append(cognitive_data)
    
    def _auto_calibrate(self, behavior_analysis: Dict):
        """
        Kalibrasi otomatis berdasarkan data awal
        """
        if len(self.hand_history) > 20 and len(self.head_position_history) > 20:
            # Set baseline positions
            recent_hand_positions = [data['wrist'] for data in list(self.hand_history)[-20:]]
            recent_head_positions = [data['position'] for data in list(self.head_position_history)[-20:]]
            
            self.baseline_hand_position = np.mean(recent_hand_positions, axis=0)
            self.baseline_head_position = np.mean(recent_head_positions, axis=0)
            
            self.is_calibrated = True
            print("🎯 Advanced Behavior Detector dikalibrasi otomatis")
    
    def get_behavior_summary(self) -> Dict:
        """
        Mendapatkan ringkasan perilaku dalam periode tertentu
        """
        summary = {
            'current_activity': self.current_activity,
            'activity_confidence': self.activity_confidence,
            'posture_state': self.posture_state,
            'cognitive_load': self.cognitive_load_level,
            'calibration_status': self.is_calibrated,
            'focus_impact_score': 0.0
        }
        
        # Hitung rata-rata focus impact dari history
        if len(self.cognitive_load_history) > 0:
            recent_data = list(self.cognitive_load_history)[-10:]
            # Simplified focus impact calculation
            load_impacts = {'low': 0.1, 'normal': 0.0, 'high': -0.2, 'very_high': -0.4}
            impacts = [load_impacts.get(data['load_level'], 0.0) for data in recent_data]
            summary['focus_impact_score'] = np.mean(impacts) if impacts else 0.0
        
        return summary
    
    def reset_calibration(self):
        """
        Reset kalibrasi detector
        """
        self.baseline_hand_position = None
        self.baseline_head_position = None
        self.is_calibrated = False
        
        # Clear histories
        self.hand_history.clear()
        self.head_position_history.clear()
        self.mouth_activity_history.clear()
        self.cognitive_load_history.clear()
        
        print("🔄 Advanced Behavior Detector direset")