"""
Face Tracker menggunakan MediaPipe FaceMesh
Mendeteksi landmark wajah, menghitung EAR (Eye Aspect Ratio) dan head pose
Untuk monitoring fokus real-time

(c) 2025 Radhitya Guntoro Adhi
"""

import cv2
import numpy as np
import mediapipe as mp
from typing import Optional, Tuple, Dict, List, Any
import math

from icikiwir.core.protocols import VisionPlugin
from icikiwir.core.registry import register_plugin
from src.vision.micro_expression import MicroExpressionDetector
from src.vision.behavior_detector import AdvancedBehaviorDetector

@register_plugin("vision")
class FaceTracker:
    """
    Kelas untuk tracking wajah dan menghitung metrik fokus
    Menggunakan MediaPipe FaceMesh untuk deteksi landmark
    """
    
    def __init__(self, 
                 max_num_faces: int = 1,
                 refine_landmarks: bool = True,
                 min_detection_confidence: float = 0.7,
                 min_tracking_confidence: float = 0.5):
        """
        Inisialisasi FaceTracker
        
        Args:
            max_num_faces: Maksimal jumlah wajah yang dideteksi
            refine_landmarks: Apakah menggunakan refined landmarks
            min_detection_confidence: Confidence threshold untuk deteksi
            min_tracking_confidence: Confidence threshold untuk tracking
        """
        self.mp_face_mesh = mp.solutions.face_mesh
        self.mp_drawing = mp.solutions.drawing_utils
        self.mp_drawing_styles = mp.solutions.drawing_styles
        
        # Inisialisasi FaceMesh
        self.face_mesh = self.mp_face_mesh.FaceMesh(
            max_num_faces=max_num_faces,
            refine_landmarks=refine_landmarks,
            min_detection_confidence=min_detection_confidence,
            min_tracking_confidence=min_tracking_confidence
        )
        
        # Indeks landmark untuk mata (berdasarkan MediaPipe FaceMesh)
        self.LEFT_EYE_LANDMARKS = [33, 7, 163, 144, 145, 153, 154, 155, 133, 173, 157, 158, 159, 160, 161, 246]
        self.RIGHT_EYE_LANDMARKS = [362, 382, 381, 380, 374, 373, 390, 249, 263, 466, 388, 387, 386, 385, 384, 398]
        
        # Landmark untuk EAR calculation (6 poin per mata)
        self.LEFT_EYE_EAR_LANDMARKS = [33, 160, 158, 133, 153, 144]  # P1, P2, P3, P4, P5, P6
        self.RIGHT_EYE_EAR_LANDMARKS = [362, 385, 387, 263, 373, 380]
        
        # Landmark untuk head pose estimation
        self.FACE_OVAL_LANDMARKS = [10, 338, 297, 332, 284, 251, 389, 356, 454, 323, 361, 288, 397, 365, 379, 378, 400, 377, 152, 148, 176, 149, 150, 136, 172, 58, 132, 93, 234, 127, 162, 21, 54, 103, 67, 109]
        
        # Model 3D points untuk head pose (dalam mm)
        self.model_points_3d = np.array([
            (0.0, 0.0, 0.0),             # Nose tip
            (0.0, -330.0, -65.0),        # Chin
            (-225.0, 170.0, -135.0),     # Left eye left corner
            (225.0, 170.0, -135.0),      # Right eye right corner
            (-150.0, -150.0, -125.0),    # Left Mouth corner
            (150.0, -150.0, -125.0)      # Right mouth corner
        ], dtype=np.float64)
        
        # Indeks landmark untuk head pose points
        self.head_pose_landmarks = [1, 152, 33, 263, 61, 291]  # nose, chin, left eye, right eye, left mouth, right mouth
        
        # Statistik tracking
        self.total_frames_processed = 0
        self.faces_detected_count = 0
        
        # Micro Expression Detector
        self.micro_expression_detector = MicroExpressionDetector()
        
    def process_frame(self, frame: np.ndarray) -> Dict:
        """
        Memproses frame untuk deteksi wajah dan perhitungan metrik
        
        Args:
            frame: Frame BGR dari kamera
            
        Returns:
            Dict: Hasil analisis dengan keys:
                - face_detected: bool
                - landmarks: List landmark points
                - ear_left: float (Eye Aspect Ratio mata kiri)
                - ear_right: float (Eye Aspect Ratio mata kanan)
                - ear_average: float (rata-rata EAR)
                - head_pose: Dict dengan yaw, pitch, roll
                - confidence: float
        """
        self.total_frames_processed += 1
        
        # Konversi BGR ke RGB untuk MediaPipe
        rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        
        # Proses dengan MediaPipe
        results = self.face_mesh.process(rgb_frame)
        
        # Inisialisasi hasil default
        analysis_result = {
            'face_detected': False,
            'landmarks': None,
            'ear_left': 0.0,
            'ear_right': 0.0,
            'ear_average': 0.0,
            'head_pose': {'yaw': 0.0, 'pitch': 0.0, 'roll': 0.0},
            'micro_expressions': {
                'neutral': 1.0, 'happy': 0.0, 'sad': 0.0, 'surprised': 0.0,
                'focused': 0.5, 'confused': 0.0, 'tired': 0.0
            },
            'dominant_expression': ('neutral', 1.0),
            'expression_focus_score': 50.0,
            'confidence': 0.0,
            'frame_with_annotations': frame.copy()
        }
        
        if results.multi_face_landmarks:
            self.faces_detected_count += 1
            
            # Ambil landmark wajah pertama
            face_landmarks = results.multi_face_landmarks[0]
            
            # Konversi landmark ke koordinat pixel
            h, w, _ = frame.shape
            landmarks_2d = []
            for landmark in face_landmarks.landmark:
                x = int(landmark.x * w)
                y = int(landmark.y * h)
                landmarks_2d.append([x, y])
            
            landmarks_2d = np.array(landmarks_2d)
            
            # Hitung EAR untuk kedua mata
            ear_left = self._calculate_ear(landmarks_2d, self.LEFT_EYE_EAR_LANDMARKS)
            ear_right = self._calculate_ear(landmarks_2d, self.RIGHT_EYE_EAR_LANDMARKS)
            ear_average = (ear_left + ear_right) / 2.0
            
            # Hitung head pose
            head_pose = self._calculate_head_pose(landmarks_2d, frame.shape)
            
            # Deteksi mikro ekspresi
            micro_expressions = {}
            dominant_expression = ('neutral', 1.0)
            expression_focus_score = 50.0
            
            try:
                # Kalibrasi baseline jika belum
                if not self.micro_expression_detector.is_calibrated():
                    self.micro_expression_detector.calibrate_baseline(landmarks_2d)
                else:
                    # Deteksi ekspresi
                    micro_expressions = self.micro_expression_detector.detect_expressions(landmarks_2d)
                    dominant_expression = self.micro_expression_detector.get_dominant_expression(micro_expressions)
                    expression_focus_score = self.micro_expression_detector.get_focus_contribution(micro_expressions)
            except Exception as e:
                print(f"⚠️ Error deteksi mikro ekspresi: {e}")
                micro_expressions = analysis_result['micro_expressions']
            
            # Update hasil
            analysis_result.update({
                'face_detected': True,
                'landmarks': landmarks_2d,
                'ear_left': ear_left,
                'ear_right': ear_right,
                'ear_average': ear_average,
                'head_pose': head_pose,
                'micro_expressions': micro_expressions,
                'dominant_expression': dominant_expression,
                'expression_focus_score': expression_focus_score,
                'confidence': 0.8,  # Placeholder confidence
                'frame_with_annotations': self._draw_annotations(frame, landmarks_2d, ear_average, head_pose, micro_expressions, dominant_expression)
            })
            
        return analysis_result
    
    def _calculate_ear(self, landmarks: np.ndarray, eye_landmarks: List[int]) -> float:
        """
        Menghitung Eye Aspect Ratio (EAR)
        EAR = (|p2-p6| + |p3-p5|) / (2 * |p1-p4|)
        
        Args:
            landmarks: Array landmark 2D
            eye_landmarks: Indeks landmark untuk mata
            
        Returns:
            float: Nilai EAR
        """
        try:
            # Ambil koordinat 6 poin mata
            points = landmarks[eye_landmarks]
            
            # Hitung jarak euclidean
            def euclidean_distance(p1, p2):
                return np.sqrt((p1[0] - p2[0])**2 + (p1[1] - p2[1])**2)
            
            # Jarak vertikal
            vertical_1 = euclidean_distance(points[1], points[5])  # p2-p6
            vertical_2 = euclidean_distance(points[2], points[4])  # p3-p5
            
            # Jarak horizontal
            horizontal = euclidean_distance(points[0], points[3])  # p1-p4
            
            # Hitung EAR
            if horizontal > 0:
                ear = (vertical_1 + vertical_2) / (2.0 * horizontal)
            else:
                ear = 0.0
                
            return ear
            
        except Exception as e:
            print(f"⚠️ Error menghitung EAR: {e}")
            return 0.0
    
    def _calculate_head_pose(self, landmarks_2d: np.ndarray, frame_shape: Tuple[int, int, int]) -> Dict[str, float]:
        """
        Menghitung head pose (yaw, pitch, roll) menggunakan PnP
        
        Args:
            landmarks_2d: Landmark 2D
            frame_shape: Shape frame (height, width, channels)
            
        Returns:
            Dict: Head pose dengan keys yaw, pitch, roll (dalam derajat)
        """
        try:
            h, w, _ = frame_shape
            
            # Camera matrix (estimasi sederhana)
            focal_length = w
            center = (w/2, h/2)
            camera_matrix = np.array([
                [focal_length, 0, center[0]],
                [0, focal_length, center[1]],
                [0, 0, 1]
            ], dtype=np.float64)
            
            # Distortion coefficients (asumsi tidak ada distorsi)
            dist_coeffs = np.zeros((4, 1))
            
            # Ambil 2D points untuk head pose
            image_points = np.array([
                landmarks_2d[self.head_pose_landmarks[0]],  # nose
                landmarks_2d[self.head_pose_landmarks[1]],  # chin
                landmarks_2d[self.head_pose_landmarks[2]],  # left eye
                landmarks_2d[self.head_pose_landmarks[3]],  # right eye
                landmarks_2d[self.head_pose_landmarks[4]],  # left mouth
                landmarks_2d[self.head_pose_landmarks[5]]   # right mouth
            ], dtype=np.float64)
            
            # Solve PnP
            success, rotation_vector, translation_vector = cv2.solvePnP(
                self.model_points_3d,
                image_points,
                camera_matrix,
                dist_coeffs
            )
            
            if success:
                # Konversi rotation vector ke rotation matrix
                rotation_matrix, _ = cv2.Rodrigues(rotation_vector)
                
                # Ekstrak euler angles
                yaw = math.atan2(rotation_matrix[1, 0], rotation_matrix[0, 0])
                pitch = math.atan2(-rotation_matrix[2, 0], 
                                 math.sqrt(rotation_matrix[2, 1]**2 + rotation_matrix[2, 2]**2))
                roll = math.atan2(rotation_matrix[2, 1], rotation_matrix[2, 2])
                
                # Konversi ke derajat
                yaw_deg = math.degrees(yaw)
                pitch_deg = math.degrees(pitch)
                roll_deg = math.degrees(roll)
                
                return {
                    'yaw': yaw_deg,
                    'pitch': pitch_deg,
                    'roll': roll_deg
                }
            else:
                return {'yaw': 0.0, 'pitch': 0.0, 'roll': 0.0}
                
        except Exception as e:
            print(f"⚠️ Error menghitung head pose: {e}")
            return {'yaw': 0.0, 'pitch': 0.0, 'roll': 0.0}
    
    def _draw_annotations(self, frame: np.ndarray, landmarks: np.ndarray,
                         ear: float, head_pose: Dict[str, float],
                         micro_expressions: Dict[str, float] = None,
                         dominant_expression: Tuple[str, float] = None) -> np.ndarray:
        """
        Menggambar anotasi pada frame dengan full face mesh

        Args:
            frame: Frame asli
            landmarks: Landmark points
            ear: Eye Aspect Ratio
            head_pose: Head pose data

        Returns:
            np.ndarray: Frame dengan anotasi
        """
        annotated_frame = frame.copy()

        try:
            # Gambar full face mesh dengan triangulasi manual
            # Menggunakan subset dari koneksi triangulasi MediaPipe untuk membuat wireframe

            # Definisi koneksi triangulasi yang aman (subset dari FACEMESH_TESSELATION)
            face_connections = [
                # Kontur wajah
                (10, 338), (338, 297), (297, 332), (332, 284), (284, 251), (251, 389),
                (389, 356), (356, 454), (454, 323), (323, 361), (361, 288), (288, 397),
                (397, 365), (365, 379), (379, 378), (378, 400), (400, 377), (377, 152),
                (152, 148), (148, 176), (176, 149), (149, 150), (150, 136), (136, 172),
                (172, 58), (58, 132), (132, 93), (93, 234), (234, 127), (127, 162),
                (162, 21), (21, 54), (54, 103), (103, 67), (67, 109), (109, 10),

                # Mata kiri
                (33, 7), (7, 163), (163, 144), (144, 145), (145, 153), (153, 154),
                (154, 155), (155, 133), (133, 173), (173, 157), (157, 158), (158, 159),
                (159, 160), (160, 161), (161, 246), (246, 33),

                # Mata kanan
                (362, 382), (382, 381), (381, 380), (380, 374), (374, 373), (373, 390),
                (390, 249), (249, 263), (263, 466), (466, 388), (388, 387), (387, 386),
                (386, 385), (385, 384), (384, 398), (398, 362),

                # Hidung
                (1, 2), (2, 5), (5, 4), (4, 6), (6, 168), (168, 8), (8, 9), (9, 10),
                (19, 94), (94, 125), (125, 141), (141, 235), (235, 31), (31, 228),
                (228, 229), (229, 230), (230, 231), (231, 232), (232, 233), (233, 244),
                (244, 245), (245, 122), (122, 6),

                # Bibir
                (61, 84), (84, 17), (17, 314), (314, 405), (405, 320), (320, 307),
                (307, 375), (375, 321), (321, 308), (308, 324), (324, 318), (318, 61),

                # Alis kiri
                (70, 63), (63, 105), (105, 66), (66, 107), (107, 55), (55, 65),
                (65, 52), (52, 53), (53, 46),

                # Alis kanan
                (296, 334), (334, 293), (293, 300), (300, 276), (276, 283), (283, 282),
                (282, 295), (295, 285), (285, 336),
            ]

            # Gambar koneksi wireframe
            for connection in face_connections:
                start_idx, end_idx = connection
                if start_idx < len(landmarks) and end_idx < len(landmarks):
                    start_point = tuple(landmarks[start_idx].astype(int))
                    end_point = tuple(landmarks[end_idx].astype(int))
                    cv2.line(annotated_frame, start_point, end_point, (0, 255, 0), 1)

            # Gambar landmark mata dengan highlight khusus
            for idx in self.LEFT_EYE_LANDMARKS + self.RIGHT_EYE_LANDMARKS:
                if idx < len(landmarks):
                    cv2.circle(annotated_frame, tuple(landmarks[idx].astype(int)), 2, (0, 255, 255), -1)

            # Gambar semua landmark sebagai titik kecil
            for i, point in enumerate(landmarks):
                if i % 5 == 0:  # Setiap 5 landmark untuk membuat pattern matrix
                    cv2.circle(annotated_frame, tuple(point.astype(int)), 1, (128, 128, 128), -1)
            
            # Gambar info EAR
            cv2.putText(annotated_frame, f"EAR: {ear:.3f}", (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
            
            # Gambar info head pose
            yaw, pitch, roll = head_pose['yaw'], head_pose['pitch'], head_pose['roll']
            cv2.putText(annotated_frame, f"Yaw: {yaw:.1f}°", (10, 60), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 0), 2)
            cv2.putText(annotated_frame, f"Pitch: {pitch:.1f}°", (10, 85), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 0), 2)
            cv2.putText(annotated_frame, f"Roll: {roll:.1f}°", (10, 110),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 0), 2)
            
            # Gambar info mikro ekspresi
            if dominant_expression:
                emotion, intensity = dominant_expression
                cv2.putText(annotated_frame, f"Ekspresi: {emotion} ({intensity:.2f})", (10, 140),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)
            
            # Gambar kalibrasi progress jika belum selesai
            if hasattr(self, 'micro_expression_detector') and not self.micro_expression_detector.is_calibrated():
                progress = self.micro_expression_detector.get_calibration_progress()
                cv2.putText(annotated_frame, f"Kalibrasi: {progress*100:.0f}%", (10, 170),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)
            
        except Exception as e:
            print(f"⚠️ Error menggambar anotasi: {e}")
            
        return annotated_frame
    
    def get_statistics(self) -> Dict[str, float]:
        """
        Mendapatkan statistik tracking
        
        Returns:
            Dict: Statistik dengan detection rate, dll
        """
        detection_rate = 0.0
        if self.total_frames_processed > 0:
            detection_rate = self.faces_detected_count / self.total_frames_processed
            
        return {
            'total_frames': self.total_frames_processed,
            'faces_detected': self.faces_detected_count,
            'detection_rate': detection_rate
        }
    
    def reset_statistics(self):
        """
        Reset statistik tracking
        """
        self.total_frames_processed = 0
        self.faces_detected_count = 0
        
        # Reset kalibrasi mikro ekspresi
        if hasattr(self, 'micro_expression_detector'):
            self.micro_expression_detector.reset_calibration()
    
    def __del__(self):
        """
        Destructor untuk cleanup
        """
        if hasattr(self, 'face_mesh'):
            self.face_mesh.close()
    
    # Implementasi VisionPlugin interface
    @property
    def name(self) -> str:
        """Nama unik plugin"""
        return "mediapipe_face_tracker"
    
    @property
    def version(self) -> str:
        """Versi plugin"""
        return "1.0.0"
    
    @property
    def description(self) -> str:
        """Deskripsi plugin"""
        return "Plugin untuk tracking wajah menggunakan MediaPipe FaceMesh dengan deteksi EAR dan head pose"
    
    def get_config_schema(self) -> Dict[str, Any]:
        """
        JSON Schema untuk validasi konfigurasi
        """
        return {
            "type": "object",
            "properties": {
                "max_num_faces": {
                    "type": "integer",
                    "minimum": 1,
                    "maximum": 5,
                    "default": 1,
                    "description": "Maksimal jumlah wajah yang dideteksi"
                },
                "refine_landmarks": {
                    "type": "boolean",
                    "default": True,
                    "description": "Apakah menggunakan refined landmarks"
                },
                "min_detection_confidence": {
                    "type": "number",
                    "minimum": 0.0,
                    "maximum": 1.0,
                    "default": 0.7,
                    "description": "Confidence threshold untuk deteksi"
                },
                "min_tracking_confidence": {
                    "type": "number",
                    "minimum": 0.0,
                    "maximum": 1.0,
                    "default": 0.5,
                    "description": "Confidence threshold untuk tracking"
                }
            },
            "additionalProperties": False
        }
    
    def configure(self, config: Dict[str, Any]) -> None:
        """
        Menerapkan konfigurasi ke plugin
        """
        # Reinisialisasi FaceMesh dengan konfigurasi baru
        if hasattr(self, 'face_mesh'):
            self.face_mesh.close()
        
        max_num_faces = config.get('max_num_faces', 1)
        refine_landmarks = config.get('refine_landmarks', True)
        min_detection_confidence = config.get('min_detection_confidence', 0.7)
        min_tracking_confidence = config.get('min_tracking_confidence', 0.5)
        
        self.face_mesh = self.mp_face_mesh.FaceMesh(
            max_num_faces=max_num_faces,
            refine_landmarks=refine_landmarks,
            min_detection_confidence=min_detection_confidence,
            min_tracking_confidence=min_tracking_confidence
        )
    
    def cleanup(self) -> None:
        """
        Membersihkan resources
        """
        if hasattr(self, 'face_mesh'):
            self.face_mesh.close()
        
        # Reset statistik
        self.reset_statistics()