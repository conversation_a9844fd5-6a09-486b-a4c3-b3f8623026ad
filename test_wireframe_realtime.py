#!/usr/bin/env python3
"""
Test script untuk memverifikasi wireframe face mesh real-time
Menampilkan video dengan overlay wireframe matrix
"""

import cv2
import time
import sys
import os

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from capture.stream import WebcamStream
from vision.face_tracker import FaceTracker

def main():
    """Test wireframe face mesh real-time"""
    print("🎥 Testing Wireframe Face Mesh Real-time...")
    print("Tekan 'q' untuk keluar, 'r' untuk reset kalibrasi")
    print("Anda seharusnya melihat garis-garis wireframe di wajah seperti matrix")
    
    # Initialize components
    webcam = WebcamStream()
    face_tracker = FaceTracker()
    
    try:
        # Start webcam
        if not webcam.start():
            print("❌ Gagal memulai webcam")
            return
        
        print("✅ Webcam started")
        print("📊 Tunggu deteksi wajah untuk melihat wireframe...")
        
        frame_count = 0
        
        while True:
            # Get frame
            frame = webcam.get_frame()
            if frame is None:
                continue
            
            frame_count += 1
            
            # Process frame
            results = face_tracker.process_frame(frame)
            
            # Get annotated frame with wireframe
            annotated_frame = results.get('frame_with_annotations', frame)
            
            # Show additional info on frame
            if results.get('face_detected', False):
                cv2.putText(annotated_frame, "WIREFRAME AKTIF", (10, 200),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                
                if 'dominant_expression' in results:
                    emotion, intensity = results['dominant_expression']
                    cv2.putText(annotated_frame, f"Deteksi: {emotion} ({intensity:.2f})", (10, 230),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            else:
                cv2.putText(annotated_frame, "Tidak ada wajah terdeteksi", (10, 200),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
            
            # Display frame
            cv2.imshow('Wireframe Face Mesh Test', annotated_frame)
            
            # Handle key press
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
            elif key == ord('r'):
                print("\n🔄 Resetting calibration...")
                face_tracker.reset_statistics()
                frame_count = 0
            
            # Small delay
            time.sleep(0.033)  # ~30 FPS
            
    except KeyboardInterrupt:
        print("\n⏹️ Interrupted by user")
    except Exception as e:
        print(f"\n❌ Error: {e}")
    finally:
        # Cleanup
        webcam.stop()
        cv2.destroyAllWindows()
        print("\n✅ Test completed")
        print("Jika Anda melihat garis-garis hijau membentuk wireframe di wajah,")
        print("maka overlay landmark sudah berfungsi dengan benar!")

if __name__ == "__main__":
    main()
