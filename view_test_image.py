#!/usr/bin/env python3
"""
Script untuk menampilkan gambar test landmark
"""

import cv2
import sys

def main():
    if len(sys.argv) < 2:
        print("Usage: python3 view_test_image.py <image_file>")
        print("Example: python3 view_test_image.py test_landmark_1.jpg")
        return
    
    image_file = sys.argv[1]
    
    try:
        # Load image
        img = cv2.imread(image_file)
        if img is None:
            print(f"❌ Cannot load image: {image_file}")
            return
        
        print(f"✅ Loaded {image_file}")
        print("Press any key to close the window")
        
        # Display image
        cv2.imshow(f'Landmark Test - {image_file}', img)
        cv2.waitKey(0)
        cv2.destroyAllWindows()
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
