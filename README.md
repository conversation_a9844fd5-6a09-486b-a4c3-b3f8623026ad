# 🧠 ICikiwir - Cognitive-Aware Study Companion

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Python 3.10+](https://img.shields.io/badge/python-3.10+-blue.svg)](https://www.python.org/downloads/)
[![Flet](https://img.shields.io/badge/GUI-Flet-purple.svg)](https://flet.dev/)

**ICikiwir** adalah sistem monitoring fokus dan kesehatan mental saat belajar yang menggunakan computer vision dan AI untuk membantu meningkatkan produktivitas belajar Anda.

**ICikiwir** is a cognitive-aware study companion that uses computer vision and AI to monitor focus and mental health during study sessions, helping improve your learning productivity.

---

## 🌟 Fitur Utama / Key Features

### 🇮🇩 Bahasa Indonesia
- **📹 Real-time Focus Monitoring**: Deteksi tingkat fokus menggunakan analisis wajah dan mata
- **🎯 Adaptive Feedback System**: Sistem nudging cerdas dengan text-to-speech
- **📊 Comprehensive Analytics**: Laporan mendalam tentang pola belajar Anda
- **🔔 Smart Notifications**: Pengingat yang tidak mengganggu untuk istirahat dan hidrasi
- **🏠 Privacy-First**: Semua data diproses secara lokal, tidak ada cloud dependency
- **💧 Hydration Reminders**: Pengingat minum air yang dapat dikustomisasi

### 🇺🇸 English
- **📹 Real-time Focus Monitoring**: Focus level detection using facial and eye analysis
- **🎯 Adaptive Feedback System**: Smart nudging system with text-to-speech
- **📊 Comprehensive Analytics**: In-depth reports about your study patterns
- **🔔 Smart Notifications**: Non-intrusive reminders for breaks and hydration
- **🏠 Privacy-First**: All data processed locally, no cloud dependencies
- **💧 Hydration Reminders**: Customizable water intake reminders

---

## 🚀 Instalasi / Installation

### Prasyarat / Prerequisites
- Python 3.10 atau lebih baru / Python 3.10 or newer
- Webcam yang berfungsi / Working webcam
- RAM minimal 8GB / Minimum 8GB RAM

### Opsi 1: Menggunakan Poetry (Direkomendasikan)
```bash
# Clone repository
git clone https://github.com/radhityaguntoro/icikiwir.git
cd icikiwir

# Install Poetry jika belum ada
curl -sSL https://install.python-poetry.org | python3 -

# Install dependencies
poetry install

# Jalankan aplikasi
poetry run python -m src
```

### Opsi 2: Menggunakan pip
```bash
# Clone repository
git clone https://github.com/radhityaguntoro/icikiwir.git
cd icikiwir

# Buat virtual environment
python -m venv venv
source venv/bin/activate  # Linux/Mac
# atau
venv\Scripts\activate     # Windows

# Install dependencies
pip install -r requirements.txt

# Jalankan aplikasi
python -m src
```

### Option 1: Using Poetry (Recommended)
```bash
# Clone repository
git clone https://github.com/radhityaguntoro/icikiwir.git
cd icikiwir

# Install Poetry if not already installed
curl -sSL https://install.python-poetry.org | python3 -

# Install dependencies
poetry install

# Run application
poetry run python -m src
```

### Option 2: Using pip
```bash
# Clone repository
git clone https://github.com/radhityaguntoro/icikiwir.git
cd icikiwir

# Create virtual environment
python -m venv venv
source venv/bin/activate  # Linux/Mac
# or
venv\Scripts\activate     # Windows

# Install dependencies
pip install -r requirements.txt

# Run application
python -m src
```

---

## 🎯 Cara Penggunaan / Usage

### 🇮🇩 Bahasa Indonesia

#### Menjalankan Real-Time Focus Monitor
```bash
# Jalankan aplikasi dengan fitur monitoring fokus
python -m src
# atau dengan Poetry
poetry run python -m src
```

#### Cara Cek Plugin
ICikiwir menggunakan sistem plugin yang dapat diperluas. Gunakan CLI untuk mengelola plugin:

```bash
# Lihat daftar plugin yang terdaftar
python scripts/icikiwir_cli.py list-plugins

# Lihat detail plugin tertentu
python scripts/icikiwir_cli.py plugin-info mediapipe_face_tracker

# Lihat daftar plugin dengan detail lengkap
python scripts/icikiwir_cli.py list-plugins --detailed

# Test plugin dengan konfigurasi
python scripts/icikiwir_cli.py test-plugin mediapipe_face_tracker

# Inisialisasi sistem dan discover plugins
python scripts/icikiwir_cli.py init
```

#### Plugin yang Tersedia:
- **Vision Plugins**: Plugin untuk pemrosesan video dan deteksi wajah
- **Analytics Plugins**: Plugin untuk analisis metrik dan scoring
- **Intervention Plugins**: Plugin untuk feedback dan intervensi

#### Langkah-langkah Penggunaan:
1. **Jalankan Aplikasi**: Gunakan perintah di atas untuk membuka GUI ICikiwir
2. **Setup Kamera**:
   - Pastikan webcam Anda berfungsi dan memberikan izin akses
   - Aplikasi akan otomatis mendeteksi kamera default (index 0)
   - Resolusi default: 640x480 pixels
3. **Mulai Monitoring**:
   - Klik tombol "Mulai Monitoring" berwarna hijau
   - Posisikan wajah Anda di depan kamera dengan pencahayaan yang cukup
4. **Monitor Real-time**:
   - **Live Video Feed**: Lihat video real-time dengan overlay deteksi wajah
   - **Skor Fokus**: Nilai 0-100 yang diperbarui setiap detik
   - **Blink Rate**: Frekuensi kedipan mata per menit
   - **Head Pose**: Orientasi kepala (Yaw/Pitch dalam derajat)
   - **FPS Counter**: Frame rate pemrosesan video
5. **Interpretasi Skor**:
   - 🎯 **80-100**: Sangat Fokus (hijau)
   - ✅ **60-79**: Fokus Baik (biru)
   - ⚠️ **40-59**: Fokus Sedang (orange)
   - ⚡ **20-39**: Kurang Fokus (merah muda)
   - ❌ **0-19**: Tidak Fokus (merah)

#### Metrik yang Dimonitor:
- **Eye Aspect Ratio (EAR)**: Deteksi kedipan mata untuk analisis fokus
- **Blink Rate**: Normal 12-20 kedipan per menit
- **Head Pose Variance**: Stabilitas posisi kepala (Yaw, Pitch, Roll)
- **Focus Score**: Kombinasi weighted dari blink rate (60%) dan head stability (40%)

### 🇺🇸 English

#### Running Real-Time Focus Monitor
```bash
# Run application with focus monitoring features
python -m src
# or with Poetry
poetry run python -m src
```

#### Usage Steps:
1. **Run Application**: Use the command above to open ICikiwir GUI
2. **Setup Camera**:
   - Ensure your webcam is working and grant access permissions
   - App will automatically detect default camera (index 0)
   - Default resolution: 640x480 pixels
3. **Start Monitoring**:
   - Click the green "Mulai Monitoring" button
   - Position your face in front of camera with adequate lighting
4. **Real-time Monitoring**:
   - **Live Video Feed**: View real-time video with face detection overlay
   - **Focus Score**: 0-100 value updated every second
   - **Blink Rate**: Eye blink frequency per minute
   - **Head Pose**: Head orientation (Yaw/Pitch in degrees)
   - **FPS Counter**: Video processing frame rate
5. **Score Interpretation**:
   - 🎯 **80-100**: Highly Focused (green)
   - ✅ **60-79**: Good Focus (blue)
   - ⚠️ **40-59**: Medium Focus (orange)
   - ⚡ **20-39**: Low Focus (pink)
   - ❌ **0-19**: Not Focused (red)

#### Monitored Metrics:
- **Eye Aspect Ratio (EAR)**: Blink detection for focus analysis
- **Blink Rate**: Normal 12-20 blinks per minute
- **Head Pose Variance**: Head position stability (Yaw, Pitch, Roll)
## 🔊 Adaptive Feedback TTS

### 🇮🇩 Bahasa Indonesia

#### Fitur Text-to-Speech Adaptif
ICikiwir dilengkapi dengan sistem feedback suara yang cerdas yang memberikan nudge kepada pengguna berdasarkan tingkat fokus real-time.

#### Cara Kerja:
1. **Monitoring Kontinyu**: Sistem memantau skor fokus setiap detik
2. **Rule-Based Evaluation**: Algoritma mengevaluasi skor berdasarkan threshold yang telah ditentukan
3. **Smart Feedback**: Memberikan pesan suara yang sesuai dengan kondisi fokus
4. **Cooldown Mechanism**: Mencegah spam audio dengan jeda minimal 20 detik antar pesan

#### Threshold dan Pesan:
- **Skor < 30**: "Kayaknya kamu lelah, istirahat sebentar yuk."
- **Skor 30-59**: "Fokus menurun, coba tarik napas dalam."
- **Recovery (+20 poin dari <60)**: "Good job, fokusmu kembali!"
- **Skor ≥ 80**: "Luar biasa! Fokusmu sangat baik." (sesekali)

#### Pengaturan TTS:
- **Toggle On/Off**: Switch "Speech Feedback" di interface
- **Voice Selection**: Otomatis menggunakan voice sistem default
- **Rate**: 180 words per minute (dapat dikonfigurasi)
- **Cooldown**: 20 detik antar feedback (dapat dikonfigurasi)

#### Konfigurasi Lanjutan:
```python
# Di src/config.py
TTS_ENABLED = True          # Aktifkan/nonaktifkan TTS
TTS_RATE = 180             # Kecepatan bicara (WPM)
TTS_COOLDOWN_SEC = 20      # Jeda minimal antar ucapan
```

### 🇺🇸 English

#### Adaptive Text-to-Speech Features
ICikiwir is equipped with an intelligent voice feedback system that provides nudges to users based on real-time focus levels.

#### How it Works:
1. **Continuous Monitoring**: System monitors focus score every second
2. **Rule-Based Evaluation**: Algorithm evaluates score based on predefined thresholds
3. **Smart Feedback**: Provides appropriate voice messages based on focus condition
4. **Cooldown Mechanism**: Prevents audio spam with minimum 20-second intervals

#### Thresholds and Messages:
- **Score < 30**: "Kayaknya kamu lelah, istirahat sebentar yuk." (You seem tired, take a break)
- **Score 30-59**: "Fokus menurun, coba tarik napas dalam." (Focus declining, try deep breathing)
- **Recovery (+20 points from <60)**: "Good job, fokusmu kembali!" (Good job, your focus is back!)
- **Score ≥ 80**: "Luar biasa! Fokusmu sangat baik." (Amazing! Your focus is excellent) (occasionally)

#### TTS Settings:
- **Toggle On/Off**: "Speech Feedback" switch in interface
- **Voice Selection**: Automatically uses system default voice
- **Rate**: 180 words per minute (configurable)
- **Cooldown**: 20 seconds between feedback (configurable)

#### Advanced Configuration:
```python
# In src/config.py
TTS_ENABLED = True          # Enable/disable TTS
TTS_RATE = 180             # Speech rate (WPM)
TTS_COOLDOWN_SEC = 20      # Minimum interval between speech
```
- **Focus Score**: Weighted combination of blink rate (60%) and head stability (40%)

---

## 🏗️ Arsitektur / Architecture

ICikiwir dibangun dengan arsitektur modular yang terdiri dari:

ICikiwir is built with a modular architecture consisting of:

- **Capture Module**: Webcam streaming dan frame processing
- **Vision Module**: Computer vision dengan MediaPipe dan ONNX
- **Analytics Module**: Analisis kognitif dan scoring
- **Intervention Module**: Sistem feedback dan TTS
- **Storage Module**: Database SQLite dan CSV logging
- **Reporting Module**: Generasi laporan dan visualisasi
- **GUI Module**: Interface Flet yang responsif

---

## 🛠️ Teknologi / Technology Stack

- **🐍 Python 3.10+**: Bahasa pemrograman utama
- **🎨 Flet**: Cross-platform GUI framework
- **👁️ OpenCV + MediaPipe**: Computer vision processing
- **🧠 ONNX Runtime**: Machine learning inference
- **📊 Altair + Plotly**: Data visualization
- **🗣️ pyttsx3**: Text-to-speech engine
- **💾 SQLite**: Local database storage
- **🐼 Pandas**: Data analysis dan manipulation

---

## 📈 Status Pengembangan / Development Status

🚧 **Aplikasi dalam tahap pengembangan aktif**

Versi saat ini adalah MVP (Minimum Viable Product) dengan fitur dasar. Fitur-fitur canggih sedang dalam pengembangan.

🚧 **Application under active development**

Current version is MVP (Minimum Viable Product) with basic features. Advanced features are under development.

### Roadmap
- [x] ✅ Project skeleton dan setup
- [x] ✅ Real-time focus monitoring (v0.1.0 - Alpha)
  - [x] WebcamStream dengan threading
  - [x] MediaPipe FaceMesh integration
  - [x] Eye Aspect Ratio (EAR) calculation
  - [x] Head pose estimation (Yaw/Pitch/Roll)
  - [x] Focus scoring algorithm
  - [x] Live GUI dengan Flet
- [x] ✅ Adaptive feedback system (v0.2.0 - Alpha)
  - [x] TTS Engine dengan pyttsx3 offline
  - [x] Rule-based feedback system
  - [x] Speech toggle dalam GUI
  - [x] Cooldown mechanism untuk TTS
- [ ] 🔄 Session logging dan dashboard
- [ ] 🔄 Multi-scale reports
- [ ] 🔄 Habit tracking (beta)
- [ ] 🔄 Hydration reminders

---

## 🤝 Kontribusi / Contributing

Kami menyambut kontribusi dari komunitas! Silakan baca [CONTRIBUTING.md](CONTRIBUTING.md) untuk panduan kontribusi.

We welcome contributions from the community! Please read [CONTRIBUTING.md](CONTRIBUTING.md) for contribution guidelines.

### Cara Berkontribusi / How to Contribute
1. Fork repository ini
2. Buat branch fitur (`git checkout -b feature/AmazingFeature`)
3. Commit perubahan (`git commit -m 'Add some AmazingFeature'`)
4. Push ke branch (`git push origin feature/AmazingFeature`)
5. Buat Pull Request

---

## 📄 Lisensi / License

Proyek ini dilisensikan di bawah MIT License - lihat file [LICENSE](LICENSE) untuk detail.

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

## 👨‍💻 Penulis / Author

**Radhitya Guntoro Adhi**
- GitHub: [@radhityaguntoro](https://github.com/radhityaguntoro)
- Email: <EMAIL>

---

## 🙏 Acknowledgments

- Terima kasih kepada komunitas open source yang telah menyediakan library-library luar biasa
- Thanks to the open source community for providing amazing libraries
- Inspirasi dari penelitian terbaru dalam cognitive load monitoring
- Inspired by recent research in cognitive load monitoring

---

## 📞 Dukungan / Support

Jika Anda mengalami masalah atau memiliki pertanyaan:

If you encounter issues or have questions:

- 🐛 **Bug Reports**: Buat issue di GitHub repository
- 💡 **Feature Requests**: Diskusikan di GitHub Discussions
- 📧 **Email**: Hubungi <EMAIL>
- 📖 **Documentation**: Lihat folder `docs/` untuk dokumentasi lengkap

---

<div align="center">

**Dibuat dengan ❤️ untuk meningkatkan produktivitas belajar**

**Made with ❤️ to improve learning productivity**

⭐ **Jangan lupa berikan star jika proyek ini membantu Anda!**

⭐ **Don't forget to star if this project helps you!**

</div>