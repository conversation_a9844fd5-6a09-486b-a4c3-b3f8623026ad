[tool:pytest]
# Konfigurasi pytest untuk ICikiwir
# (c) 2025 Radhitya Guntoro Adhi

minversion = 7.0
addopts =
    -ra
    -q
    --strict-markers
    --strict-config
    --tb=short
    --maxfail=5
    --durations=10
    --cov=src
    --cov-report=html
    --cov-report=term-missing
    --cov-fail-under=70

testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

markers =
    slow: marks tests as slow (deselect with '-m "not slow"')
    integration: marks tests as integration tests
    unit: marks tests as unit tests
    gui: marks tests that require GUI components
    camera: marks tests that require camera access
    network: marks tests that require network access

filterwarnings =
    ignore::UserWarning
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning

# Logging configuration
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S