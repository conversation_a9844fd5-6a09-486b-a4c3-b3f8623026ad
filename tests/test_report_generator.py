"""
Test untuk Report Generator - Phase D Markdown Reporting & Viewer
Testing generate_daily_report function dengan data dummy

(c) 2025 Radhitya Guntoro Adhi
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, date
from pathlib import Path
import tempfile
import shutil

# Import module yang akan ditest
from src.reporting.generator import generate_daily_report, ensure_report_dir


class TestReportGenerator:
    """Test class untuk report generator functionality"""
    
    def setup_method(self):
        """Setup untuk setiap test method"""
        # Buat temporary directory untuk testing
        self.temp_dir = Path(tempfile.mkdtemp())
        
        # Buat dummy data yang realistis
        self.test_date = date(2025, 6, 22)
        
        # Generate dummy metrics data
        timestamps = pd.date_range(
            start=f"{self.test_date} 08:00:00",
            end=f"{self.test_date} 17:00:00",
            freq='5min'  # Data setiap 5 menit
        )
        
        # Simulasi data fokus yang realistis dengan pola harian
        np.random.seed(42)  # Untuk konsistensi testing
        
        # Pola fokus: tinggi di pagi, turun siang, naik lagi sore
        time_factor = np.linspace(0, 2*np.pi, len(timestamps))
        focus_base = 70 + 15 * np.sin(time_factor - np.pi/2)  # Pola sinusoidal
        focus_noise = 10 * np.random.randn(len(timestamps))
        focus_scores = np.clip(focus_base + focus_noise, 0, 100)
        
        # Blink rate yang bervariasi (normal 12-20 per menit)
        blink_rates = 15 + 3 * np.random.randn(len(timestamps))
        blink_rates = np.clip(blink_rates, 8, 25)
        
        # Head pose data
        head_yaw = 5 * np.random.randn(len(timestamps))
        head_pitch = 3 * np.random.randn(len(timestamps))
        
        self.metrics_df = pd.DataFrame({
            'timestamp': timestamps,
            'focus_score': focus_scores,
            'blink_rate': blink_rates,
            'head_pose_yaw': head_yaw,
            'head_pose_pitch': head_pitch
        })
        
    def teardown_method(self):
        """Cleanup setelah setiap test method"""
        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)
    
    def test_ensure_report_dir(self):
        """Test fungsi ensure_report_dir"""
        # Test default behavior (no parameters)
        result_dir = ensure_report_dir()
        
        assert result_dir.exists()
        assert result_dir.is_dir()
        assert result_dir.name == "reports"
    
    def test_generate_daily_report_basic(self):
        """Test basic functionality generate_daily_report"""
        # Generate report
        report_path = generate_daily_report(
            self.test_date, 
            self.metrics_df, 
            str(self.temp_dir)
        )
        
        # Verify file exists
        assert report_path is not None
        assert report_path.exists()
        assert report_path.suffix == '.md'
        
        # Verify filename format
        expected_filename = f"{self.test_date.strftime('%Y-%m-%d')}.md"
        assert report_path.name == expected_filename
        
        # Read and verify content
        content = report_path.read_text(encoding='utf-8')
        
        # Check YAML front-matter
        assert content.startswith('---')
        assert 'date:' in content
        assert 'total_session:' in content
        assert 'avg_focus:' in content
        assert 'blink_rate_mean:' in content
        
        # Check main content
        assert '# Laporan Harian ICikiwir' in content
        assert '## 📊 Ringkasan Metrik' in content
        assert '## 📈 Grafik Skor Fokus' in content
        assert '## 🎯 Analisis' in content
        assert '## 💡 Rekomendasi' in content
        
        # Check that metrics are included
        assert 'Rata-rata Fokus:' in content
        assert 'Rata-rata Blink Rate:' in content
        
    def test_generate_daily_report_with_empty_data(self):
        """Test generate report dengan data kosong"""
        empty_df = pd.DataFrame(columns=['timestamp', 'focus_score', 'blink_rate'])
        
        result = generate_daily_report(
            self.test_date,
            empty_df,
            str(self.temp_dir)
        )
        
        # Should return False for empty data
        assert result is False
    
    def test_generate_daily_report_statistics(self):
        """Test bahwa statistik dihitung dengan benar"""
        report_path = generate_daily_report(
            self.test_date,
            self.metrics_df,
            str(self.temp_dir)
        )
        
        content = report_path.read_text(encoding='utf-8')
        
        # Hitung statistik manual untuk verifikasi
        avg_focus = self.metrics_df['focus_score'].mean()
        max_focus = self.metrics_df['focus_score'].max()
        min_focus = self.metrics_df['focus_score'].min()
        avg_blink = self.metrics_df['blink_rate'].mean()
        
        # Check that calculated values are in content
        # (Toleransi untuk pembulatan)
        assert f"{avg_focus:.1f}" in content
        assert f"{max_focus:.1f}" in content
        assert f"{min_focus:.1f}" in content
        assert f"{avg_blink:.1f}" in content
    
    def test_generate_daily_report_graphs(self):
        """Test bahwa grafik di-generate dengan benar"""
        report_path = generate_daily_report(
            self.test_date,
            self.metrics_df,
            str(self.temp_dir)
        )
        
        content = report_path.read_text(encoding='utf-8')
        
        # Check for base64 image placeholders
        # (Karena Flet Markdown belum support base64 images penuh)
        assert '📈 Grafik Skor Fokus' in content
        
        # Should contain base64 image data
        assert 'data:image/png;base64,' in content
        
        # Check for image alt text
        assert '![Grafik Skor Fokus]' in content
    
    def test_generate_daily_report_recommendations(self):
        """Test bahwa rekomendasi di-generate berdasarkan data"""
        # Test dengan data fokus rendah
        low_focus_df = self.metrics_df.copy()
        low_focus_df['focus_score'] = 30  # Fokus rendah
        
        report_path = generate_daily_report(
            self.test_date,
            low_focus_df,
            str(self.temp_dir)
        )
        
        content = report_path.read_text(encoding='utf-8')
        
        # Should contain recommendations for low focus
        recommendations_section = content[content.find('## 💡 Rekomendasi'):]
        assert len(recommendations_section) > 50  # Should have substantial content
        assert any(word in recommendations_section.lower() for word in [
            'istirahat', 'mata', 'postur', 'pencahayaan', 'hidrasi', 'break'
        ])
    
    def test_generate_daily_report_file_permissions(self):
        """Test bahwa file report dapat dibaca dan ditulis"""
        report_path = generate_daily_report(
            self.test_date,
            self.metrics_df,
            str(self.temp_dir)
        )
        
        # Test read permission
        content = report_path.read_text(encoding='utf-8')
        assert len(content) > 0
        
        # Test that file is not empty
        assert report_path.stat().st_size > 100  # At least 100 bytes
        
        # Test that we can write to the directory
        test_file = report_path.parent / "test_write.txt"
        test_file.write_text("test")
        assert test_file.exists()
        test_file.unlink()
    
    def test_generate_daily_report_different_dates(self):
        """Test generate report untuk tanggal yang berbeda"""
        dates_to_test = [
            date(2025, 1, 1),   # New Year
            date(2025, 6, 15),  # Mid year
            date(2025, 12, 31)  # End of year
        ]
        
        for test_date in dates_to_test:
            report_path = generate_daily_report(
                test_date,
                self.metrics_df,
                str(self.temp_dir)
            )
            
            assert report_path.exists()
            
            # Check date in filename
            expected_date_str = test_date.strftime('%Y-%m-%d')
            assert expected_date_str in report_path.name
            
            # Check date in content
            content = report_path.read_text(encoding='utf-8')
            assert expected_date_str in content
    
    def test_generate_daily_report_concurrent_access(self):
        """Test bahwa multiple reports dapat di-generate secara bersamaan"""
        # Skip threading test karena matplotlib tidak thread-safe
        # Test sequential generation dengan tanggal berbeda
        dates_to_test = [
            date(2025, 6, 20),
            date(2025, 6, 21),
            date(2025, 6, 22)
        ]
        
        results = []
        for test_date in dates_to_test:
            report_path = generate_daily_report(
                test_date,
                self.metrics_df,
                str(self.temp_dir)
            )
            results.append(report_path)
        
        # Verify all files exist and are different
        assert len(results) == 3
        for report_path in results:
            assert report_path.exists()
        
        # Verify filenames are different
        filenames = [r.name for r in results]
        assert len(set(filenames)) == 3  # All unique


# Integration test
def test_full_report_generation_workflow():
    """Integration test untuk full workflow report generation"""
    with tempfile.TemporaryDirectory() as temp_dir:
        # Setup data
        test_date = date.today()
        
        # Create realistic data for full day
        timestamps = pd.date_range(
            start=f"{test_date} 09:00:00",
            end=f"{test_date} 18:00:00",
            freq='2min'
        )
        
        # Realistic focus pattern: high morning, dip after lunch, recover
        hours = np.array([t.hour + t.minute/60 for t in timestamps])
        focus_pattern = np.where(
            hours < 12,  # Morning
            80 + 10 * np.random.randn(len(timestamps)),
            np.where(
                hours < 14,  # Lunch dip
                60 + 15 * np.random.randn(len(timestamps)),
                75 + 12 * np.random.randn(len(timestamps))  # Afternoon recovery
            )
        )
        focus_scores = np.clip(focus_pattern, 0, 100)
        
        metrics_df = pd.DataFrame({
            'timestamp': timestamps,
            'focus_score': focus_scores,
            'blink_rate': 15 + 5 * np.random.randn(len(timestamps)),
            'head_pose_yaw': 3 * np.random.randn(len(timestamps)),
            'head_pose_pitch': 2 * np.random.randn(len(timestamps))
        })
        
        # Generate report
        report_path = generate_daily_report(test_date, metrics_df, temp_dir)
        
        # Comprehensive verification
        assert report_path.exists()
        
        content = report_path.read_text(encoding='utf-8')
        
        # Verify structure
        required_sections = [
            '# Laporan Harian ICikiwir',
            '## 📊 Ringkasan Metrik',
            '## 📈 Grafik Skor Fokus',
            '## 🎯 Analisis',
            '## 💡 Rekomendasi'
        ]
        
        for section in required_sections:
            assert section in content, f"Missing section: {section}"
        
        # Verify YAML front-matter
        lines = content.split('\n')
        assert lines[0] == '---'
        yaml_end = lines.index('---', 1)
        yaml_content = '\n'.join(lines[1:yaml_end])
        
        assert 'date:' in yaml_content
        assert 'total_session:' in yaml_content
        assert 'avg_focus:' in yaml_content
        assert 'blink_rate_mean:' in yaml_content
        
        # Verify metrics are reasonable
        avg_focus = metrics_df['focus_score'].mean()
        assert f"{avg_focus:.1f}" in content
        
        print(f"✅ Integration test passed. Report generated: {report_path}")
        print(f"📊 Report size: {report_path.stat().st_size} bytes")
        print(f"📈 Average focus score: {avg_focus:.1f}")


if __name__ == "__main__":
    # Run basic test jika dijalankan langsung
    test_full_report_generation_workflow()
    print("✅ All tests passed!")