"""
Unit tests untuk Focus Metric Calculator
Testing fungsi compute_focus dan komponen terkait

(c) 2025 Ra<PERSON><PERSON><PERSON>
"""

import pytest
import time
import numpy as np
from src.analytics.focus_metric import FocusMetricCalculator, compute_focus


class TestFocusMetricCalculator:
    """Test class untuk FocusMetricCalculator"""
    
    def setup_method(self):
        """Setup untuk setiap test method"""
        self.calculator = FocusMetricCalculator(
            blink_window_sec=10,  # Window kecil untuk testing
            head_var_window=10,
            normal_blink_range=(12.0, 20.0)
        )
    
    def test_initialization(self):
        """Test inisialisasi calculator"""
        assert self.calculator.blink_window_sec == 10
        assert self.calculator.head_var_window == 10
        assert self.calculator.normal_blink_range == (12.0, 20.0)
        assert len(self.calculator.blink_history) == 0
        assert len(self.calculator.head_pose_history) == 0
        assert self.calculator.last_focus_score == 50.0
    
    def test_add_frame_data(self):
        """Test penambahan data frame"""
        timestamp = time.time()
        head_pose = {'yaw': 5.0, 'pitch': -2.0, 'roll': 1.0}
        
        # Tambah data frame
        self.calculator.add_frame_data(0.3, head_pose, timestamp)
        
        # Cek data tersimpan
        assert len(self.calculator.head_pose_history) == 1
        assert self.calculator.head_pose_history[0]['yaw'] == 5.0
        assert self.calculator.head_pose_history[0]['pitch'] == -2.0
        assert self.calculator.head_pose_history[0]['roll'] == 1.0
    
    def test_blink_detection(self):
        """Test deteksi blink berdasarkan EAR"""
        timestamp = time.time()
        head_pose = {'yaw': 0.0, 'pitch': 0.0, 'roll': 0.0}
        
        # Simulasi pola blink: tinggi -> rendah -> tinggi
        ear_values = [0.3, 0.2, 0.3]  # Pola blink
        
        for i, ear in enumerate(ear_values):
            self.calculator.add_frame_data(ear, head_pose, timestamp + i * 0.1)
        
        # Cek apakah blink terdeteksi
        blink_rate = self.calculator.calculate_blink_rate()
        assert blink_rate > 0  # Harus ada blink yang terdeteksi
    
    def test_calculate_blink_rate(self):
        """Test perhitungan blink rate"""
        timestamp = time.time()
        head_pose = {'yaw': 0.0, 'pitch': 0.0, 'roll': 0.0}
        
        # Simulasi 3 blink dalam 10 detik
        for i in range(3):
            # Pola blink
            self.calculator.add_frame_data(0.3, head_pose, timestamp + i * 3)
            self.calculator.add_frame_data(0.2, head_pose, timestamp + i * 3 + 0.1)
            self.calculator.add_frame_data(0.3, head_pose, timestamp + i * 3 + 0.2)
        
        blink_rate = self.calculator.calculate_blink_rate(timestamp + 10)
        
        # 3 blink dalam 10 detik = 18 blink per menit
        expected_rate = 3 * 6  # 3 blink * (60/10)
        assert abs(blink_rate - expected_rate) < 1.0  # Toleransi 1 blink/menit
    
    def test_calculate_head_pose_variance(self):
        """Test perhitungan head pose variance"""
        timestamp = time.time()
        
        # Data head pose dengan variance rendah (stabil)
        stable_poses = [
            {'yaw': 0.0, 'pitch': 0.0, 'roll': 0.0},
            {'yaw': 1.0, 'pitch': 0.5, 'roll': 0.2},
            {'yaw': -0.5, 'pitch': -0.3, 'roll': -0.1}
        ]
        
        for i, pose in enumerate(stable_poses):
            self.calculator.add_frame_data(0.3, pose, timestamp + i)
        
        variance = self.calculator.calculate_head_pose_variance(timestamp + 3)
        
        # Variance harus rendah untuk data stabil
        assert variance['total_var'] < 10.0
        assert variance['yaw_var'] >= 0
        assert variance['pitch_var'] >= 0
        assert variance['roll_var'] >= 0
    
    def test_score_blink_rate_normal(self):
        """Test scoring blink rate dalam range normal"""
        # Blink rate normal (15 per menit)
        score = self.calculator._score_blink_rate(15.0)
        assert score >= 80.0  # Skor tinggi untuk blink rate normal
    
    def test_score_blink_rate_low(self):
        """Test scoring blink rate rendah"""
        # Blink rate sangat rendah (5 per menit)
        score = self.calculator._score_blink_rate(5.0)
        assert 30.0 <= score <= 80.0  # Skor sedang (bisa fokus atau mengantuk)
    
    def test_score_blink_rate_high(self):
        """Test scoring blink rate tinggi"""
        # Blink rate tinggi (40 per menit)
        score = self.calculator._score_blink_rate(40.0)
        assert score <= 50.0  # Skor rendah untuk blink rate tinggi
    
    def test_score_head_variance_stable(self):
        """Test scoring head variance stabil"""
        # Variance rendah (kepala stabil)
        score = self.calculator._score_head_variance(10.0)
        assert score >= 85.0  # Skor tinggi untuk kepala stabil
    
    def test_score_head_variance_unstable(self):
        """Test scoring head variance tidak stabil"""
        # Variance tinggi (kepala tidak stabil)
        score = self.calculator._score_head_variance(500.0)
        assert score <= 30.0  # Skor rendah untuk kepala tidak stabil
    
    def test_compute_focus_high_score(self):
        """Test compute_focus dengan kondisi fokus tinggi"""
        # Blink rate normal dan head variance rendah
        score = self.calculator.compute_focus(blink_rate=15.0, head_variance=10.0)
        assert score >= 80.0  # Skor fokus tinggi
    
    def test_compute_focus_low_score(self):
        """Test compute_focus dengan kondisi fokus rendah"""
        # Blink rate tinggi dan head variance tinggi
        score = self.calculator.compute_focus(blink_rate=40.0, head_variance=500.0)
        assert score <= 40.0  # Skor fokus rendah
    
    def test_compute_focus_medium_score(self):
        """Test compute_focus dengan kondisi fokus sedang"""
        # Blink rate sedikit tinggi dan head variance sedang
        score = self.calculator.compute_focus(blink_rate=25.0, head_variance=150.0)
        assert 40.0 <= score <= 80.0  # Skor fokus sedang
    
    def test_get_current_metrics(self):
        """Test mendapatkan metrik saat ini"""
        timestamp = time.time()
        head_pose = {'yaw': 2.0, 'pitch': 1.0, 'roll': 0.5}
        
        # Tambah beberapa data
        for i in range(5):
            self.calculator.add_frame_data(0.3, head_pose, timestamp + i)
        
        metrics = self.calculator.get_current_metrics(timestamp + 5)
        
        # Cek semua metrik ada
        required_keys = [
            'focus_score', 'blink_rate', 'head_variance',
            'head_yaw_var', 'head_pitch_var', 'head_roll_var',
            'total_blinks', 'data_points'
        ]
        
        for key in required_keys:
            assert key in metrics
            assert isinstance(metrics[key], (int, float))
    
    def test_get_focus_level_description(self):
        """Test deskripsi level fokus"""
        assert "Sangat Fokus" in self.calculator.get_focus_level_description(90.0)
        assert "Fokus Baik" in self.calculator.get_focus_level_description(70.0)
        assert "Fokus Sedang" in self.calculator.get_focus_level_description(50.0)
        assert "Kurang Fokus" in self.calculator.get_focus_level_description(30.0)
        assert "Tidak Fokus" in self.calculator.get_focus_level_description(10.0)
    
    def test_reset_data(self):
        """Test reset data"""
        timestamp = time.time()
        head_pose = {'yaw': 1.0, 'pitch': 1.0, 'roll': 1.0}
        
        # Tambah data
        self.calculator.add_frame_data(0.3, head_pose, timestamp)
        assert len(self.calculator.head_pose_history) > 0
        
        # Reset
        self.calculator.reset_data()
        assert len(self.calculator.head_pose_history) == 0
        assert len(self.calculator.blink_history) == 0
        assert self.calculator.blink_count == 0
        assert self.calculator.last_focus_score == 50.0
    
    def test_get_statistics(self):
        """Test mendapatkan statistik"""
        stats = self.calculator.get_statistics()
        
        required_keys = [
            'total_calculations', 'total_blinks_detected',
            'head_pose_data_points', 'last_focus_score',
            'blink_window_sec', 'head_var_window', 'normal_blink_range'
        ]
        
        for key in required_keys:
            assert key in stats
    
    def test_data_cleanup(self):
        """Test pembersihan data lama"""
        timestamp = time.time()
        head_pose = {'yaw': 0.0, 'pitch': 0.0, 'roll': 0.0}
        
        # Tambah data lama (lebih dari window)
        old_timestamp = timestamp - 20  # 20 detik lalu (lebih dari window 10 detik)
        self.calculator.add_frame_data(0.3, head_pose, old_timestamp)
        
        # Tambah data baru
        self.calculator.add_frame_data(0.3, head_pose, timestamp)
        
        # Data lama harus dibersihkan
        recent_poses = [p for p in self.calculator.head_pose_history 
                       if timestamp - p['timestamp'] <= self.calculator.head_var_window]
        
        assert len(recent_poses) == 1  # Hanya data baru yang tersisa


class TestComputeFocusFunction:
    """Test untuk fungsi compute_focus standalone"""
    
    def test_compute_focus_function_normal(self):
        """Test fungsi compute_focus dengan nilai normal"""
        score = compute_focus(blink_rate=15.0, head_variance=50.0)
        assert 0.0 <= score <= 100.0
        assert isinstance(score, float)
    
    def test_compute_focus_function_extreme_values(self):
        """Test fungsi compute_focus dengan nilai ekstrem"""
        # Nilai sangat tinggi
        score_high = compute_focus(blink_rate=100.0, head_variance=1000.0)
        assert 0.0 <= score_high <= 100.0
        
        # Nilai sangat rendah
        score_low = compute_focus(blink_rate=0.0, head_variance=0.0)
        assert 0.0 <= score_low <= 100.0
    
    def test_compute_focus_function_negative_values(self):
        """Test fungsi compute_focus dengan nilai negatif"""
        # Nilai negatif harus ditangani dengan baik
        score = compute_focus(blink_rate=-5.0, head_variance=-10.0)
        assert 0.0 <= score <= 100.0


class TestEdgeCases:
    """Test untuk edge cases dan kondisi khusus"""
    
    def setup_method(self):
        """Setup untuk setiap test method"""
        self.calculator = FocusMetricCalculator()
    
    def test_empty_data(self):
        """Test dengan data kosong"""
        blink_rate = self.calculator.calculate_blink_rate()
        variance = self.calculator.calculate_head_pose_variance()
        score = self.calculator.compute_focus()
        
        assert blink_rate == 0.0
        assert variance['total_var'] == 0.0
        assert 0.0 <= score <= 100.0
    
    def test_single_data_point(self):
        """Test dengan satu data point"""
        timestamp = time.time()
        head_pose = {'yaw': 5.0, 'pitch': 2.0, 'roll': 1.0}
        
        self.calculator.add_frame_data(0.3, head_pose, timestamp)
        
        variance = self.calculator.calculate_head_pose_variance()
        assert variance['total_var'] == 0.0  # Tidak bisa hitung variance dengan 1 point
    
    def test_missing_head_pose_keys(self):
        """Test dengan head pose yang tidak lengkap"""
        timestamp = time.time()
        incomplete_pose = {'yaw': 5.0}  # Missing pitch dan roll
        
        # Harus tidak error
        self.calculator.add_frame_data(0.3, incomplete_pose, timestamp)
        
        # Data harus tersimpan dengan nilai default
        assert len(self.calculator.head_pose_history) == 1
        assert self.calculator.head_pose_history[0]['pitch'] == 0.0
        assert self.calculator.head_pose_history[0]['roll'] == 0.0


if __name__ == "__main__":
    # Jalankan test jika file dieksekusi langsung
    pytest.main([__file__, "-v"])