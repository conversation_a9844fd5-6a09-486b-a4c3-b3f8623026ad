"""
Test dasar untuk memastikan struktur proyek berfungsi dengan baik
Unit tests untuk komponen-komponen utama ICikiwir

(c) 2025 Radhitya Guntoro Adhi
"""

import pytest
import sys
import os

# Tambahkan src ke Python path untuk testing
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

def test_import_main_modules():
    """
    Test untuk memastikan semua modul utama dapat di-import
    """
    try:
        import src
        assert src.__version__ == "0.1.0"
        assert src.__author__ == "Radhitya Guntoro Adhi"
    except ImportError as e:
        pytest.fail(f"Gagal import modul src: {e}")

def test_import_gui_module():
    """
    Test untuk memastikan modul GUI dapat di-import
    """
    try:
        from src.gui import app
        assert hasattr(app, 'run_app')
        assert callable(app.run_app)
    except ImportError as e:
        pytest.fail(f"Gagal import modul GUI: {e}")

def test_import_capture_module():
    """
    Test untuk memastikan modul capture dapat di-import
    """
    try:
        from src.capture.stream import WebcamStream
        assert WebcamStream is not None
        
        # Test inisialisasi tanpa menjalankan kamera
        stream = WebcamStream()
        assert stream.src == 0
        assert stream.resolution == (640, 480)
        assert not stream.is_active()
        
    except ImportError as e:
        pytest.fail(f"Gagal import modul capture: {e}")

def test_project_structure():
    """
    Test untuk memastikan struktur folder proyek sesuai arsitektur
    """
    base_path = os.path.join(os.path.dirname(__file__), '..')
    
    # Folder yang harus ada
    required_folders = [
        'src',
        'src/gui',
        'src/capture',
        'src/vision',
        'src/analytics',
        'src/intervention',
        'src/storage',
        'src/reporting',
        'tests',
        'data',
        'docs',
        'scripts'
    ]
    
    for folder in required_folders:
        folder_path = os.path.join(base_path, folder)
        assert os.path.exists(folder_path), f"Folder {folder} tidak ditemukan"
        assert os.path.isdir(folder_path), f"{folder} bukan folder"

def test_required_files():
    """
    Test untuk memastikan file-file penting ada
    """
    base_path = os.path.join(os.path.dirname(__file__), '..')
    
    # File yang harus ada
    required_files = [
        'README.md',
        'LICENSE',
        'pyproject.toml',
        'requirements.txt',
        '.gitignore',
        'src/__init__.py',
        'src/__main__.py',
        'src/gui/__init__.py',
        'src/gui/app.py',
        'src/capture/__init__.py',
        'src/capture/stream.py'
    ]
    
    for file in required_files:
        file_path = os.path.join(base_path, file)
        assert os.path.exists(file_path), f"File {file} tidak ditemukan"
        assert os.path.isfile(file_path), f"{file} bukan file"

def test_python_version():
    """
    Test untuk memastikan Python version sesuai requirement
    """
    import sys
    
    # Minimal Python 3.10
    assert sys.version_info >= (3, 10), f"Python version {sys.version} tidak didukung. Minimal Python 3.10"

@pytest.mark.slow
def test_dependencies_import():
    """
    Test untuk memastikan semua dependency utama dapat di-import
    Ditandai sebagai slow test karena import beberapa library berat
    """
    dependencies = [
        'cv2',          # opencv-python
        'mediapipe',    # mediapipe
        'numpy',        # numpy
        'pandas',       # pandas
        'flet',         # flet
        'pyttsx3',      # pyttsx3
        'altair',       # altair
        'plotly',       # plotly
    ]
    
    failed_imports = []
    
    for dep in dependencies:
        try:
            __import__(dep)
        except ImportError:
            failed_imports.append(dep)
    
    if failed_imports:
        pytest.skip(f"Dependencies tidak terinstall: {', '.join(failed_imports)}")

def test_webcam_stream_basic():
    """
    Test dasar untuk WebcamStream tanpa mengakses kamera fisik
    """
    from src.capture.stream import WebcamStream
    
    # Test inisialisasi
    stream = WebcamStream(src=0, resolution=(320, 240))
    assert stream.src == 0
    assert stream.resolution == (320, 240)
    assert not stream.is_running
    assert stream.frame is None
    assert stream.current_fps == 0.0
    
    # Test status
    assert not stream.is_active()
    
    # Test stop tanpa start (tidak boleh error)
    stream.stop()  # Tidak boleh crash

if __name__ == "__main__":
    # Jalankan test jika file dieksekusi langsung
    pytest.main([__file__, "-v"])