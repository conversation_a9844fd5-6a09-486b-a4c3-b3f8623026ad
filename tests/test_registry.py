"""
Test untuk Plugin Registry
Testing discover, register, dan manage plugins

(c) 2025 Radhi<PERSON><PERSON>
"""

import pytest
from typing import Dict, Any
import numpy as np

from icikiwir.core.registry import PluginRegistry, register_plugin
from icikiwir.core.protocols import VisionPlugin, AnalyticsPlugin, InterventionPlugin


# Mock plugin classes untuk testing
class MockVisionPlugin:
    """Mock vision plugin untuk testing"""
    
    @property
    def name(self) -> str:
        return "mock_vision"
    
    @property
    def version(self) -> str:
        return "1.0.0"
    
    @property
    def description(self) -> str:
        return "Mock vision plugin untuk testing"
    
    def process_frame(self, frame: np.ndarray) -> Dict[str, Any]:
        return {
            "face_detected": True,
            "metrics": {"ear": 0.3, "head_yaw": 5.0},
            "confidence": 0.8,
            "annotations": None,
            "metadata": {"test": True}
        }
    
    def get_config_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "threshold": {"type": "number", "default": 0.5}
            }
        }
    
    def configure(self, config: Dict[str, Any]) -> None:
        self.threshold = config.get("threshold", 0.5)
    
    def cleanup(self) -> None:
        pass


class MockAnalyticsPlugin:
    """Mock analytics plugin untuk testing"""
    
    @property
    def name(self) -> str:
        return "mock_analytics"
    
    @property
    def version(self) -> str:
        return "1.0.0"
    
    @property
    def description(self) -> str:
        return "Mock analytics plugin untuk testing"
    
    def analyze_metrics(self, metrics: Dict[str, Any]) -> Dict[str, Any]:
        return {
            "focus_score": 0.7,
            "attention_level": "medium",
            "insights": ["Fokus cukup baik"],
            "recommendations": ["Pertahankan konsentrasi"],
            "metadata": {"test": True}
        }
    
    def get_config_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "sensitivity": {"type": "number", "default": 0.8}
            }
        }
    
    def configure(self, config: Dict[str, Any]) -> None:
        self.sensitivity = config.get("sensitivity", 0.8)
    
    def cleanup(self) -> None:
        pass


class MockInterventionPlugin:
    """Mock intervention plugin untuk testing"""
    
    @property
    def name(self) -> str:
        return "mock_intervention"
    
    @property
    def version(self) -> str:
        return "1.0.0"
    
    @property
    def description(self) -> str:
        return "Mock intervention plugin untuk testing"
    
    def trigger_intervention(self, context: Dict[str, Any]) -> bool:
        return context.get("focus_score", 1.0) < 0.5
    
    def is_available(self) -> bool:
        return True
    
    def get_config_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "enabled": {"type": "boolean", "default": True}
            }
        }
    
    def configure(self, config: Dict[str, Any]) -> None:
        self.enabled = config.get("enabled", True)
    
    def cleanup(self) -> None:
        pass


class TestPluginRegistry:
    """Test suite untuk PluginRegistry"""
    
    def setup_method(self):
        """Setup untuk setiap test method"""
        # Buat registry baru untuk setiap test
        self.registry = PluginRegistry()
        # Clear semua plugin yang mungkin sudah terdaftar
        self.registry._vision_plugins.clear()
        self.registry._analytics_plugins.clear()
        self.registry._intervention_plugins.clear()
        self.registry._instances.clear()
    
    def test_singleton_pattern(self):
        """Test bahwa PluginRegistry menggunakan singleton pattern"""
        registry1 = PluginRegistry()
        registry2 = PluginRegistry()
        assert registry1 is registry2
    
    def test_register_vision_plugin(self):
        """Test register vision plugin"""
        self.registry.register("test_vision", MockVisionPlugin, "vision")
        
        plugins = self.registry.list_plugins()
        assert "test_vision" in plugins["vision"]
        assert len(plugins["vision"]) == 1
    
    def test_register_analytics_plugin(self):
        """Test register analytics plugin"""
        self.registry.register("test_analytics", MockAnalyticsPlugin, "analytics")
        
        plugins = self.registry.list_plugins()
        assert "test_analytics" in plugins["analytics"]
        assert len(plugins["analytics"]) == 1
    
    def test_register_intervention_plugin(self):
        """Test register intervention plugin"""
        self.registry.register("test_intervention", MockInterventionPlugin, "intervention")
        
        plugins = self.registry.list_plugins()
        assert "test_intervention" in plugins["intervention"]
        assert len(plugins["intervention"]) == 1
    
    def test_auto_detect_plugin_category(self):
        """Test auto-detection kategori plugin"""
        # Test tanpa specify kategori
        self.registry.register("auto_vision", MockVisionPlugin)
        self.registry.register("auto_analytics", MockAnalyticsPlugin)
        self.registry.register("auto_intervention", MockInterventionPlugin)
        
        plugins = self.registry.list_plugins()
        assert "auto_vision" in plugins["vision"]
        assert "auto_analytics" in plugins["analytics"]
        assert "auto_intervention" in plugins["intervention"]
    
    def test_create_instance(self):
        """Test pembuatan instance plugin"""
        self.registry.register("test_vision", MockVisionPlugin, "vision")
        
        # Buat instance tanpa config
        instance = self.registry.create_instance("test_vision")
        assert instance.name == "mock_vision"
        assert instance.version == "1.0.0"
        
        # Buat instance dengan config
        config = {"threshold": 0.7}
        instance_with_config = self.registry.create_instance("test_vision", config)
        assert hasattr(instance_with_config, 'threshold')
        assert instance_with_config.threshold == 0.7
    
    def test_get_instance(self):
        """Test mendapatkan instance yang sudah dibuat"""
        self.registry.register("test_vision", MockVisionPlugin, "vision")
        
        # Buat instance
        original_instance = self.registry.create_instance("test_vision")
        
        # Get instance yang sama
        retrieved_instance = self.registry.get_instance("test_vision")
        assert original_instance is retrieved_instance
    
    def test_get_plugin_info(self):
        """Test mendapatkan informasi plugin"""
        self.registry.register("test_vision", MockVisionPlugin, "vision")
        
        info = self.registry.get_plugin_info("test_vision")
        assert info["name"] == "mock_vision"
        assert info["version"] == "1.0.0"
        assert info["category"] == "vision"
        assert info["class_name"] == "MockVisionPlugin"
    
    def test_get_plugins_iterator(self):
        """Test iterator untuk mendapatkan plugin berdasarkan kategori"""
        self.registry.register("vision1", MockVisionPlugin, "vision")
        self.registry.register("vision2", MockVisionPlugin, "vision")
        self.registry.register("analytics1", MockAnalyticsPlugin, "analytics")
        
        # Test vision plugins
        vision_plugins = list(self.registry.get_plugins("vision"))
        assert len(vision_plugins) == 2
        
        # Test analytics plugins
        analytics_plugins = list(self.registry.get_plugins("analytics"))
        assert len(analytics_plugins) == 1
        
        # Test intervention plugins (kosong)
        intervention_plugins = list(self.registry.get_plugins("intervention"))
        assert len(intervention_plugins) == 0
    
    def test_unregister_plugin(self):
        """Test unregister plugin"""
        self.registry.register("test_vision", MockVisionPlugin, "vision")
        
        # Pastikan plugin terdaftar
        plugins = self.registry.list_plugins()
        assert "test_vision" in plugins["vision"]
        
        # Unregister
        self.registry.unregister("test_vision")
        
        # Pastikan plugin sudah tidak terdaftar
        plugins = self.registry.list_plugins()
        assert "test_vision" not in plugins["vision"]
    
    def test_cleanup_all(self):
        """Test cleanup semua plugin instances"""
        self.registry.register("test_vision", MockVisionPlugin, "vision")
        self.registry.register("test_analytics", MockAnalyticsPlugin, "analytics")
        
        # Buat instances
        self.registry.create_instance("test_vision")
        self.registry.create_instance("test_analytics")
        
        # Pastikan instances ada
        assert len(self.registry._instances) == 2
        
        # Cleanup all
        self.registry.cleanup_all()
        
        # Pastikan instances sudah dibersihkan
        assert len(self.registry._instances) == 0
    
    def test_invalid_category(self):
        """Test error handling untuk kategori yang tidak valid"""
        with pytest.raises(ValueError, match="Kategori plugin tidak dikenal"):
            self.registry.register("test", MockVisionPlugin, "invalid_category")
    
    def test_plugin_not_found(self):
        """Test error handling untuk plugin yang tidak ditemukan"""
        with pytest.raises(ValueError, match="Plugin 'nonexistent' tidak ditemukan"):
            self.registry.create_instance("nonexistent")
        
        with pytest.raises(ValueError, match="Plugin 'nonexistent' tidak ditemukan"):
            self.registry.get_plugin_info("nonexistent")
    
    def test_instance_not_created(self):
        """Test error handling untuk instance yang belum dibuat"""
        self.registry.register("test_vision", MockVisionPlugin, "vision")
        
        with pytest.raises(ValueError, match="Instance plugin 'test_vision' belum dibuat"):
            self.registry.get_instance("test_vision")


class TestRegisterDecorator:
    """Test untuk decorator register_plugin"""
    
    def setup_method(self):
        """Setup untuk setiap test method"""
        # Clear registry
        registry = PluginRegistry()
        registry._vision_plugins.clear()
        registry._analytics_plugins.clear()
        registry._intervention_plugins.clear()
    
    def test_register_decorator(self):
        """Test decorator register_plugin"""
        
        @register_plugin("vision")
        class DecoratedVisionPlugin:
            @property
            def name(self) -> str:
                return "decorated_vision"
            
            @property
            def version(self) -> str:
                return "1.0.0"
            
            @property
            def description(self) -> str:
                return "Decorated vision plugin"
            
            def process_frame(self, frame: np.ndarray) -> Dict[str, Any]:
                return {"test": True}
            
            def get_config_schema(self) -> Dict[str, Any]:
                return {}
            
            def configure(self, config: Dict[str, Any]) -> None:
                pass
            
            def cleanup(self) -> None:
                pass
        
        # Pastikan plugin terdaftar otomatis
        registry = PluginRegistry()
        plugins = registry.list_plugins()
        assert "decoratedvisionplugin" in plugins["vision"]


if __name__ == "__main__":
    pytest.main([__file__])