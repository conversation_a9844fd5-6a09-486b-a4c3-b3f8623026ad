"""
Test untuk FocusLogger - Storage Layer ICikiwir
Menguji fungsi logging, storage, dan export data focus metrics

(c) 2025 Radhi<PERSON>a Guntoro Adhi
"""

import pytest
import asyncio
import tempfile
import os
import sqlite3
import csv
import time
from datetime import datetime, timedelta
from pathlib import Path
from unittest.mock import patch, MagicMock

# Import modules yang akan ditest
from src.storage.logger import FocusLogger, FocusMetrics


class TestFocusLogger:
    """Test suite untuk FocusLogger"""
    
    def setup_method(self):
        """Setup untuk setiap test method"""
        # Gunakan temporary directory untuk testing
        self.temp_dir = tempfile.mkdtemp()
        self.db_path = Path(self.temp_dir) / "test_focus.db"
        
        # Inisialisasi logger dengan path test
        self.logger = FocusLogger(
            db_path=self.db_path,
            flush_interval=1  # Short interval for testing
        )
    
    def teardown_method(self):
        """Cleanup setelah setiap test"""
        if hasattr(self.logger, 'cleanup'):
            self.logger.cleanup()
        
        # Hapus file test
        for file_path in [self.db_path, self.csv_path]:
            if os.path.exists(file_path):
                os.remove(file_path)
        
        os.rmdir(self.temp_dir)
    
    def test_focus_metrics_creation(self):
        """Test pembuatan FocusMetrics object"""
        current_time = time.time()
        metrics = FocusMetrics(
            timestamp=current_time,
            blink_rate=0.25,
            head_yaw_variance=0.15,
            focus_score=0.85,
            session_id="test_session_001",
            metadata={"source": "test"}
        )
        
        assert metrics.timestamp == current_time
        assert metrics.blink_rate == 0.25
        assert metrics.head_yaw_variance == 0.15
        assert metrics.focus_score == 0.85
        assert metrics.session_id == "test_session_001"
        assert metrics.metadata["source"] == "test"
    
    def test_logger_initialization(self):
        """Test inisialisasi FocusLogger"""
        assert self.logger.db_path == self.db_path
        assert self.logger.csv_fallback_path == self.csv_path
        assert self.logger.batch_size == 10
        assert self.logger.flush_interval == 30
        assert len(self.logger._batch) == 0
    
    def test_database_creation(self):
        """Test pembuatan database SQLite"""
        # Trigger database creation
        self.logger._ensure_db_connection()
        
        # Verify database file exists
        assert os.path.exists(self.db_path)
        
        # Verify table structure
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='focus_metrics'")
        table_exists = cursor.fetchone() is not None
        assert table_exists
        
        # Verify columns
        cursor.execute("PRAGMA table_info(focus_metrics)")
        columns = [row[1] for row in cursor.fetchall()]
        expected_columns = ['id', 'timestamp', 'focus_score', 'attention_level', 'session_id', 'metadata']
        
        for col in expected_columns:
            assert col in columns
        
        conn.close()
    
    @pytest.mark.asyncio
    async def test_log_metrics_single(self):
        """Test logging single metrics"""
        metrics = FocusMetrics(
            timestamp=datetime.now(),
            focus_score=0.75,
            attention_level="medium",
            session_id="test_session_002"
        )
        
        await self.logger.log_metrics(metrics)
        
        # Verify metrics added to batch
        assert len(self.logger._batch) == 1
        assert self.logger._batch[0] == metrics
    
    @pytest.mark.asyncio
    async def test_batch_processing(self):
        """Test batch processing dan auto-flush"""
        # Create multiple metrics to trigger batch flush
        metrics_list = []
        for i in range(12):  # Lebih dari batch_size (10)
            metrics = FocusMetrics(
                timestamp=datetime.now(),
                focus_score=0.5 + (i * 0.05),
                attention_level="medium",
                session_id=f"batch_test_{i}"
            )
            metrics_list.append(metrics)
            await self.logger.log_metrics(metrics)
        
        # Batch should have been flushed automatically
        # Remaining items should be less than total
        assert len(self.logger._batch) < 12
        
        # Force flush remaining
        await self.logger.flush()
        assert len(self.logger._batch) == 0
    
    @pytest.mark.asyncio
    async def test_database_storage(self):
        """Test penyimpanan ke database SQLite"""
        metrics = FocusMetrics(
            timestamp=datetime.now(),
            focus_score=0.90,
            attention_level="high",
            session_id="db_test_session",
            metadata={"test": "database_storage"}
        )
        
        await self.logger.log_metrics(metrics)
        await self.logger.flush()
        
        # Verify data in database
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("SELECT * FROM focus_metrics WHERE session_id = ?", ("db_test_session",))
        row = cursor.fetchone()
        
        assert row is not None
        assert row[2] == 0.90  # focus_score
        assert row[3] == "high"  # attention_level
        assert row[4] == "db_test_session"  # session_id
        
        conn.close()
    
    @pytest.mark.asyncio
    async def test_csv_fallback(self):
        """Test fallback ke CSV jika SQLite gagal"""
        # Mock database connection failure
        with patch.object(self.logger, '_ensure_db_connection', side_effect=Exception("DB Error")):
            metrics = FocusMetrics(
                timestamp=datetime.now(),
                focus_score=0.60,
                attention_level="medium",
                session_id="csv_fallback_test"
            )
            
            await self.logger.log_metrics(metrics)
            await self.logger.flush()
            
            # Verify CSV file created
            assert os.path.exists(self.csv_path)
            
            # Verify CSV content
            with open(self.csv_path, 'r') as f:
                reader = csv.DictReader(f)
                rows = list(reader)
                
                assert len(rows) == 1
                assert rows[0]['focus_score'] == '0.6'
                assert rows[0]['attention_level'] == 'medium'
                assert rows[0]['session_id'] == 'csv_fallback_test'
    
    def test_export_to_csv(self):
        """Test export data ke CSV"""
        # Insert test data directly to database
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Create table
        self.logger._ensure_db_connection()
        
        # Insert test data
        test_data = [
            (datetime.now().isoformat(), 0.85, "high", "export_test_1", "{}"),
            (datetime.now().isoformat(), 0.70, "medium", "export_test_2", "{}"),
            (datetime.now().isoformat(), 0.45, "low", "export_test_3", "{}")
        ]
        
        cursor.executemany(
            "INSERT INTO focus_metrics (timestamp, focus_score, attention_level, session_id, metadata) VALUES (?, ?, ?, ?, ?)",
            test_data
        )
        conn.commit()
        conn.close()
        
        # Test export
        export_path = os.path.join(self.temp_dir, "export_test.csv")
        success = self.logger.export_to_csv(export_path)
        
        assert success
        assert os.path.exists(export_path)
        
        # Verify exported content
        with open(export_path, 'r') as f:
            reader = csv.DictReader(f)
            rows = list(reader)
            
            assert len(rows) == 3
            assert rows[0]['session_id'] == 'export_test_1'
            assert rows[1]['focus_score'] == '0.7'
            assert rows[2]['attention_level'] == 'low'
    
    def test_export_with_date_filter(self):
        """Test export dengan filter tanggal"""
        # Setup database dengan data berbeda tanggal
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        self.logger._ensure_db_connection()
        
        today = datetime.now()
        yesterday = today - timedelta(days=1)
        
        test_data = [
            (today.isoformat(), 0.85, "high", "today_session", "{}"),
            (yesterday.isoformat(), 0.70, "medium", "yesterday_session", "{}")
        ]
        
        cursor.executemany(
            "INSERT INTO focus_metrics (timestamp, focus_score, attention_level, session_id, metadata) VALUES (?, ?, ?, ?, ?)",
            test_data
        )
        conn.commit()
        conn.close()
        
        # Export dengan filter hari ini
        export_path = os.path.join(self.temp_dir, "filtered_export.csv")
        success = self.logger.export_to_csv(
            export_path, 
            date=today.strftime("%Y-%m-%d")
        )
        
        assert success
        
        # Verify hanya data hari ini yang diekspor
        with open(export_path, 'r') as f:
            reader = csv.DictReader(f)
            rows = list(reader)
            
            assert len(rows) == 1
            assert rows[0]['session_id'] == 'today_session'
    
    def test_export_with_limit(self):
        """Test export dengan limit jumlah record"""
        # Setup database dengan banyak data
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        self.logger._ensure_db_connection()
        
        test_data = []
        for i in range(10):
            test_data.append((
                datetime.now().isoformat(),
                0.5 + (i * 0.05),
                "medium",
                f"limit_test_{i}",
                "{}"
            ))
        
        cursor.executemany(
            "INSERT INTO focus_metrics (timestamp, focus_score, attention_level, session_id, metadata) VALUES (?, ?, ?, ?, ?)",
            test_data
        )
        conn.commit()
        conn.close()
        
        # Export dengan limit 5
        export_path = os.path.join(self.temp_dir, "limited_export.csv")
        success = self.logger.export_to_csv(export_path, limit=5)
        
        assert success
        
        # Verify hanya 5 record yang diekspor
        with open(export_path, 'r') as f:
            reader = csv.DictReader(f)
            rows = list(reader)
            
            assert len(rows) == 5
    
    @pytest.mark.asyncio
    async def test_concurrent_logging(self):
        """Test concurrent logging operations"""
        async def log_batch(session_prefix, count):
            for i in range(count):
                metrics = FocusMetrics(
                    timestamp=datetime.now(),
                    focus_score=0.5 + (i * 0.1),
                    attention_level="medium",
                    session_id=f"{session_prefix}_{i}"
                )
                await self.logger.log_metrics(metrics)
        
        # Run concurrent logging tasks
        await asyncio.gather(
            log_batch("concurrent_1", 5),
            log_batch("concurrent_2", 5),
            log_batch("concurrent_3", 5)
        )
        
        await self.logger.flush()
        
        # Verify all data stored
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("SELECT COUNT(*) FROM focus_metrics WHERE session_id LIKE 'concurrent_%'")
        count = cursor.fetchone()[0]
        
        assert count == 15  # 3 batches * 5 records each
        
        conn.close()
    
    def test_cleanup(self):
        """Test cleanup resources"""
        # Add some data to batch
        metrics = FocusMetrics(
            timestamp=datetime.now(),
            focus_score=0.80,
            attention_level="high",
            session_id="cleanup_test"
        )
        
        asyncio.run(self.logger.log_metrics(metrics))
        assert len(self.logger._batch) == 1
        
        # Test cleanup
        self.logger.cleanup()
        
        # Verify batch cleared and connections closed
        assert len(self.logger._batch) == 0


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])