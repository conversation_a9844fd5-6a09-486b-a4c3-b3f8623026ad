"""
Unit tests untuk rule engine sistem feedback ICikiwir
Menguji logika evaluasi fokus dan feedback messages

(c) 2025 Radhi<PERSON>a Guntoro Adhi
"""

import pytest
import time
from unittest.mock import patch, MagicMock
from src.intervention.rules import FocusRuleEngine, evaluate_focus, reset_rule_engine


class TestFocusRuleEngine:
    """Test cases untuk FocusRuleEngine"""
    
    def setup_method(self):
        """Setup untuk setiap test method"""
        self.rule_engine = FocusRuleEngine()
        self.rule_engine.reset_state()
    
    def test_init_rule_engine(self):
        """Test inisialisasi rule engine"""
        engine = FocusRuleEngine()
        
        assert engine.previous_score is None
        assert engine.previous_category is None
        assert engine.last_feedback_time == 0
        assert len(engine.score_history) == 0
        assert engine.low_threshold == 30.0
        assert engine.medium_threshold == 60.0
        assert engine.high_threshold == 80.0
    
    def test_get_focus_category(self):
        """Test kategorisasi skor fokus"""
        # Test very_low category
        assert self.rule_engine._get_focus_category(10) == 'very_low'
        assert self.rule_engine._get_focus_category(29) == 'very_low'
        
        # Test low category
        assert self.rule_engine._get_focus_category(30) == 'low'
        assert self.rule_engine._get_focus_category(45) == 'low'
        assert self.rule_engine._get_focus_category(59) == 'low'
        
        # Test medium category
        assert self.rule_engine._get_focus_category(60) == 'medium'
        assert self.rule_engine._get_focus_category(70) == 'medium'
        assert self.rule_engine._get_focus_category(79) == 'medium'
        
        # Test high category
        assert self.rule_engine._get_focus_category(80) == 'high'
        assert self.rule_engine._get_focus_category(95) == 'high'
        assert self.rule_engine._get_focus_category(100) == 'high'
    
    def test_evaluate_focus_invalid_input(self):
        """Test evaluasi dengan input tidak valid"""
        # Test input negatif
        assert self.rule_engine.evaluate_focus(-10) is None
        
        # Test input > 100
        assert self.rule_engine.evaluate_focus(150) is None
        
        # Test input bukan angka
        assert self.rule_engine.evaluate_focus("invalid") is None
        assert self.rule_engine.evaluate_focus(None) is None
    
    def test_evaluate_focus_very_low_score(self):
        """Test evaluasi skor sangat rendah"""
        # Mock cooldown agar tidak menghalangi
        with patch.object(self.rule_engine, '_should_give_feedback', return_value=True):
            message = self.rule_engine.evaluate_focus(20)
            
            assert message is not None
            assert "Kayaknya kamu lelah, istirahat sebentar yuk." in message
            assert self.rule_engine.previous_score == 20
            assert self.rule_engine.previous_category == 'very_low'
    
    def test_evaluate_focus_low_score(self):
        """Test evaluasi skor rendah"""
        with patch.object(self.rule_engine, '_should_give_feedback', return_value=True):
            message = self.rule_engine.evaluate_focus(45)
            
            assert message is not None
            assert "Fokus menurun, coba tarik napas dalam." in message
            assert self.rule_engine.previous_score == 45
            assert self.rule_engine.previous_category == 'low'
    
    def test_evaluate_focus_recovery(self):
        """Test deteksi recovery (peningkatan fokus)"""
        # Set kondisi awal: skor rendah
        self.rule_engine.previous_score = 40
        self.rule_engine.previous_category = 'low'
        
        with patch.object(self.rule_engine, '_should_give_feedback', return_value=True):
            # Skor naik signifikan ke medium
            message = self.rule_engine.evaluate_focus(70)
            
            assert message is not None
            assert "Good job, fokusmu kembali!" in message
    
    def test_evaluate_focus_high_score(self):
        """Test evaluasi skor tinggi"""
        # Set kondisi sebelumnya bukan high
        self.rule_engine.previous_category = 'medium'
        
        with patch.object(self.rule_engine, '_should_give_feedback', return_value=True):
            message = self.rule_engine.evaluate_focus(85)
            
            assert message is not None
            assert "Luar biasa! Fokusmu sangat baik." in message
    
    def test_cooldown_mechanism(self):
        """Test mekanisme cooldown"""
        # Set waktu feedback terakhir baru saja
        self.rule_engine.last_feedback_time = time.time()
        
        # Evaluasi skor rendah, seharusnya tidak ada feedback karena cooldown
        message = self.rule_engine.evaluate_focus(20)
        assert message is None
    
    def test_detect_recovery_conditions(self):
        """Test berbagai kondisi recovery"""
        # Kondisi 1: dari very_low ke medium
        self.rule_engine.previous_score = 25
        self.rule_engine.previous_category = 'very_low'
        assert self.rule_engine._detect_recovery(65, 'medium') is True
        
        # Kondisi 2: dari low ke high
        self.rule_engine.previous_score = 45
        self.rule_engine.previous_category = 'low'
        assert self.rule_engine._detect_recovery(85, 'high') is True
        
        # Kondisi 3: peningkatan >= 20 poin dari < 60
        self.rule_engine.previous_score = 40
        self.rule_engine.previous_category = 'low'
        assert self.rule_engine._detect_recovery(65, 'medium') is True
        
        # Kondisi 4: tidak ada recovery (peningkatan kecil)
        self.rule_engine.previous_score = 55
        self.rule_engine.previous_category = 'low'
        assert self.rule_engine._detect_recovery(62, 'medium') is False
    
    def test_should_give_feedback_cooldown_types(self):
        """Test cooldown berbeda untuk jenis pesan berbeda"""
        current_time = time.time()
        
        # Set waktu feedback 10 detik yang lalu
        self.rule_engine.last_feedback_time = current_time - 10
        
        # Warning: cooldown normal (20 detik) - masih cooldown
        assert self.rule_engine._should_give_feedback('warning') is False
        
        # Recovery: cooldown lebih cepat (10 detik) - sudah boleh
        assert self.rule_engine._should_give_feedback('recovery') is True
        
        # Excellent: cooldown lebih lama (40 detik) - masih cooldown
        assert self.rule_engine._should_give_feedback('excellent') is False
    
    def test_score_history_management(self):
        """Test pengelolaan history skor"""
        # Tambahkan beberapa skor
        for i in range(12):  # Lebih dari batas 10
            self.rule_engine.evaluate_focus(50 + i)
        
        # History harus dibatasi maksimal 10
        assert len(self.rule_engine.score_history) == 10
        
        # Data terbaru harus ada
        assert self.rule_engine.score_history[-1]['score'] == 61
    
    def test_get_current_state(self):
        """Test mendapatkan state saat ini"""
        self.rule_engine.evaluate_focus(75)
        
        state = self.rule_engine.get_current_state()
        
        assert 'previous_score' in state
        assert 'previous_category' in state
        assert 'last_feedback_time' in state
        assert 'score_history_count' in state
        assert 'thresholds' in state
        
        assert state['previous_score'] == 75
        assert state['previous_category'] == 'medium'
        assert state['score_history_count'] == 1
    
    def test_reset_state(self):
        """Test reset state"""
        # Set beberapa data
        self.rule_engine.evaluate_focus(50)
        self.rule_engine.last_feedback_time = time.time()
        
        # Reset
        self.rule_engine.reset_state()
        
        assert self.rule_engine.previous_score is None
        assert self.rule_engine.previous_category is None
        assert self.rule_engine.last_feedback_time == 0
        assert len(self.rule_engine.score_history) == 0
    
    def test_update_thresholds(self):
        """Test update threshold"""
        # Update threshold
        self.rule_engine.update_thresholds(low=25, medium=55, high=75)
        
        assert self.rule_engine.low_threshold == 25
        assert self.rule_engine.medium_threshold == 55
        assert self.rule_engine.high_threshold == 75
        
        # Test kategorisasi dengan threshold baru
        assert self.rule_engine._get_focus_category(30) == 'low'  # Sebelumnya low, sekarang low
        assert self.rule_engine._get_focus_category(60) == 'medium'  # Sebelumnya medium, sekarang medium


class TestGlobalFunctions:
    """Test untuk fungsi-fungsi global"""
    
    def test_evaluate_focus_function(self):
        """Test fungsi global evaluate_focus"""
        reset_rule_engine()
        
        with patch('src.intervention.rules.rule_engine.evaluate_focus') as mock_evaluate:
            mock_evaluate.return_value = "Test message"
            
            result = evaluate_focus(50)
            
            mock_evaluate.assert_called_once_with(50)
            assert result == "Test message"
    
    def test_reset_rule_engine_function(self):
        """Test fungsi global reset_rule_engine"""
        with patch('src.intervention.rules.rule_engine.reset_state') as mock_reset:
            reset_rule_engine()
            mock_reset.assert_called_once()


class TestIntegrationScenarios:
    """Test skenario integrasi lengkap"""
    
    def setup_method(self):
        """Setup untuk setiap test method"""
        self.rule_engine = FocusRuleEngine()
        self.rule_engine.reset_state()
    
    def test_focus_decline_scenario(self):
        """Test skenario penurunan fokus"""
        # Mulai dengan skor tinggi
        with patch.object(self.rule_engine, '_should_give_feedback', return_value=True):
            # Skor tinggi - tidak ada pesan
            message1 = self.rule_engine.evaluate_focus(85)
            assert "Luar biasa!" in message1  # Pesan pujian untuk skor tinggi pertama kali
            
            # Skor turun ke medium - tidak ada pesan
            message2 = self.rule_engine.evaluate_focus(65)
            assert message2 is None
            
            # Skor turun ke low - ada peringatan
            message3 = self.rule_engine.evaluate_focus(45)
            assert message3 is not None
            assert "Fokus menurun" in message3
    
    def test_recovery_scenario(self):
        """Test skenario recovery fokus"""
        with patch.object(self.rule_engine, '_should_give_feedback', return_value=True):
            # Mulai dengan skor rendah
            message1 = self.rule_engine.evaluate_focus(25)
            assert message1 is not None
            assert "lelah" in message1
            
            # Recovery ke skor medium
            message2 = self.rule_engine.evaluate_focus(70)
            assert message2 is not None
            assert "Good job" in message2
    
    def test_stable_high_focus_scenario(self):
        """Test skenario fokus tinggi yang stabil"""
        with patch.object(self.rule_engine, '_should_give_feedback', return_value=True):
            # Skor tinggi pertama kali
            message1 = self.rule_engine.evaluate_focus(90)
            assert message1 is not None  # Pujian pertama kali
            
            # Skor tinggi lagi - tidak ada pesan (sudah di kategori high)
            message2 = self.rule_engine.evaluate_focus(88)
            assert message2 is None
            
            # Skor tinggi lagi - tidak ada pesan
            message3 = self.rule_engine.evaluate_focus(92)
            assert message3 is None


if __name__ == '__main__':
    pytest.main([__file__])