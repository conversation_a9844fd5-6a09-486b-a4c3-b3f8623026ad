"""
Test untuk Event Bus
Testing publish/subscribe event system

(c) 2025 Radhitya Guntoro Adhi
"""

import pytest
import asyncio
from typing import Dict, Any, List
from datetime import datetime

from icikiwir.core.event_bus import (
    EventBus, Event, VisionEvent, AnalyticsEvent, 
    InterventionEvent, SystemEvent, event_bus
)


class TestEvent:
    """Test suite untuk Event classes"""
    
    def test_base_event_creation(self):
        """Test pembuatan base event"""
        event = Event(
            event_type="test",
            source="test_source",
            data={"key": "value"}
        )
        
        assert event.event_type == "test"
        assert event.source == "test_source"
        assert event.data == {"key": "value"}
        assert isinstance(event.timestamp, datetime)
        assert event.event_id is not None
    
    def test_vision_event(self):
        """Test VisionEvent"""
        event = VisionEvent(
            source="face_tracker",
            data={"face_detected": True, "ear": 0.3}
        )
        
        assert event.event_type == "vision"
        assert event.source == "face_tracker"
        assert event.data["face_detected"] is True
    
    def test_analytics_event(self):
        """Test AnalyticsEvent"""
        event = AnalyticsEvent(
            source="focus_analyzer",
            data={"focus_score": 0.8, "attention_level": "high"}
        )
        
        assert event.event_type == "analytics"
        assert event.source == "focus_analyzer"
        assert event.data["focus_score"] == 0.8
    
    def test_intervention_event(self):
        """Test InterventionEvent"""
        event = InterventionEvent(
            source="tts_engine",
            data={"message": "Fokus menurun", "triggered": True}
        )
        
        assert event.event_type == "intervention"
        assert event.source == "tts_engine"
        assert event.data["message"] == "Fokus menurun"
    
    def test_system_event(self):
        """Test SystemEvent"""
        event = SystemEvent(
            source="core",
            data={"status": "started", "plugins_loaded": 5}
        )
        
        assert event.event_type == "system"
        assert event.source == "core"
        assert event.data["status"] == "started"


class TestEventBus:
    """Test suite untuk EventBus"""
    
    def setup_method(self):
        """Setup untuk setiap test method"""
        self.event_bus = EventBus()
        # Clear semua subscribers
        self.event_bus.clear_all_subscribers()
    
    @pytest.mark.asyncio
    async def test_start_stop_event_bus(self):
        """Test start dan stop event bus"""
        assert not self.event_bus._running
        
        await self.event_bus.start()
        assert self.event_bus._running
        
        await self.event_bus.stop()
        assert not self.event_bus._running
    
    @pytest.mark.asyncio
    async def test_publish_subscribe_with_handler(self):
        """Test publish/subscribe dengan handler function"""
        received_events = []
        
        def test_handler(event: Event):
            received_events.append(event)
        
        # Subscribe
        subscription_id = self.event_bus.subscribe("test", test_handler)
        assert subscription_id is not None
        
        # Start event bus
        await self.event_bus.start()
        
        # Publish event
        test_event = Event(event_type="test", source="test", data={"test": True})
        await self.event_bus.publish(test_event)
        
        # Tunggu sebentar untuk processing
        await asyncio.sleep(0.1)
        
        # Verify event diterima
        assert len(received_events) == 1
        assert received_events[0].event_type == "test"
        assert received_events[0].data["test"] is True
        
        await self.event_bus.stop()
    
    @pytest.mark.asyncio
    async def test_publish_subscribe_with_queue(self):
        """Test publish/subscribe dengan asyncio.Queue"""
        # Subscribe dengan queue
        queue = self.event_bus.subscribe_async("test")
        
        # Start event bus
        await self.event_bus.start()
        
        # Publish event
        test_event = Event(event_type="test", source="test", data={"queue_test": True})
        await self.event_bus.publish(test_event)
        
        # Receive event dari queue
        received_event = await asyncio.wait_for(queue.get(), timeout=1.0)
        
        assert received_event.event_type == "test"
        assert received_event.data["queue_test"] is True
        
        await self.event_bus.stop()
    
    @pytest.mark.asyncio
    async def test_multiple_subscribers(self):
        """Test multiple subscribers untuk event yang sama"""
        received_events_1 = []
        received_events_2 = []
        
        def handler_1(event: Event):
            received_events_1.append(event)
        
        def handler_2(event: Event):
            received_events_2.append(event)
        
        # Subscribe multiple handlers
        self.event_bus.subscribe("test", handler_1)
        self.event_bus.subscribe("test", handler_2)
        queue = self.event_bus.subscribe_async("test")
        
        await self.event_bus.start()
        
        # Publish event
        test_event = Event(event_type="test", source="test", data={"multi": True})
        await self.event_bus.publish(test_event)
        
        # Tunggu processing
        await asyncio.sleep(0.1)
        
        # Verify semua subscriber menerima event
        assert len(received_events_1) == 1
        assert len(received_events_2) == 1
        
        queue_event = await asyncio.wait_for(queue.get(), timeout=1.0)
        assert queue_event.data["multi"] is True
        
        await self.event_bus.stop()
    
    @pytest.mark.asyncio
    async def test_async_handler(self):
        """Test async event handler"""
        received_events = []
        
        async def async_handler(event: Event):
            await asyncio.sleep(0.01)  # Simulate async work
            received_events.append(event)
        
        self.event_bus.subscribe("test", async_handler)
        await self.event_bus.start()
        
        test_event = Event(event_type="test", source="test", data={"async": True})
        await self.event_bus.publish(test_event)
        
        # Tunggu async handler selesai
        await asyncio.sleep(0.1)
        
        assert len(received_events) == 1
        assert received_events[0].data["async"] is True
        
        await self.event_bus.stop()
    
    @pytest.mark.asyncio
    async def test_unsubscribe(self):
        """Test unsubscribe dari event"""
        received_events = []
        
        def test_handler(event: Event):
            received_events.append(event)
        
        # Subscribe
        self.event_bus.subscribe("test", test_handler)
        queue = self.event_bus.subscribe_async("test")
        
        await self.event_bus.start()
        
        # Publish event pertama
        event1 = Event(event_type="test", source="test", data={"first": True})
        await self.event_bus.publish(event1)
        await asyncio.sleep(0.1)
        
        # Unsubscribe handler
        success = self.event_bus.unsubscribe("test", handler=test_handler)
        assert success
        
        # Publish event kedua
        event2 = Event(event_type="test", source="test", data={"second": True})
        await self.event_bus.publish(event2)
        await asyncio.sleep(0.1)
        
        # Handler seharusnya hanya menerima event pertama
        assert len(received_events) == 1
        assert received_events[0].data["first"] is True
        
        # Queue masih harus menerima kedua event
        queue_event1 = await asyncio.wait_for(queue.get(), timeout=1.0)
        queue_event2 = await asyncio.wait_for(queue.get(), timeout=1.0)
        
        assert queue_event1.data["first"] is True
        assert queue_event2.data["second"] is True
        
        await self.event_bus.stop()
    
    @pytest.mark.asyncio
    async def test_wait_for_event(self):
        """Test wait_for_event functionality"""
        await self.event_bus.start()
        
        # Start waiting untuk event (dalam background task)
        async def publish_later():
            await asyncio.sleep(0.1)
            test_event = Event(event_type="delayed", source="test", data={"delayed": True})
            await self.event_bus.publish(test_event)
        
        # Start background task
        asyncio.create_task(publish_later())
        
        # Wait for event
        received_event = await self.event_bus.wait_for_event("delayed", timeout=1.0)
        
        assert received_event is not None
        assert received_event.event_type == "delayed"
        assert received_event.data["delayed"] is True
        
        await self.event_bus.stop()
    
    @pytest.mark.asyncio
    async def test_wait_for_event_timeout(self):
        """Test timeout pada wait_for_event"""
        await self.event_bus.start()
        
        # Wait for event yang tidak akan datang
        received_event = await self.event_bus.wait_for_event("nonexistent", timeout=0.1)
        
        assert received_event is None
        
        await self.event_bus.stop()
    
    def test_get_subscriber_count(self):
        """Test mendapatkan jumlah subscriber"""
        def handler1(event): pass
        def handler2(event): pass
        
        assert self.event_bus.get_subscriber_count("test") == 0
        
        self.event_bus.subscribe("test", handler1)
        assert self.event_bus.get_subscriber_count("test") == 1
        
        self.event_bus.subscribe("test", handler2)
        queue = self.event_bus.subscribe_async("test")
        assert self.event_bus.get_subscriber_count("test") == 3
    
    def test_get_event_types(self):
        """Test mendapatkan daftar event types"""
        assert len(self.event_bus.get_event_types()) == 0
        
        def handler(event): pass
        
        self.event_bus.subscribe("type1", handler)
        self.event_bus.subscribe("type2", handler)
        self.event_bus.subscribe_async("type3")
        
        event_types = self.event_bus.get_event_types()
        assert "type1" in event_types
        assert "type2" in event_types
        assert "type3" in event_types
        assert len(event_types) == 3
    
    def test_clear_all_subscribers(self):
        """Test clear semua subscribers"""
        def handler(event): pass
        
        self.event_bus.subscribe("test1", handler)
        self.event_bus.subscribe("test2", handler)
        self.event_bus.subscribe_async("test3")
        
        assert self.event_bus.get_subscriber_count("test1") > 0
        assert self.event_bus.get_subscriber_count("test2") > 0
        assert self.event_bus.get_subscriber_count("test3") > 0
        
        self.event_bus.clear_all_subscribers()
        
        assert self.event_bus.get_subscriber_count("test1") == 0
        assert self.event_bus.get_subscriber_count("test2") == 0
        assert self.event_bus.get_subscriber_count("test3") == 0
    
    def test_publish_sync(self):
        """Test publish_sync method"""
        received_events = []
        
        def handler(event: Event):
            received_events.append(event)
        
        self.event_bus.subscribe("sync_test", handler)
        
        # Publish sync
        test_event = Event(event_type="sync_test", source="test", data={"sync": True})
        self.event_bus.publish_sync(test_event)
        
        # Event harus langsung diproses (dalam event loop baru)
        # Karena ini sync test, kita tidak bisa langsung assert
        # Tapi method tidak boleh error
        assert True  # Jika sampai sini berarti tidak error
    
    @pytest.mark.asyncio
    async def test_queue_overflow_handling(self):
        """Test handling queue overflow"""
        # Buat event bus dengan queue size kecil
        small_bus = EventBus(max_queue_size=2)
        queue = small_bus.subscribe_async("overflow_test")
        
        await small_bus.start()
        
        # Publish lebih banyak event dari kapasitas queue
        for i in range(5):
            event = Event(event_type="overflow_test", source="test", data={"index": i})
            await small_bus.publish(event)
        
        # Queue seharusnya hanya menyimpan event terakhir (karena overflow handling)
        events_received = []
        try:
            while True:
                event = queue.get_nowait()
                events_received.append(event)
        except asyncio.QueueEmpty:
            pass
        
        # Seharusnya ada event (mungkin tidak semua karena overflow)
        assert len(events_received) <= 5
        
        await small_bus.stop()


class TestConvenienceFunctions:
    """Test untuk convenience functions"""
    
    def setup_method(self):
        """Setup untuk setiap test method"""
        event_bus.clear_all_subscribers()
    
    @pytest.mark.asyncio
    async def test_convenience_publish_functions(self):
        """Test convenience functions untuk publish events"""
        from icikiwir.core.event_bus import (
            publish_vision_event, publish_analytics_event,
            publish_intervention_event, publish_system_event
        )
        
        received_events = []
        
        def handler(event: Event):
            received_events.append(event)
        
        # Subscribe ke semua event types
        event_bus.subscribe("vision", handler)
        event_bus.subscribe("analytics", handler)
        event_bus.subscribe("intervention", handler)
        event_bus.subscribe("system", handler)
        
        await event_bus.start()
        
        # Test semua convenience functions
        await publish_vision_event("test", {"vision": True})
        await publish_analytics_event("test", {"analytics": True})
        await publish_intervention_event("test", {"intervention": True})
        await publish_system_event("test", {"system": True})
        
        await asyncio.sleep(0.1)
        
        assert len(received_events) == 4
        
        event_types = [event.event_type for event in received_events]
        assert "vision" in event_types
        assert "analytics" in event_types
        assert "intervention" in event_types
        assert "system" in event_types
        
        await event_bus.stop()
    
    def test_convenience_subscribe_functions(self):
        """Test convenience functions untuk subscribe events"""
        from icikiwir.core.event_bus import (
            subscribe_to_vision_events, subscribe_to_analytics_events,
            subscribe_to_intervention_events, subscribe_to_system_events
        )
        
        def handler(event): pass
        
        # Test semua convenience subscribe functions
        sub1 = subscribe_to_vision_events(handler)
        sub2 = subscribe_to_analytics_events(handler)
        sub3 = subscribe_to_intervention_events(handler)
        sub4 = subscribe_to_system_events(handler)
        
        assert sub1 is not None
        assert sub2 is not None
        assert sub3 is not None
        assert sub4 is not None
        
        # Verify subscribers terdaftar
        assert event_bus.get_subscriber_count("vision") == 1
        assert event_bus.get_subscriber_count("analytics") == 1
        assert event_bus.get_subscriber_count("intervention") == 1
        assert event_bus.get_subscriber_count("system") == 1


if __name__ == "__main__":
    pytest.main([__file__])