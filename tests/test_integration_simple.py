"""
Test Integrasi Sederhana untuk Logging & Dashboard ICikiwir
Test end-to-end untuk memastikan komponen utama berfungsi

(c) 2025 Radhitya Guntoro Adhi
"""

import pytest
import asyncio
import tempfile
import time
from pathlib import Path
from unittest.mock import Mock, patch

# Test basic imports
def test_imports():
    """Test import komponen utama"""
    try:
        from src.storage.logger import FocusLogger, FocusMetrics
        from src.analytics.logger_plugin import AnalyticsLogger
        from src.gui.components.dashboard import DashboardComponent
        from icikiwir.core.event_bus import FocusMetricEvent, publish_focus_metric_event
        print("✅ Semua import berhasil")
        return True
    except ImportError as e:
        print(f"❌ Import gagal: {e}")
        return False

def test_focus_metrics_creation():
    """Test pembuatan FocusMetrics"""
    from src.storage.logger import FocusMetrics
    
    current_time = time.time()
    metrics = FocusMetrics(
        timestamp=current_time,
        blink_rate=0.25,
        head_yaw_variance=0.15,
        focus_score=0.85,
        session_id="test_session",
        metadata={"source": "test"}
    )
    
    assert metrics.timestamp == current_time
    assert metrics.blink_rate == 0.25
    assert metrics.focus_score == 0.85
    assert metrics.session_id == "test_session"
    print("✅ FocusMetrics creation test passed")

def test_focus_logger_basic():
    """Test basic FocusLogger functionality"""
    from src.storage.logger import FocusLogger, FocusMetrics
    
    # Create temporary directory
    with tempfile.TemporaryDirectory() as temp_dir:
        db_path = Path(temp_dir) / "test.db"
        
        # Initialize logger
        logger = FocusLogger(db_path=db_path, flush_interval=1)
        
        # Test basic properties
        assert logger.db_path == db_path
        assert logger.flush_interval == 1
        assert len(logger._buffer) == 0
        
        print("✅ FocusLogger basic test passed")

@pytest.mark.asyncio
async def test_logger_async_operations():
    """Test async operations logger"""
    from src.storage.logger import FocusLogger, FocusMetrics
    
    with tempfile.TemporaryDirectory() as temp_dir:
        db_path = Path(temp_dir) / "test_async.db"
        logger = FocusLogger(db_path=db_path, flush_interval=1)
        
        # Test logging metrics
        metrics = FocusMetrics(
            timestamp=time.time(),
            blink_rate=0.3,
            head_yaw_variance=0.2,
            focus_score=0.7,
            session_id="async_test"
        )
        
        await logger.log_focus(metrics)
        assert len(logger._buffer) == 1
        
        # Test start/stop
        await logger.start()
        await asyncio.sleep(0.1)  # Let it run briefly
        await logger.stop()
        
        print("✅ Logger async operations test passed")

def test_event_bus_focus_metric():
    """Test FocusMetricEvent creation"""
    from icikiwir.core.event_bus import FocusMetricEvent
    
    event = FocusMetricEvent(
        source="test_calculator",
        data={
            "focus_score": 0.85,
            "blink_rate": 0.25,
            "session_id": "test_session"
        }
    )
    
    assert event.source == "test_calculator"
    assert event.data["focus_score"] == 0.85
    assert event.event_type == "focus_metric"
    
    print("✅ FocusMetricEvent test passed")

def test_analytics_plugin_basic():
    """Test basic AnalyticsLogger plugin"""
    from src.analytics.logger_plugin import AnalyticsLogger
    
    # Create plugin instance (no config parameter needed)
    plugin = AnalyticsLogger()
    
    # Test basic properties
    assert plugin.name == "analytics_logger"
    assert plugin.version == "1.0.0"
    assert plugin.description == "Plugin untuk logging metrik fokus ke database secara realtime"
    
    # Test initial state
    assert plugin._running == False
    assert plugin._current_session_id is None
    assert plugin._events_processed == 0
    
    print("✅ AnalyticsLogger basic test passed")

def test_dashboard_component_basic():
    """Test basic DashboardComponent"""
    from src.gui.components.dashboard import DashboardComponent
    
    # Mock page object
    mock_page = Mock()
    mock_page.update = Mock()
    mock_page.run_thread = Mock()
    
    # Mock logger instance (bukan class)
    with patch('src.gui.components.dashboard.logger') as mock_logger:
        mock_logger.query_metrics = Mock(return_value=[])
        mock_logger.get_stats = Mock(return_value={
            'buffer_size': 0,
            'running': False
        })
        
        dashboard = DashboardComponent(mock_page)
        
        # Test basic properties
        assert dashboard.page == mock_page
        assert dashboard.is_active == False
        assert dashboard.refresh_task is None
        assert len(dashboard.focus_data) == 0
        assert len(dashboard.blink_data) == 0
        
        print("✅ DashboardComponent basic test passed")

def test_cli_export_command():
    """Test CLI export command structure"""
    try:
        from scripts.icikiwir_cli import app
        
        # Check if export-csv command exists
        commands = [cmd.name for cmd in app.commands.values()]
        assert "export-csv" in commands
        
        print("✅ CLI export command test passed")
    except Exception as e:
        print(f"⚠️ CLI test skipped: {e}")

@pytest.mark.asyncio
async def test_end_to_end_flow():
    """Test end-to-end flow sederhana"""
    from src.storage.logger import FocusLogger, FocusMetrics
    from icikiwir.core.event_bus import FocusMetricEvent
    
    print("🔄 Testing end-to-end flow...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        db_path = Path(temp_dir) / "e2e_test.db"
        
        # 1. Create logger
        logger = FocusLogger(db_path=db_path, flush_interval=1)
        await logger.start()
        
        # 2. Create and log metrics
        metrics = FocusMetrics(
            timestamp=time.time(),
            blink_rate=0.28,
            head_yaw_variance=0.18,
            focus_score=0.82,
            session_id="e2e_test_session",
            metadata={"test": "end_to_end"}
        )
        
        await logger.log_focus(metrics)
        
        # 3. Create event
        event = FocusMetricEvent(
            source="e2e_test",
            data={
                "focus_score": metrics.focus_score,
                "session_id": metrics.session_id
            }
        )
        
        # 4. Verify event structure
        assert event.source == "e2e_test"
        assert event.data["focus_score"] == 0.82
        
        # 5. Flush and stop logger
        await logger.stop()
        
        # 6. Verify database file created
        assert db_path.exists()
        
        print("✅ End-to-end flow test passed")

def test_system_integration():
    """Test integrasi sistem secara keseluruhan"""
    print("🔧 Testing system integration...")
    
    # Test semua komponen dapat diimport dan diinisialisasi
    components_tested = []
    
    try:
        from src.storage.logger import FocusLogger
        components_tested.append("FocusLogger")
    except Exception as e:
        print(f"❌ FocusLogger import failed: {e}")
    
    try:
        from src.analytics.logger_plugin import AnalyticsLogger
        components_tested.append("AnalyticsLogger")
    except Exception as e:
        print(f"❌ AnalyticsLogger import failed: {e}")
    
    try:
        from src.gui.components.dashboard import DashboardComponent
        components_tested.append("DashboardComponent")
    except Exception as e:
        print(f"❌ DashboardComponent import failed: {e}")
    
    try:
        from icikiwir.core.event_bus import FocusMetricEvent
        components_tested.append("FocusMetricEvent")
    except Exception as e:
        print(f"❌ FocusMetricEvent import failed: {e}")
    
    print(f"✅ Components successfully imported: {', '.join(components_tested)}")
    
    # Minimal requirement: at least 3 components should work
    assert len(components_tested) >= 3, f"Only {len(components_tested)} components working"
    
    print("✅ System integration test passed")

if __name__ == "__main__":
    # Run tests manually
    print("🚀 Running ICikiwir Integration Tests")
    print("=" * 50)
    
    # Basic tests
    test_imports()
    test_focus_metrics_creation()
    test_focus_logger_basic()
    test_event_bus_focus_metric()
    test_analytics_plugin_basic()
    test_dashboard_component_basic()
    test_cli_export_command()
    test_system_integration()
    
    # Async tests
    print("\n🔄 Running async tests...")
    asyncio.run(test_logger_async_operations())
    asyncio.run(test_end_to_end_flow())
    
    print("\n✅ All integration tests completed!")