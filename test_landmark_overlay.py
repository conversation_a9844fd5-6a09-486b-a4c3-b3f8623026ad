#!/usr/bin/env python3
"""
Test script untuk memverifikasi overlay landmark wajah
Mengambil beberapa frame dan menyimpan sebagai gambar untuk verifikasi
"""

import cv2
import time
import sys
import os

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from capture.stream import WebcamStream
from vision.face_tracker import FaceTracker

def main():
    """Test overlay landmark wajah"""
    print("🎥 Testing Face Landmark Overlay...")
    print("Mengambil 5 frame dengan overlay landmark...")
    
    # Initialize components
    webcam = WebcamStream()
    face_tracker = FaceTracker()
    
    try:
        # Start webcam
        if not webcam.start():
            print("❌ Gagal memulai webcam")
            return
        
        print("✅ Webcam started")
        
        # Wait a bit for camera to stabilize
        time.sleep(2)
        
        frame_count = 0
        saved_count = 0
        
        while saved_count < 5:
            # Get frame
            frame = webcam.get_frame()
            if frame is None:
                continue
            
            frame_count += 1
            
            # Skip first few frames
            if frame_count < 10:
                continue
            
            # Process frame
            results = face_tracker.process_frame(frame)
            
            # Get annotated frame
            annotated_frame = results.get('frame_with_annotations', frame)
            
            # Save frame every 30 frames
            if frame_count % 30 == 0:
                filename = f"test_landmark_{saved_count + 1}.jpg"
                cv2.imwrite(filename, annotated_frame)
                print(f"✅ Saved {filename}")
                saved_count += 1
                
                # Show info about detection
                if 'dominant_expression' in results:
                    emotion, intensity = results['dominant_expression']
                    print(f"   - Expression: {emotion} ({intensity:.2f})")
                if 'ear_average' in results:
                    print(f"   - EAR: {results['ear_average']:.3f}")
            
            # Small delay
            time.sleep(0.033)  # ~30 FPS
            
    except KeyboardInterrupt:
        print("\n⏹️ Interrupted by user")
    except Exception as e:
        print(f"\n❌ Error: {e}")
    finally:
        # Cleanup
        webcam.stop()
        print(f"\n✅ Test completed. Saved {saved_count} images.")
        print("Periksa file test_landmark_*.jpg untuk melihat overlay landmark")

if __name__ == "__main__":
    main()
