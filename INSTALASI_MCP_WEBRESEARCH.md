# Instalasi MCP WebResearch Server

**Dibuat oleh: <PERSON><PERSON><PERSON><PERSON>**

## <PERSON>kasan Instalasi

MCP WebResearch Server telah berhasil diinstal dan dikonfigurasi di sistem ini. Server ini menyediakan kemampuan penelitian web real-time untuk Claude dengan fitur pencarian Google, ekstraksi konten halaman web, dan pengambilan screenshot.

## Langkah-langkah yang Telah Dilakukan

### 1. Persiapan Sistem
- ✅ Instalasi Git: `sudo apt install git`
- ✅ Instalasi Node.js v22.16.0 dari NodeSource repository
- ✅ Instalasi pnpm sebagai package manager: `sudo npm install -g pnpm`

### 2. Instalasi Server
- ✅ Membuat direktori: `mkdir -p mcp-servers`
- ✅ Clone repository: `git clone https://github.com/pashpashpash/mcp-webresearch.git`
- ✅ Instalasi dependencies: `pnpm install`
- ✅ Instalasi type definitions: `pnpm add -D @types/node`
- ✅ Build proyek: `pnpm build`

### 3. Konfigurasi
- ✅ Membuat file `mcp_settings.json` dengan konfigurasi server
- ✅ Menggunakan nama server: "github.com/pashpashpash/mcp-webresearch"
- ✅ Path executable: `./mcp-servers/mcp-webresearch/dist/index.js`

## Struktur File

```
/home/<USER>/project/icikiwir/
├── mcp_settings.json                    # Konfigurasi MCP server
└── mcp-servers/
    └── mcp-webresearch/
        ├── dist/
        │   ├── index.js                 # File executable utama
        │   ├── index.d.ts              # Type definitions
        │   └── index.js.map            # Source map
        ├── package.json                 # Dependencies dan scripts
        ├── README.md                    # Dokumentasi
        └── index.ts                     # Source code TypeScript
```

## Konfigurasi MCP Settings

File `mcp_settings.json`:
```json
{
  "mcpServers": {
    "github.com/pashpashpash/mcp-webresearch": {
      "command": "node",
      "args": ["./mcp-servers/mcp-webresearch/dist/index.js"],
      "env": {
        "NODE_ENV": "production"
      }
    }
  }
}
```

## Tools yang Tersedia

Server menyediakan 3 tools utama:

### 1. search_google
- **Deskripsi**: Melakukan pencarian Google
- **Parameter**: 
  - `query` (string, required): Query pencarian

### 2. visit_page
- **Deskripsi**: Mengunjungi halaman web dan mengekstrak konten
- **Parameter**:
  - `url` (string, required): URL yang akan dikunjungi
  - `takeScreenshot` (boolean, optional): Apakah mengambil screenshot

### 3. take_screenshot
- **Deskripsi**: Mengambil screenshot halaman saat ini
- **Parameter**: Tidak ada parameter yang diperlukan

## Verifikasi Instalasi

Server telah diverifikasi dapat berjalan dengan sukses:

1. **Test Tools List**: ✅ Berhasil menampilkan daftar tools
2. **Server Startup**: ✅ Server dapat dijalankan tanpa error
3. **Configuration**: ✅ File konfigurasi dibuat dengan benar

## Cara Penggunaan

### Menjalankan Server Standalone
```bash
cd mcp-servers/mcp-webresearch
node dist/index.js
```

### Menggunakan dengan Claude Desktop
1. Salin konfigurasi dari `mcp_settings.json` ke file konfigurasi Claude Desktop
2. Restart Claude Desktop
3. Server akan otomatis terhubung dan tools akan tersedia

## Dependencies Utama

- **@modelcontextprotocol/sdk**: SDK untuk MCP
- **playwright**: Browser automation untuk screenshot dan web scraping
- **turndown**: Konversi HTML ke Markdown
- **@types/node**: Type definitions untuk Node.js

## Troubleshooting

### Error TypeScript
- **Solusi**: Instalasi `@types/node` dengan `pnpm add -D @types/node`

### Error Path
- **Solusi**: Gunakan relative path `./mcp-servers/mcp-webresearch/dist/index.js`

### Error Screenshot Cleanup
- **Normal**: Error cleanup screenshot directory saat shutdown adalah normal

## Status Instalasi

🟢 **BERHASIL** - MCP WebResearch Server telah berhasil diinstal dan dikonfigurasi dengan lengkap.

---
**Catatan**: Server ini siap digunakan dengan Claude Desktop atau sistem MCP lainnya yang kompatibel.