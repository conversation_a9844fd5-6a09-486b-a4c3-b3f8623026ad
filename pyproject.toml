[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poetry]
name = "icikiwir"
version = "0.1.0"
description = "Cognitive-Aware Study Companion - Sistem monitoring fokus dan kesehatan mental saat belajar"
authors = ["<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>"]
license = "MIT"
readme = "README.md"
homepage = "https://github.com/radhityaguntoro/icikiwir"
repository = "https://github.com/radhityaguntoro/icikiwir"
documentation = "https://github.com/radhityaguntoro/icikiwir/docs"
keywords = ["cognitive", "study", "focus", "monitoring", "health", "ai", "computer-vision"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Education",
    "Intended Audience :: End Users/Desktop",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Education",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Multimedia :: Video :: Capture",
]

[tool.poetry.dependencies]
python = "^3.10"
flet = "^0.19.0"
opencv-python = "^4.8.0"
mediapipe = "^0.10.0"
onnxruntime = "^1.16.0"
numpy = "^1.24.0"
pandas = "^2.0.0"
altair = "^5.0.0"
plotly = "^5.17.0"
pyttsx3 = "^2.90"
sqlite-utils = "^3.35.0"
pydantic = "^2.0.0"
loguru = "^0.7.0"
python-dotenv = "^1.0.0"
schedule = "^1.2.0"
pillow = "^10.0.0"
typer = "^0.9.0"
rich = "^13.0.0"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.0"
pytest-cov = "^4.1.0"
pytest-asyncio = "^0.21.0"
black = "^23.0.0"
isort = "^5.12.0"
flake8 = "^6.0.0"
mypy = "^1.5.0"
pre-commit = "^3.4.0"

[tool.poetry.scripts]
icikiwir = "src.__main__:main"

[tool.poetry.plugins."icikiwir.plugins"]
# Placeholder contoh plugin internal
vision_dummy = "src.vision.face_tracker:FaceTracker"

[tool.black]
line-length = 100
target-version = ['py310']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
line_length = 100
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/venv/*",
    "*/env/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]