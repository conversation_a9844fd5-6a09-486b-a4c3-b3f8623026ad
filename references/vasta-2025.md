# <PERSON><PERSON><PERSON> et al. 2025 — Webcam-Based Cognitive Load Estimation  
(c) <PERSON><PERSON>an oleh Radhitya Guntoro Adhi  

## <PERSON><PERSON><PERSON> seberapa akurat metrik sederhana webcam (blink rate, head-pose variance) untuk memprediksi Mental Workload (NASA-TLX) saat tugas pemecahan masalah.

## Metodologi  
- N = 48 mahasiswa, 3 tingkat kesulitan tugas n-back.  
- Kamera laptop 30 fps, OpenFace untuk gaze & blink, POSIT untuk head-pose.  
- Random-forest regresi → skor TLX kontinu.  

## <PERSON><PERSON><PERSON>  
| Variabel | r terhadap TLX | p |  
|----------|---------------|---|  
| Blink rate | 0.64 | <.001 |  
| Head-pose yaw SD | 0.57 | <.01 |  
| Kombinasi (RF) | **0.71** | <.001 |  

Latency inferensi offline ≈ 22 ms per frame (RTX2060).  

## Keterbatasan  
- Setting lab cahaya stabil.  
- Tidak menguji ekspresi emosional.  

## Implikasi ke Proyek  
Blink rate + head-pose sudah memadai untuk indikator beban kognitif MVP tanpa GPU berat. Threshold adaptif bisa dibangun dari baseline 5 menit pertama sesi.