# <PERSON> et al. 2025 — Vision Transformer for Micro-Expression Recognition  
(c) <PERSON><PERSON><PERSON> o<PERSON>h <PERSON>a Guntoro Adhi  

## <PERSON>juan  
<PERSON>katkan akurasi deteksi mikro-ekspresi spontan (<0.5 s) dengan arsitektur Vision Transformer (ViT) yang di-pretrain pada data ekspresi makro.

## Metodologi  
- Dataset: CASME II & SAMM (total 460 klip).  
- Pre-training ViT pada 400K frame ekspresi reguler, lalu fine-tune.  
- Teknik Frame Magnification (Eulerian).  
- Evaluasi LOSOCV; banding CNN (ResNet-50).  

## <PERSON><PERSON><PERSON>  
| Model | Akurasi (%) | F1 |  
|-------|-------------|----|  
| ResNet-50 | 83.7 | 0.81 |  
| ViT-base | 90.8 | 0.88 |  
| ViT-base + Mag | **92.3** | **0.90** |  

Latency inference 18 ms @ RTX2060; dapat turun ke 38 ms CPU i7.

## Keterbatasan  
- Frame magnification sensitif noise cahaya.  
- Sample size relatif kecil; cross-culture belum diuji.  

## Implikasi ke Proyek  
Model ViT-base pratrain bisa diprune menjadi lightweight (≈40 MB) tanpa hilang banyak akurasi, cocok untuk Focus Monitor—khususnya mendeteksi subtle frown ketika beban kognitif naik.