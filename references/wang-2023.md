# <PERSON> et al. 2023 — Real-Time Smoking Gesture Recognition  
(c) <PERSON><PERSON><PERSON> o<PERSON>h <PERSON>dhi<PERSON>a Guntoro Adhi  

## <PERSON>juan  
Mendeteksi perilaku merokok secara real-time menggunakan kamera laptop/ CCTV untuk aplikasi kesehatan publik dan self-tracking.

## Metodologi  
- Dataset internal: 1 200 klip (merokok vs. non-merokok) dari 32 partisipan.  
- Arsitektur: Two-Stream CNN (RGB + Optical Flow) + Temporal Segment Network.  
- Augmentasi: random crop, brightness jitter.  
- Evaluasi: AP (Average Precision), latency pada GPU & CPU.

## <PERSON><PERSON><PERSON>  
| Model | AP (%) | Latency (ms) RTX2060 | CPU i7 |  
|-------|-------|----------------------|--------|  
| RGB-only | 81.2 | 14 | 42 |  
| Optical Flow only | 76.7 | 16 | 48 |  
| Two-Stream fusion | **88.4** | 22 | 58 |  

False positive ≈ 0.18/menit pada skenario kantor.

## Keterbatasan  
- <PERSON>ias kultur (gaya merokok).  
- Sudut kamera fixed; mobile belum diuji.

## Implikasi ke Proyek  
Two-Stream CNN dapat dipangkas channel-nya (MobileNet backbone) untuk modul **Distraction/Tic Detector** fokus merokok. Ambang confidence bisa dinaikkan untuk meminimalkan false alert saat gestur minum.