# <PERSON><PERSON><PERSON> et al. 2024 — Real-Time Tic Detection in Tourette Patients  
(c) <PERSON><PERSON><PERSON> oleh Radhitya Guntoro Adhi  

## Tujuan  
Mengembangkan sistem CV yang mampu mendeteksi motoric tic kompleks secara real-time untuk membantu terapi biofeedback Tourette.

## Metodologi  
- Peserta: 15 pasien Tourette, 2 × 30 menit sesi rekaman harian.  
- Kamera HD 60 fps, multi-angle.  
- Model: Temporal CNN + Optical Flow; window 0.5 s.  
- Ground-truth: ahli neurologi menandai onset tic.  

## Hasil Kunci  
| Metric | Value |  
|--------|-------|  
| F1-score | **0.86** |  
| Latency | 28 ms (GTX1650) |  
| False Pos | 0.12/min |  

## Keterbatasan  
- Ukuran sample kecil; generalisasi gerakan non-Tourette belum diuji.  
- Penerangan studio; noise rumah bisa beda.  

## Implikasi ke Proyek  
Arsitektur Temporal CNN ringan dapat diadaptasi untuk modul **Distraction/Tic Detector**, khususnya karena latency rendah & bisa jalan di GPU entry-level atau CPU modern dengan optimasi.